{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
LOCALIZATION SELECTOR COMPONENT
----------------------------------------------------------------------------------------------------------------------

This component renders a selector for a localization component (either country or locale).

********************************************
Supported variables
********************************************

* type: can be either "country" or "locale" (required)
* show_country_name: for type "country" if set to true the country/currency name are shown
* show_country_flag: for type "country" if set to true the country flags are shown
* popover_horizontal_position: an optional vertical position
* popover_vertical_position: an optional horizontal position (start if none is passed)
{%- endcomment -%}

{%- capture localization_form_id -%}localization-form-{{ type }}-{{ popover_vertical_position }}-{{ section.id }}{%- endcapture -%}
{%- capture localization_popover_id -%}popover-localization-form-{{ type }}-{{ popover_vertical_position }}-{{ section.id }}{%- endcapture -%}
{%- assign popover_vertical_position = popover_vertical_position | default: 'start' -%}
{%- assign popover_horizontal_position = popover_horizontal_position | default: 'end' -%}

{%- assign allowed_countries = 'US,GB,AU,DE,FR,ES' | split: ',' -%} 

{%- case type -%}
  {%- when 'country' -%}
    {%- if show_country_flag -%}
      <link rel="stylesheet" href="{{ 'country-flags.css' | asset_url }}" media="print" onload="this.media='all'; this.onload = null">
    {%- endif -%}

    <div class="relative">
      <button type="button" class="text-with-icon gap-2.5 group" aria-controls="{{ localization_popover_id }}" aria-expanded="false">
        <div class="h-stack gap-2" style="padding-left: 20px;">
          {%- if show_country_flag -%}
            <span class="country-selector country-flags country-flags--{{ localization.country.iso_code }}"></span>
          {%- endif -%}

          <span class="bold text-sm">
            {%- if show_country_name -%}
              {{ localization.country.name }} ({{ localization.country.currency.iso_code }} {% if localization.country.currency.symbol %}{{ localization.country.currency.symbol }}){%- endif -%}
            {%- else -%}
              {{ localization.country.currency.iso_code }} {% if localization.country.currency.symbol %}{{ localization.country.currency.symbol }}{%- endif -%}
            {%- endif -%}
          </span>
        </div>

        {%- render 'icon' with 'chevron-bottom' -%}
      </button>

      <x-popover id="{{ localization_popover_id }}" initial-focus="[aria-selected='true']" anchor-horizontal="{{ popover_horizontal_position }}" anchor-vertical="{{ popover_vertical_position }}" class="popover">
        <p class="h5" slot="title">{{ 'general.localization.country' | t }}</p>

        {%- form 'localization', id: localization_form_id -%}
          <x-listbox class="popover-listbox popover-listbox--sm" role="listbox">
            {%- for country in localization.available_countries -%}
              {%- if allowed_countries contains country.iso_code -%}
                <button type="submit" class="popover-listbox__option" name="country_code" role="option" value="{{ country.iso_code }}" {% if country.iso_code == localization.country.iso_code %}aria-selected="true"{% endif %}>
                  <span class="country-flags country-flags--{{ country.iso_code }}"></span>
                  <span>{{- country.name }} ({{ country.currency.iso_code }} {% if country.currency.symbol %}{{ country.currency.symbol }}{%- endif -%})</span>
                </button>
              {%- endif -%}
            {%- endfor -%}
          </x-listbox>
        {%- endform -%}
      </x-popover>
    </div>

{%- when 'locale' -%}
    <div class="relative">
      <button type="button" class="text-with-icon gap-2.5 group" aria-controls="{{ localization_popover_id }}" aria-expanded="false">
        <span class="bold text-sm">{{- localization.language.endonym_name | capitalize -}}</span>
        {%- render 'icon' with 'chevron-bottom' -%}
      </button>

      <x-popover id="{{ localization_popover_id }}" initial-focus="[aria-selected='true']" anchor-horizontal="{{ popover_horizontal_position }}" anchor-vertical="{{ popover_vertical_position }}" class="popover">
        <p class="h5" slot="title">{{ 'general.localization.language' | t }}</p>

        {%- form 'localization', id: localization_form_id -%}
          <x-listbox class="popover-listbox popover-listbox--sm" role="listbox">
            {%- for language in localization.available_languages -%}
              <button type="button" class="popover-listbox__option" name="locale_code" role="option" value="{{ language.iso_code }}" {% if language.iso_code == localization.language.iso_code %}aria-selected="true"{% endif %} onclick="changeLanguage('{{ language.iso_code }}')">
                {{- language.endonym_name | capitalize -}}
              </button>
            {%- endfor -%}
          </x-listbox>
        {%- endform -%}
      </x-popover>
    </div>
    
    <!-- Add redirect script to change URL path to the correct language structure -->
<script>
  function changeLanguage(selectedLanguage) {
    const currentPath = window.location.pathname;
    const baseUrl = window.location.origin;

    // If the selected language is English, remove the language prefix
    if (selectedLanguage === "en") {
      // Remove any existing language prefix
      const newPath = currentPath.replace(/^\/[a-z]{2}(-[a-z]{2})?(\/|$)/, "/");
      window.location.href = baseUrl + newPath;
      return;
    }

    // Construct the new language prefix (e.g., /fr-fr)
    const languagePrefix = `/${selectedLanguage}-${selectedLanguage}`;

    // Clean the path: Remove any existing language prefixes (e.g., /en-es, /fr-fr, etc.)
    const cleanedPath = currentPath.replace(/\/[a-z]{2}(-[a-z]{2})?(\/|$)/g, "/");

    // Add the new language prefix to the cleaned path
    const newPath = languagePrefix + cleanedPath;

    // Redirect to the new URL
    window.location.href = baseUrl + newPath;
  }
</script>




{%- endcase -%}
