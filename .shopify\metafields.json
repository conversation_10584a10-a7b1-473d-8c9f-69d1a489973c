{"article": [], "blog": [], "collection": [], "company": [], "company_location": [], "location": [], "market": [], "order": [], "page": [], "product": [{"key": "target-gender", "namespace": "shopify", "name": "Target gender", "description": "Identifies the intended gender for a product, such as female, male or unisex", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "closure-type", "namespace": "shopify", "name": "Closure type", "description": "Defines the method used to fasten or secure a product, such as zipper or buckles", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "toe-style", "namespace": "shopify", "name": "Toe style", "description": "Defines the shape or design of a shoe's toe, such as open or pointed", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "shoe-features", "namespace": "shopify", "name": "Shoe features", "description": "Identifies special characteristics of shoes, such as waterproof or lightweight", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "sneaker-style", "namespace": "shopify", "name": "Sneaker style", "description": "Identifies the design or type of sneakers, such as athletic or high-top", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "color-pattern", "namespace": "shopify", "name": "Color", "description": "Defines the primary color or pattern, such as blue or striped", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "freeshipping", "namespace": "custom", "name": "Shipping Details", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "likers", "namespace": "custom", "name": "Likers", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "spring_sale", "namespace": "custom", "name": "Promo Details", "description": "", "type": {"name": "rich_text_field", "category": "TEXT"}}, {"key": "recommended_text", "namespace": "custom", "name": "recommended-text", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "p_details_1", "namespace": "custom", "name": "p-details-1", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "p_details_2", "namespace": "custom", "name": "p-details-2", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "p_details_3", "namespace": "custom", "name": "p-details-3", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "p_details_4", "namespace": "custom", "name": "p-details-4", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "p_details_5", "namespace": "custom", "name": "p-details-5", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "p_detail_1_icon", "namespace": "custom", "name": "p-detail-1-ico", "description": "", "type": {"name": "url", "category": "URL"}}, {"key": "p_detail_2_icon", "namespace": "custom", "name": "p-detail-2-ico", "description": "", "type": {"name": "url", "category": "URL"}}, {"key": "p_detail_3_icon", "namespace": "custom", "name": "p-detail-3-ico", "description": "", "type": {"name": "url", "category": "URL"}}, {"key": "review_count", "namespace": "custom", "name": "Review Count", "description": "", "type": {"name": "single_line_text_field", "category": "TEXT"}}, {"key": "operating-system", "namespace": "shopify", "name": "Operating system", "description": "Defines the system software a device uses, such as iOS or ChromeOS", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "license-type", "namespace": "shopify", "name": "License type", "description": "Specifies the type of software license, such as open-source or one-time purchase", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "user-type", "namespace": "shopify", "name": "User type", "description": "Identifies the intended user of a software product, such as individual or small business", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "customer-support-channels", "namespace": "shopify", "name": "Customer support channels", "description": "Specifies the ways customers can get help, such as phone support or video tutorials", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "software-features", "namespace": "shopify", "name": "Software features", "description": "Identifies the key functionalities of a software product, such as privacy protection or auto backup", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "hardware-material", "namespace": "shopify", "name": "Hardware material", "description": "Specifies the composition of hardware items, such as metal or stainless steel", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}, {"key": "travel-pillow-shape", "namespace": "shopify", "name": "Travel pillow shape", "description": "Describes the physical contour of travel pillows, such as J-shaped ", "type": {"name": "list.metaobject_reference", "category": "REFERENCE"}}], "variant": [], "shop": []}