{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant iRseFowuC4ACffmqw" data-instant-id="RseFowuC4ACffmqw" data-instant-version="3.0.3" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-RseFowuC4ACffmqw.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="iPD3cKpQeytQBVsfF">
    {%- liquid
      assign loading = 'eager'
      assign fetchpriority = 'auto'
      if section.location == 'footer'
        assign loading = 'lazy'
      elsif section.location == 'header'
        assign fetchpriority = 'high'
      elsif section.location == 'template'
        if section.index == 1
          assign fetchpriority = 'high'
        elsif section.index > 2
          assign loading = 'lazy'
        endif
      endif
    -%}
    <div class="iaGtDEcoGGlqZeE5y" data-instant-type="container" id="iaGtDEcoGGlqZeE5y">
      <div class="iJLjBkNtMZzhBGlIi" data-instant-type="container">
        <div data-instant-type="text" class="instant-rich-text ikSsRT4hGO1AVVO2z">
          <div>{{ section.settings.text_kSsRT4hGO1AVVO2z }}</div>
        </div>
        <div data-instant-type="text" class="instant-rich-text iL5lPlCB6euBSR7gk">
          <div>{{ section.settings.text_L5lPlCB6euBSR7gk }}</div>
        </div>
      </div>
      <div class="instant-slider-container i8OqDij49kFhdGk1F" data-instant-type="slider-container">
        <script type="application/json" id="instant-slider-bHHC2atZsOjF95QE-params">
          { "breakpoints": { "0": { "speed": 300, "direction": "horizontal", "slidesPerView": 1, "freeMode": false, "centeredSlides": true, "centerInsufficientSlides": true, "spaceBetween": 12 }, "576": { "speed": 300, "direction": "horizontal", "slidesPerView": 1, "freeMode": false, "centeredSlides": false, "centerInsufficientSlides": false, "spaceBetween": 32 }, "769": { "speed": 300, "direction": "horizontal", "slidesPerView": 2, "freeMode": false, "centeredSlides": false, "centerInsufficientSlides": false, "spaceBetween": 32 } }, "navigation": { "nextEl": ".instant-slider-bHHC2atZsOjF95QE-button-next", "prevEl": ".instant-slider-bHHC2atZsOjF95QE-button-prev", "disabledClass": "instant-slider-button-disabled", "hiddenClass": "instant-slider-button-hidden", "lockClass": "instant-slider-button-lock", "navigationDisabledClass": "instant-slider-navigation-disabled" }, "watchOverflow": true, "pagination": { "enabled": true, "clickable": true, "el": ".ikI6gqkoAdFSeZorW.instant-slider-pagination", "dynamicBullets": false, "bulletClass": "instant-slider-pagination-bullet", "bulletActiveClass": "instant-slider-pagination-bullet-active", "modifierClass": "instant-slider-pagination-", "hiddenClass": "instant-slider-pagination-hidden", "clickableClass": "instant-slider-pagination-clickable", "lockClass": "instant-slider-pagination-lock", "horizontalClass": "instant-slider-pagination-horizontal", "verticalClass": "instant-slider-pagination-vertical", "paginationDisabledClass": "instant-slider-pagination-disabled" }, "a11y": { "enabled": true, "notificationClass": "instant-slider-notification" }, "containerModifierClass": "instant-slider-", "noSwipingClass": "instant-slider-no-swiping", "slideClass": "instant-slider-slide", "slideBlankClass": "instant-slider-slide-blank", "slideActiveClass": "instant-slider-slide-active", "slideVisibleClass": "instant-slider-slide-visible", "slideFullyVisibleClass": "instant-slider-slide-fully-visible", "slideNextClass": "instant-slider-slide-next", "slidePrevClass": "instant-slider-slide-prev", "wrapperClass": "instant-slider-wrapper", "lazyPreloaderClass": "instant-slider-lazy-preloader" }
        </script>
        <div class="instant-slider ibHHC2atZsOjF95QE" data-instant-slider-id="bHHC2atZsOjF95QE" id="bHHC2atZsOjF95QE" data-instant-type="slider">
          <div class="instant-slider-wrapper">
            <div class="instant-slider-slide i3RFb9KaXZu9TGdlk" data-instant-type="slider-slide">
              <div class="itxQeisadjYmTOTRW" data-instant-type="container">
                <div class="il2J5dDx5IOrJQnYK" data-instant-type="container">
                  <div data-instant-type="image" class="iPEMcJm7RdnGBCVfv">
                    {% if section.settings.image_PEMcJm7RdnGBCVfv and section.settings.image_PEMcJm7RdnGBCVfv != blank %}
                      {{ section.settings.image_PEMcJm7RdnGBCVfv | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_PEMcJm7RdnGBCVfv.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                    {% else %}
                      <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png" width="2000" height="2000" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                    {% endif %}
                  </div>
                </div>
                <div class="iOmiqbGbK5M9FbiNm" data-instant-type="container">
                  <div class="iYq4ppJU584zbZlcM" data-instant-type="container">
                    <div class="iSrj4cF8JLtN1rAcS" data-instant-type="container">
                      {% if section.settings.image_Srj4cF8JLtN1rAcS and section.settings.image_Srj4cF8JLtN1rAcS != blank %}
                        {{ section.settings.image_Srj4cF8JLtN1rAcS | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_Srj4cF8JLtN1rAcS.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                      {% else %}
                        <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg" width="116" height="19" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                      {% endif %}
                    </div>
                    <div data-instant-type="text" class="instant-rich-text ik7pPLXsNs37bJ529">
                      <div>{{ section.settings.text_k7pPLXsNs37bJ529 }}</div>
                    </div>
                    <div data-instant-type="text" class="instant-rich-text i8lJLBf48QLyMqBQg">
                      <div>{{ section.settings.text_8lJLBf48QLyMqBQg }}</div>
                    </div>
                  </div>
                  <div class="iGqlZbLPlBfexUy3b" data-instant-type="container">
                    <div class="iSO8lXik43daU1mTa" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text ixX5uGteTmajICFZc">
                        <div>{{ section.settings.text_xX5uGteTmajICFZc }}</div>
                      </div>
                      <div data-instant-type="text" class="instant-rich-text i8ehxqEeg5Rq3bfpx">
                        <div>{{ section.settings.text_8ehxqEeg5Rq3bfpx }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="instant-slider-slide i0KIPSEBCbLW8D7WQ" data-instant-type="slider-slide">
              <div class="ik3PforopDXA59Edd" data-instant-type="container">
                <div class="ia2robFxwh4yvrlA6" data-instant-type="container">
                  <div data-instant-type="image" class="inryYGZ9WgCjJK0vZ">
                    {% if section.settings.image_nryYGZ9WgCjJK0vZ and section.settings.image_nryYGZ9WgCjJK0vZ != blank %}
                      {{ section.settings.image_nryYGZ9WgCjJK0vZ | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_nryYGZ9WgCjJK0vZ.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                    {% else %}
                      <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png" width="2000" height="2000" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                    {% endif %}
                  </div>
                </div>
                <div class="isF0XWMBK5WcwgRrZ" data-instant-type="container">
                  <div class="iyeRPan4jL9PNMip4" data-instant-type="container">
                    <div class="iYu1W9wwuDlB6v7hY" data-instant-type="container">
                      {% if section.settings.image_Yu1W9wwuDlB6v7hY and section.settings.image_Yu1W9wwuDlB6v7hY != blank %}
                        {{ section.settings.image_Yu1W9wwuDlB6v7hY | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_Yu1W9wwuDlB6v7hY.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                      {% else %}
                        <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg" width="116" height="19" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                      {% endif %}
                    </div>
                    <div data-instant-type="text" class="instant-rich-text i0bE7QV7xsGsjzBfq">
                      <div>{{ section.settings.text_0bE7QV7xsGsjzBfq }}</div>
                    </div>
                    <div data-instant-type="text" class="instant-rich-text i43mhufJVTbc8TZe0">
                      <div>{{ section.settings.text_43mhufJVTbc8TZe0 }}</div>
                    </div>
                  </div>
                  <div class="iBLMEX09IUglnTQmy" data-instant-type="container">
                    <div class="iquujAU5j6ZcPgiOu" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text i6YJHQvkfhbFr4H4p">
                        <div>{{ section.settings.text_6YJHQvkfhbFr4H4p }}</div>
                      </div>
                      <div data-instant-type="text" class="instant-rich-text i0s7VAWAHVL6gMpcY">
                        <div>{{ section.settings.text_0s7VAWAHVL6gMpcY }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="instant-slider-slide iYYusMyYukswvxpVT" data-instant-type="slider-slide">
              <div class="i8RgrDOJiEGqMM0dF" data-instant-type="container">
                <div class="iGE5SXGbLm8mRolpF" data-instant-type="container">
                  <div data-instant-type="image" class="i4xR1WMu3IsHaLhyN">
                    {% if section.settings.image_4xR1WMu3IsHaLhyN and section.settings.image_4xR1WMu3IsHaLhyN != blank %}
                      {{ section.settings.image_4xR1WMu3IsHaLhyN | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_4xR1WMu3IsHaLhyN.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                    {% else %}
                      <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png" width="2000" height="2000" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                    {% endif %}
                  </div>
                </div>
                <div class="iI15dKqEVtEpmZKZJ" data-instant-type="container">
                  <div class="ifaP12WI3EGkqnfFX" data-instant-type="container">
                    <div class="ijf6DnYfJ4fqEEVRF" data-instant-type="container">
                      {% if section.settings.image_jf6DnYfJ4fqEEVRF and section.settings.image_jf6DnYfJ4fqEEVRF != blank %}
                        {{ section.settings.image_jf6DnYfJ4fqEEVRF | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_jf6DnYfJ4fqEEVRF.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                      {% else %}
                        <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg" width="116" height="19" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                      {% endif %}
                    </div>
                    <div data-instant-type="text" class="instant-rich-text ijQi703Qp8SO0zGAm">
                      <div>{{ section.settings.text_jQi703Qp8SO0zGAm }}</div>
                    </div>
                    <div data-instant-type="text" class="instant-rich-text i0Tv93o9ZjPWaPjW4">
                      <div>{{ section.settings.text_0Tv93o9ZjPWaPjW4 }}</div>
                    </div>
                  </div>
                  <div class="iW0SUIiUdgamZZIxt" data-instant-type="container">
                    <div class="iUTCVdP6IN0fNKmWr" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text iaOB8CkTbcRQ3MYcA">
                        <div>{{ section.settings.text_aOB8CkTbcRQ3MYcA }}</div>
                      </div>
                      <div data-instant-type="text" class="instant-rich-text i34ZJdj4vwItKD8wq">
                        <div>{{ section.settings.text_34ZJdj4vwItKD8wq }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="instant-slider-slide idPzq4H4O4PE2U3Iq" data-instant-type="slider-slide">
              <div class="i70qtFOYcIKB8GHVY" data-instant-type="container">
                <div class="iln8Pm4265IutiEGg" data-instant-type="container">
                  <div data-instant-type="image" class="ieGCXZC0s0JKwDtVP">
                    {% if section.settings.image_eGCXZC0s0JKwDtVP and section.settings.image_eGCXZC0s0JKwDtVP != blank %}
                      {{ section.settings.image_eGCXZC0s0JKwDtVP | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_eGCXZC0s0JKwDtVP.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                    {% else %}
                      <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png" width="2000" height="2000" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                    {% endif %}
                  </div>
                </div>
                <div class="igCB9fpCUEYnW5dfD" data-instant-type="container">
                  <div class="iAKmp3prXQBhB2ApU" data-instant-type="container">
                    <div class="isli64nqKRsFPoUTW" data-instant-type="container">
                      {% if section.settings.image_sli64nqKRsFPoUTW and section.settings.image_sli64nqKRsFPoUTW != blank %}
                        {{ section.settings.image_sli64nqKRsFPoUTW | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_sli64nqKRsFPoUTW.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                      {% else %}
                        <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg" width="116" height="19" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                      {% endif %}
                    </div>
                    <div data-instant-type="text" class="instant-rich-text iO9l0eW5cn4eRlfF1">
                      <div>{{ section.settings.text_O9l0eW5cn4eRlfF1 }}</div>
                    </div>
                    <div data-instant-type="text" class="instant-rich-text ifmMftL6Q1lIpumT8">
                      <div>{{ section.settings.text_fmMftL6Q1lIpumT8 }}</div>
                    </div>
                  </div>
                  <div class="iAQSNT47HEjxvbZXp" data-instant-type="container">
                    <div class="iBzgKNeraGwoFSgIU" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text iK0AbYyCaqFRg5Npg">
                        <div>{{ section.settings.text_K0AbYyCaqFRg5Npg }}</div>
                      </div>
                      <div data-instant-type="text" class="instant-rich-text iCfluzwEgX50FgGZq">
                        <div>{{ section.settings.text_CfluzwEgX50FgGZq }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="instant-slider-slide iFa6zryppnf9CGNhW" data-instant-type="slider-slide">
              <div class="iMwycWX7wI0V6sGir" data-instant-type="container">
                <div class="iDyPXGFtpnvF47tb7" data-instant-type="container">
                  <div data-instant-type="image" class="iIMegm2zjFzKA7SAq">
                    {% if section.settings.image_IMegm2zjFzKA7SAq and section.settings.image_IMegm2zjFzKA7SAq != blank %}
                      {{ section.settings.image_IMegm2zjFzKA7SAq | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_IMegm2zjFzKA7SAq.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                    {% else %}
                      <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png" width="2000" height="2000" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                    {% endif %}
                  </div>
                </div>
                <div class="iJj1iXnVDcH1uh7Pm" data-instant-type="container">
                  <div class="i60J66fyzJcRUMz4V" data-instant-type="container">
                    <div class="iSKHBn1fxHp3YM2aa" data-instant-type="container">
                      {% if section.settings.image_SKHBn1fxHp3YM2aa and section.settings.image_SKHBn1fxHp3YM2aa != blank %}
                        {{ section.settings.image_SKHBn1fxHp3YM2aa | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_SKHBn1fxHp3YM2aa.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                      {% else %}
                        <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg" width="116" height="19" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                      {% endif %}
                    </div>
                    <div data-instant-type="text" class="instant-rich-text iT0Oeb74n4jRNTBUD">
                      <div>{{ section.settings.text_T0Oeb74n4jRNTBUD }}</div>
                    </div>
                    <div data-instant-type="text" class="instant-rich-text ioiWAB17RBdUCoR3m">
                      <div>{{ section.settings.text_oiWAB17RBdUCoR3m }}</div>
                    </div>
                  </div>
                  <div class="iOL6Laxqwnqlakg2z" data-instant-type="container">
                    <div class="imfvworZi1evU86b0" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text imoG8kPPFRECPgkEK">
                        <div>{{ section.settings.text_moG8kPPFRECPgkEK }}</div>
                      </div>
                      <div data-instant-type="text" class="instant-rich-text i9OTOfTBOQov6rWyb">
                        <div>{{ section.settings.text_9OTOfTBOQov6rWyb }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="instant-slider-slide iVEsI4vkWCoM2pzpY" data-instant-type="slider-slide">
              <div class="iG7EBaZOsQ0vmpKkJ" data-instant-type="container">
                <div class="iolwBvXKmML0HUpdL" data-instant-type="container">
                  <div data-instant-type="image" class="isSs1austftcoO567">
                    {% if section.settings.image_sSs1austftcoO567 and section.settings.image_sSs1austftcoO567 != blank %}
                      {{ section.settings.image_sSs1austftcoO567 | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_sSs1austftcoO567.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                    {% else %}
                      <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png" width="2000" height="2000" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                    {% endif %}
                  </div>
                </div>
                <div class="imIa8EVhCZNiBkxuw" data-instant-type="container">
                  <div class="iN287JOLuZOSJm4PC" data-instant-type="container">
                    <div class="iRVVy6vgTQCl9JiOW" data-instant-type="container">
                      {% if section.settings.image_RVVy6vgTQCl9JiOW and section.settings.image_RVVy6vgTQCl9JiOW != blank %}
                        {{ section.settings.image_RVVy6vgTQCl9JiOW | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_RVVy6vgTQCl9JiOW.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                      {% else %}
                        <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg" width="116" height="19" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                      {% endif %}
                    </div>
                    <div data-instant-type="text" class="instant-rich-text idBZTEUOV5T2BNO2X">
                      <div>{{ section.settings.text_dBZTEUOV5T2BNO2X }}</div>
                    </div>
                    <div data-instant-type="text" class="instant-rich-text ia0mskNg6K9lQngFo">
                      <div>{{ section.settings.text_a0mskNg6K9lQngFo }}</div>
                    </div>
                  </div>
                  <div class="iq0pMY2aBQTa8Vwra" data-instant-type="container">
                    <div class="i3pmUMczkqB4vfMKM" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text iK5wxdirvIDWCYpze">
                        <div>{{ section.settings.text_K5wxdirvIDWCYpze }}</div>
                      </div>
                      <div data-instant-type="text" class="instant-rich-text iDPhFzlsaDcSK3s1r">
                        <div>{{ section.settings.text_DPhFzlsaDcSK3s1r }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="ibVy4hJAoYal0ppXz" data-instant-type="container">
          <div class="inTW8WhtdUmrWhZHL" data-instant-type="container">
            <button class="instant-slider-button instant-slider-button-prev instant-slider-bHHC2atZsOjF95QE-button-prev i9tbBOXDMjEQCODXx" data-instant-type="slider-button-prev" type="button">
              <div data-instant-type="icon" class="itKSGVCveoBIYlzS5">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                  <title>arrow-left</title><path d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z"></path>
                </svg>
              </div>
            </button>
            <button class="instant-slider-button instant-slider-button-next instant-slider-bHHC2atZsOjF95QE-button-next iqj6cJYPd0O7setxi" data-instant-type="slider-button-next" type="button">
              <div data-instant-type="icon" class="iCmW8z1k4Eoo2JlnW">
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                  <title>arrow-right</title><path d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z"></path>
                </svg>
              </div>
            </button>
          </div>
          <div class="instant-slider-pagination ikI6gqkoAdFSeZorW" data-instant-type="container"><div class="instant-slider-pagination-bullet iS1fURsS85ELsNAVu" data-instant-type="container"></div></div>
        </div>
      </div>
    </div>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.3",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison Reviews Carousel",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "richtext",
      "id": "text_kSsRT4hGO1AVVO2z",
      "label": "Heading",
      "default": "<p>Real Adventures. </p><p>Real Feedback.</p>"
    },
    {
      "type": "richtext",
      "id": "text_L5lPlCB6euBSR7gk",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_PEMcJm7RdnGBCVfv",
      "label": "Image"
    },
    {
      "type": "image_picker",
      "id": "image_Srj4cF8JLtN1rAcS",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_k7pPLXsNs37bJ529",
      "label": "Text",
      "default": "<p>“Main objection handled here”</p>"
    },
    {
      "type": "richtext",
      "id": "text_8lJLBf48QLyMqBQg",
      "label": "Quote",
      "default": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>"
    },
    {
      "type": "richtext",
      "id": "text_xX5uGteTmajICFZc",
      "label": "Text",
      "default": "<p>Name Surname</p>"
    },
    {
      "type": "richtext",
      "id": "text_8ehxqEeg5Rq3bfpx",
      "label": "Text",
      "default": "<p>Woman, 38 years, New York City</p>"
    },
    {
      "type": "image_picker",
      "id": "image_nryYGZ9WgCjJK0vZ",
      "label": "Image"
    },
    {
      "type": "image_picker",
      "id": "image_Yu1W9wwuDlB6v7hY",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_0bE7QV7xsGsjzBfq",
      "label": "Text",
      "default": "<p>“Main objection handled here”</p>"
    },
    {
      "type": "richtext",
      "id": "text_43mhufJVTbc8TZe0",
      "label": "Quote",
      "default": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>"
    },
    {
      "type": "richtext",
      "id": "text_6YJHQvkfhbFr4H4p",
      "label": "Text",
      "default": "<p>Name Surname</p>"
    },
    {
      "type": "richtext",
      "id": "text_0s7VAWAHVL6gMpcY",
      "label": "Text",
      "default": "<p>Woman, 38 years, New York City</p>"
    },
    {
      "type": "image_picker",
      "id": "image_4xR1WMu3IsHaLhyN",
      "label": "Image"
    },
    {
      "type": "image_picker",
      "id": "image_jf6DnYfJ4fqEEVRF",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_jQi703Qp8SO0zGAm",
      "label": "Text",
      "default": "<p>“Main objection handled here”</p>"
    },
    {
      "type": "richtext",
      "id": "text_0Tv93o9ZjPWaPjW4",
      "label": "Quote",
      "default": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>"
    },
    {
      "type": "richtext",
      "id": "text_aOB8CkTbcRQ3MYcA",
      "label": "Text",
      "default": "<p>Name Surname</p>"
    },
    {
      "type": "richtext",
      "id": "text_34ZJdj4vwItKD8wq",
      "label": "Text",
      "default": "<p>Woman, 38 years, New York City</p>"
    },
    {
      "type": "image_picker",
      "id": "image_eGCXZC0s0JKwDtVP",
      "label": "Image"
    },
    {
      "type": "image_picker",
      "id": "image_sli64nqKRsFPoUTW",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_O9l0eW5cn4eRlfF1",
      "label": "Text",
      "default": "<p>“Main objection handled here”</p>"
    },
    {
      "type": "richtext",
      "id": "text_fmMftL6Q1lIpumT8",
      "label": "Quote",
      "default": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>"
    },
    {
      "type": "richtext",
      "id": "text_K0AbYyCaqFRg5Npg",
      "label": "Text",
      "default": "<p>Name Surname</p>"
    },
    {
      "type": "richtext",
      "id": "text_CfluzwEgX50FgGZq",
      "label": "Text",
      "default": "<p>Woman, 38 years, New York City</p>"
    },
    {
      "type": "image_picker",
      "id": "image_IMegm2zjFzKA7SAq",
      "label": "Image"
    },
    {
      "type": "image_picker",
      "id": "image_SKHBn1fxHp3YM2aa",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_T0Oeb74n4jRNTBUD",
      "label": "Text",
      "default": "<p>“Main objection handled here”</p>"
    },
    {
      "type": "richtext",
      "id": "text_oiWAB17RBdUCoR3m",
      "label": "Quote",
      "default": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>"
    },
    {
      "type": "richtext",
      "id": "text_moG8kPPFRECPgkEK",
      "label": "Text",
      "default": "<p>Name Surname</p>"
    },
    {
      "type": "richtext",
      "id": "text_9OTOfTBOQov6rWyb",
      "label": "Text",
      "default": "<p>Woman, 38 years, New York City</p>"
    },
    {
      "type": "image_picker",
      "id": "image_sSs1austftcoO567",
      "label": "Image"
    },
    {
      "type": "image_picker",
      "id": "image_RVVy6vgTQCl9JiOW",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_dBZTEUOV5T2BNO2X",
      "label": "Text",
      "default": "<p>“Main objection handled here”</p>"
    },
    {
      "type": "richtext",
      "id": "text_a0mskNg6K9lQngFo",
      "label": "Quote",
      "default": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>"
    },
    {
      "type": "richtext",
      "id": "text_K5wxdirvIDWCYpze",
      "label": "Text",
      "default": "<p>Name Surname</p>"
    },
    {
      "type": "richtext",
      "id": "text_DPhFzlsaDcSK3s1r",
      "label": "Text",
      "default": "<p>Woman, 38 years, New York City</p>"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison Reviews Carousel",
      "settings": {
        "text_kSsRT4hGO1AVVO2z": "<p>Real Adventures. </p><p>Real Feedback.</p>",
        "text_L5lPlCB6euBSR7gk": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit.</p>",
        "text_k7pPLXsNs37bJ529": "<p>“Main objection handled here”</p>",
        "text_8lJLBf48QLyMqBQg": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>",
        "text_xX5uGteTmajICFZc": "<p>Name Surname</p>",
        "text_8ehxqEeg5Rq3bfpx": "<p>Woman, 38 years, New York City</p>",
        "text_0bE7QV7xsGsjzBfq": "<p>“Main objection handled here”</p>",
        "text_43mhufJVTbc8TZe0": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>",
        "text_6YJHQvkfhbFr4H4p": "<p>Name Surname</p>",
        "text_0s7VAWAHVL6gMpcY": "<p>Woman, 38 years, New York City</p>",
        "text_jQi703Qp8SO0zGAm": "<p>“Main objection handled here”</p>",
        "text_0Tv93o9ZjPWaPjW4": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>",
        "text_aOB8CkTbcRQ3MYcA": "<p>Name Surname</p>",
        "text_34ZJdj4vwItKD8wq": "<p>Woman, 38 years, New York City</p>",
        "text_O9l0eW5cn4eRlfF1": "<p>“Main objection handled here”</p>",
        "text_fmMftL6Q1lIpumT8": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>",
        "text_K0AbYyCaqFRg5Npg": "<p>Name Surname</p>",
        "text_CfluzwEgX50FgGZq": "<p>Woman, 38 years, New York City</p>",
        "text_T0Oeb74n4jRNTBUD": "<p>“Main objection handled here”</p>",
        "text_oiWAB17RBdUCoR3m": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>",
        "text_moG8kPPFRECPgkEK": "<p>Name Surname</p>",
        "text_9OTOfTBOQov6rWyb": "<p>Woman, 38 years, New York City</p>",
        "text_dBZTEUOV5T2BNO2X": "<p>“Main objection handled here”</p>",
        "text_a0mskNg6K9lQngFo": "<p>\"These shoes have changed my life! I can now take long walks without any pain. It&apos;s like walking on clouds!\"</p>",
        "text_K5wxdirvIDWCYpze": "<p>Name Surname</p>",
        "text_DPhFzlsaDcSK3s1r": "<p>Woman, 38 years, New York City</p>"
      }
    }
  ]
}
{% endschema %}
