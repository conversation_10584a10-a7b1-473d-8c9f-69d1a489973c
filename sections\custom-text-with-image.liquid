 {% style %}
  .section-{{ section.id }} {
    padding-top: {{section.settings.padding_top}}px;
    padding-bottom: {{section.settings.padding_bottom}}px;
  }
  .section-{{ section.id }} .gradient-container {
    background-image: {{ section.settings.bg_color }};
    width: 100%;
    max-width: 1180px;
    margin: auto;
    border-radius: 10px;
  }
  .section-{{ section.id }} .custom-twi-image img {
    width: 100%;
  }
  {%- if section.settings.bg_image != blank -%}
  .section-{{ section.id }} .custom-twi-container {
    background-image: url({{section.settings.bg_image | img_url: 'master' }});
  }
  {%- endif -%}
  .section-{{ section.id }} .custom-twi-content>span>* {
    color: {{ section.settings.paragraph_color }};
  }
  .overlay-top .custom-twi-container {
    padding-top: 100px;
}
  .custom-twi-container {
    padding-top: 50px;
    padding-bottom: 0;
    display: flex;
    align-items: center;
    gap: 20px;
    border-radius: 10px;
}
  .custom-twi-content:has(>.custom-social_media_ratings-container) {
    flex-wrap: wrap;
    flex-direction: unset;
}
   .custom-twi-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
    justify-content: center;
    align-items: flex-start;
    padding: 50px 0;
}
   .custom-twi-content.content-align-center>* {
    text-align: center;
}
   .custom-twi-content>* {
    margin: 0;
}

/* RTE paragraph spacing and formatting for Custom Text with Image */
.section-{{ section.id }} .custom-twi-content span p {
  margin: 0 0 16px 0;
  line-height: 1.6;
  color: {{ section.settings.paragraph_color }};
}
.section-{{ section.id }} .custom-twi-content span p:last-child {
  margin-bottom: 0;
}
.section-{{ section.id }} .custom-twi-content span ul,
.section-{{ section.id }} .custom-twi-content span ol {
  margin: 16px 0;
  padding-left: 1.25rem;
  color: {{ section.settings.paragraph_color }};
}
.section-{{ section.id }} .custom-twi-content span li {
  margin: 8px 0;
  line-height: 1.6;
  color: {{ section.settings.paragraph_color }};
}
.section-{{ section.id }} .custom-twi-content span strong {
  font-weight: 700;
}
.section-{{ section.id }} .custom-twi-content span em {
  font-style: italic;
}
.section-{{ section.id }} .custom-twi-content span a {
  color: {{ section.settings.paragraph_color | color_modify: 'alpha', 0.8 }};
  text-decoration: underline;
}
.section-{{ section.id }} .custom-twi-content span a:hover {
  text-decoration: none;
}
   .custom-social_media_ratings-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    max-width: 305px;
    padding: 0 20px 20px;
    border-radius: 8px;
}
   .social-image {
    align-self: center;
}
   .social-rated {
    text-align: center;
}
   svg.social_custom-star {
    width: 100%;
    max-width: 20px;
}
   .social-stars {
    display: flex;
    gap: 5px;
    justify-content: center;
}
.custom-twi-content h2 {
    font-size: 37px;
    text-align: center;
   font-weight: bold;
   padding: 0 15px;
}
  @media(max-width: 767px){
    .section-{{ section.id }} {
      padding-top: calc({{section.settings.padding_top}}px/2);
      padding-bottom: calc({{section.settings.padding_bottom}}px/2);
    }
    .custom-social_media_ratings-container {
    width: 40%;
    padding: 10px;
}
    .custom-twi-content h2 {
    font-size: 27px;
}
    .custom-twi-content.content-align-left {
    padding-top: 10px;
}

    /* Mobile-specific RTE paragraph adjustments */
    .section-{{ section.id }} .custom-twi-content span p {
      margin: 0 0 14px 0;
      line-height: 1.5;
    }
    .section-{{ section.id }} .custom-twi-content span ul,
    .section-{{ section.id }} .custom-twi-content span ol {
      margin: 14px 0;
    }
    .section-{{ section.id }} .custom-twi-content span li {
      margin: 6px 0;
    }
  }
{% endstyle %}

<div class="custom-twi section-{{ section.id }} {{ section.settings.custom_class }}">
  <div class="gradient-container">
    <div class="wrapper{% if section.settings.full_width %} custom-full-width{% endif %} custom-twi-container align-{% if section.settings.img_alignment == 'left'%}left{% else %}right{% endif %}">
      <div class="custom-twi-content content-align-{% if section.settings.content_alignment == 'left' %}left{% elsif section.settings.content_alignment == "center" %}center{% else %}right{% endif %}">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            
            {%- when 'heading' -%}
              <h2 style="color:{{ block.settings.header_color }}">{{ block.settings.header }}</h2>
  
            {%- when 'text' -%}
              <span>{{ block.settings.paragraph }}</span>

            {%- when 'sub_heading' -%}
              <h3 style="color:{{ block.settings.sub_header_color }}">{{ block.settings.sub_header }}</h3>
            
            {%- when 'button' -%}
              <a href="{{ block.settings.button_link }}"{% if block.settings.external_link %} target="_blank"{% endif %}>
                {{ block.settings.button_text }}
                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" height="27" width="27" version="1.1" id="Capa_1" viewBox="0 0 185.343 185.343" xml:space="preserve">
                  <g>
                  	<g>
                  		<path style="fill:#34620F;" d="M51.707,185.343c-2.741,0-5.493-1.044-7.593-3.149c-4.194-4.194-4.194-10.981,0-15.175    l74.352-74.347L44.114,18.32c-4.194-4.194-4.194-10.987,0-15.175c4.194-4.194,10.987-4.194,15.18,0l81.934,81.934    c4.194,4.194,4.194,10.987,0,15.175l-81.934,81.939C57.201,184.293,54.454,185.343,51.707,185.343z"/>
                  	</g>
                  </g>
                </svg>
              </a>
            
            {%- when 'ratings' -%}
              <div class="custom-stars">
                {% for rating in (1..5) %}
                  {% if block.settings.star_rating >= rating %}
                    <svg role="presentation" fill="none" focusable="false" class="custom-star" viewBox="0 0 15 15">
                      <path d="M7.5 0L9.58587 5.2731L15 5.72949L10.875 9.44483L12.1353 15L7.5 12.0231L2.86475 15L4.125 9.44483L0 5.72949L5.41414 5.2731L7.5 0Z" fill="#FABE1E"></path>
                    </svg>
                  {% endif %}
                {% endfor %}
              </div>

            {%- when 'social_media_ratings' -%}
              <div class="custom-social_media_ratings-container" style="background-color: {{ block.settings.social_container_color }}">
                {%- if block.settings.social_image -%}
                <div class="social-image">
                  <img src="{{ block.settings.social_image | img_url: 'master' }}">
                </div>
                {%- endif -%}
                <div class="social-rated">
                  <span style="color: {{ block.settings.rated_color}}"><b>Rated {{ block.settings.social_star_rating }}</b></span>
                </div>
                <div class="social-stars">
                  {% for rating in (1..5) %}
                  {% if block.settings.social_star_rating >= rating %}
                      <svg role="presentation" fill="none" focusable="false" class="social_custom-star" viewBox="0 0 15 15">
                        <path d="M7.5 0L9.58587 5.2731L15 5.72949L10.875 9.44483L12.1353 15L7.5 12.0231L2.86475 15L4.125 9.44483L0 5.72949L5.41414 5.2731L7.5 0Z" fill="#FABE1E"></path>
                      </svg>
                    {% endif %}
                  {% endfor %}
                </div>
              </div>
            
          {%- endcase -%}
        {%- endfor -%}
      </div>
      {%- if section.settings.image != blank -%}
        {%- assign desktop_image = section.settings.image -%}
        <div class="custom-twi-image custom-desktop">
          <img src="{{ desktop_image | img_url: 'master' }}">
        </div>
        
        <div class="custom-twi-image custom-mobile">
          <img src="{{ section.settings.mobile_image | default: desktop_image | img_url: 'master' }}">
        </div>
      {%- endif -%}
    </div>
  </div>
</div>



{% schema %}
  {
    "name": "Custom: Text with Image",
    "settings": [
      {
        "type": "image_picker",
        "id": "image",
        "label": "Dekstop Image"
      },
      {
        "type": "image_picker",
        "id": "mobile_image",
        "label": "Mobile Image"
      },
      {
        "type": "select",
        "id": "img_alignment",
        "label": "Image Alignment",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "right"
      },
      {
        "type": "select",
        "id": "content_alignment",
        "label": "Content Alignment",
        "options": [
          {
            "value": "left",
            "label": "Left"
          },
          {
            "value": "center",
            "label": "Center"
          },
          {
            "value": "right",
            "label": "Right"
          }
        ],
        "default": "left"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 1,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Padding Top",
        "default": 50
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "min": 1,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Padding Bottom",
        "default": 50
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full Width",
        "default": false
      },
      {
        "type": "color_background",
        "id": "bg_color",
        "label": "Background",
        "default": "linear-gradient(#ffffff, #ffffff)"
      },
      {
        "type": "image_picker",
        "id": "bg_image",
        "label": "Background Image"
      },
      {
        "type": "color",
        "id": "paragraph_color",
        "label": "Paragraph Color",
        "default": "#000000"
      },
      {
        "type": "text",
        "id": "custom_class",
        "label": "Custom Class"
      }
    ],
    "blocks": [
       {
         "name": "Heading",
         "type": "heading",
         "settings": [
           {
              "type": "text",
              "id": "header",
              "label": "Heading",
              "default": "Heading"
           },
           {
              "type": "color",
              "id": "header_color",
              "label": "Heading Color",
              "default": "#000000"
            }
         ]
       },
       {
         "name": "Text",
         "type": "text",
         "settings": [
           {
              "type": "richtext",
              "id": "paragraph",
              "label": "Paragraph"
           }
         ]
       },
       {
         "name": "Sub Heading",
         "type": "sub_heading",
         "settings": [
           {
              "type": "text",
              "id": "sub_header",
              "label": "Sub Heading",
              "default": "Sub Heading"
           },
           {
              "type": "color",
              "id": "sub_header_color",
              "label": "Sub Heading Color",
              "default": "#000000"
            }
         ]
       },
       {
         "name": "Button",
         "type": "button",
         "limit": 1,
         "settings": [
           {
              "type": "text",
              "id": "button_text",
              "label": "Button Text",
              "default": "See More"
           },
           {
              "type": "url",
              "id": "button_link",
              "label": "Button link"
            },
            {
              "type": "checkbox",
              "id": "external_link",
              "label": "External Link",
              "default": false
            }
         ]
       },
       {
         "name": "Ratings",
         "type": "ratings",
         "limit": 1,
         "settings": [
           {
              "type": "range",
              "id": "star_rating",
              "min": 0,
              "max": 5,
              "step": 1,
              "label": "Rating",
              "default": 5
           }
         ]
       },
      {
         "name": "Social Media Ratings",
         "type": "social_media_ratings",
         "settings": [
           {
              "type": "image_picker",
              "id": "social_image",
              "label": "Social Image"
           },
           {
              "type": "range",
              "id": "social_star_rating",
              "min": 0,
              "max": 5,
              "step": 0.1,
              "label": "Rating",
              "default": 5
           },
           {
              "type": "color",
              "id": "rated_color",
              "label": "Rated Color",
              "default": "#000000"
            },
            {
              "type": "color",
              "id": "social_container_color",
              "label": "Container Color",
              "default": "#000000"
            }
         ]
       }
    ],
    "presets": [
      {
        "name": "Custom: Text with Image"
      }
    ]
  }
{% endschema %}