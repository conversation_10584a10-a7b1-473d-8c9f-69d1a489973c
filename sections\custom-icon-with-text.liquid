 {% style %}
  .section-{{ section.id }} {
    padding-top: {{section.settings.padding_top}}px;
    padding-bottom: {{section.settings.padding_bottom}}px;
    background-color: {{section.settings.bg_color}};
  }

  /* RTE paragraph spacing and formatting for Custom Icon with Text */
  .section-{{ section.id }} .custom-ciwt.header p {
    margin: 0 0 16px 0;
    line-height: 1.6;
  }
  .section-{{ section.id }} .custom-ciwt.header p:last-child {
    margin-bottom: 0;
  }
  .section-{{ section.id }} .custom-ciwt.header ul,
  .section-{{ section.id }} .custom-ciwt.header ol {
    margin: 16px 0;
    padding-left: 1.25rem;
  }
  .section-{{ section.id }} .custom-ciwt.header li {
    margin: 8px 0;
    line-height: 1.6;
  }
  .section-{{ section.id }} .custom-ciwt.header strong {
    font-weight: 700;
  }
  .section-{{ section.id }} .custom-ciwt.header em {
    font-style: italic;
  }
  .section-{{ section.id }} .custom-ciwt.header a {
    text-decoration: underline;
  }
  .section-{{ section.id }} .custom-ciwt.header a:hover {
    text-decoration: none;
  }

  /* RTE paragraph spacing for block text content */
  .section-{{ section.id }} .ciwt-text p {
    margin: 0 0 12px 0;
    line-height: 1.5;
  }
  .section-{{ section.id }} .ciwt-text p:last-child {
    margin-bottom: 0;
  }
  .section-{{ section.id }} .ciwt-text ul,
  .section-{{ section.id }} .ciwt-text ol {
    margin: 12px 0;
    padding-left: 1rem;
  }
  .section-{{ section.id }} .ciwt-text li {
    margin: 6px 0;
    line-height: 1.5;
  }

  @media(max-width: 767px){
    .section-{{ section.id }} {
      padding-top: calc({{section.settings.padding_top}}px/2);
      padding-bottom: calc({{section.settings.padding_bottom}}px/2);
    }

    /* Mobile-specific RTE paragraph adjustments */
    .section-{{ section.id }} .custom-ciwt.header p {
      margin: 0 0 14px 0;
      line-height: 1.5;
    }
    .section-{{ section.id }} .custom-ciwt.header ul,
    .section-{{ section.id }} .custom-ciwt.header ol {
      margin: 14px 0;
    }
    .section-{{ section.id }} .custom-ciwt.header li {
      margin: 6px 0;
    }
    .section-{{ section.id }} .ciwt-text p {
      margin: 0 0 10px 0;
    }
    .section-{{ section.id }} .ciwt-text ul,
    .section-{{ section.id }} .ciwt-text ol {
      margin: 10px 0;
    }
    .section-{{ section.id }} .ciwt-text li {
      margin: 4px 0;
    }
  }
{% endstyle %}

<div class="custom-ciwt section-{{ section.id }} {{ section.settings.custom_class }}" id="claim-bogo-now">
  <div class="ciwt-wrapper{% if section.settings.full_width %} custom-full-width{% endif %} ">
    <div class="custom-ciwt header">
      {%- if section.settings.header != blank-%}
        <h2 class="ciwt-header">{{ section.settings.header }}</h2>
      {%- endif -%}
      {%- if section.settings.paragraph != blank-%}
        {{ section.settings.paragraph }}
      {%- endif -%}
    </div>
    <div class="custom-ciwt body">
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          
          {%- when 'icon_text' -%}
            <div class="ciwt">
              {%- if block.settings.block_icon != blank-%}
                <div class="ciwt-icon">
                  <img src="{{ block.settings.block_icon | img_url: 'master'  }}">
                </div>
              {%- endif -%}
              {%- if block.settings.block_text != blank-%}
                <div class="ciwt-text">
                  {{ block.settings.block_text }}
                </div>
              {%- endif -%}
            </div>
          
        {%- endcase -%}
      {%- endfor -%}
    </div>
  </div>
  <div class="indicators"></div>
</div>

{% schema %}
  {
    "name": "Custom: Icon with Text",
    "settings": [
      {
        "type": "text",
        "id": "header",
        "label": "Heading",
        "default": "Heading"
      },
      {
        "type": "richtext",
        "id": "paragraph",
        "label": "Paragraph"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 1,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Padding Top",
        "default": 50
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "min": 1,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Padding Bottom",
        "default": 50
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full Width",
        "default": false
      },
      {
        "type": "color",
        "id": "bg_color",
        "label": "Background Color",
        "default": "#ffffff"
      },
      {
        "type": "text",
        "id": "custom_class",
        "label": "Custom Class"
      }
    ],
    "blocks": [
       {
         "name": "Text with Icon",
         "type": "icon_text",
         "settings": [
           {
              "type": "image_picker",
              "id": "block_icon",
              "label": "Icon"
           },
           {
              "type": "text",
              "id": "block_text",
              "label": "Text",
              "default": "Text Here.."
            }
         ]
       }
    ],
    "presets": [
      {
        "name": "Custom: Icon with Text"
      }
    ]
  }
{% endschema %}

<script>
const slider = document.querySelector('.custom-ciwt .body');
const items = document.querySelectorAll('.ciwt');
const indicatorsContainer = document.querySelector('.indicators');
let currentIndex = 0;
let startX = 0;
let currentX = 0;
let isDragging = false;

function createIndicators() {
  indicatorsContainer.innerHTML = ''; // Clear existing indicators
  items.forEach((_, index) => {
    const indicator = document.createElement('div');
    indicator.classList.add('indicator');
    if (index === 0) indicator.classList.add('active'); // Make the first indicator active
    indicatorsContainer.appendChild(indicator);
  });
}

function updateSliderPosition() {
  const offset = -currentIndex * 100;
  slider.style.transform = `translateX(${offset}%)`;
  updateIndicators();
}

function updateIndicators() {
  const indicators = document.querySelectorAll('.indicator');
  indicators.forEach((indicator, index) => {
    indicator.classList.toggle('active', index === currentIndex);
  });
}

function handleTouchStart(e) {
  startX = e.touches[0].clientX;
  isDragging = true;
  slider.style.transition = 'none'; // Disable transition during drag
}

function handleTouchMove(e) {
  if (!isDragging) return;
  currentX = e.touches[0].clientX;
  const deltaX = currentX - startX;
  slider.style.transform = `translateX(calc(${-currentIndex * 100}% + ${deltaX}px))`;
}

function handleTouchEnd() {
  if (!isDragging) return;
  isDragging = false;

  const deltaX = currentX - startX;
  slider.style.transition = 'transform 0.5s ease'; // Re-enable transition

  if (deltaX < -50 && currentIndex < items.length - 1) {
    currentIndex++;
  } else if (deltaX > 50 && currentIndex > 0) {
    currentIndex--;
  }

  updateSliderPosition();
}

function checkScreenSize() {
  const isMobile = window.innerWidth <= 1000;
  if (isMobile) {
    createIndicators();
    indicatorsContainer.style.display = 'flex';
  } else {
    indicatorsContainer.style.display = 'none';
  }
}

// Attach touch events
slider.addEventListener('touchstart', handleTouchStart);
slider.addEventListener('touchmove', handleTouchMove);
slider.addEventListener('touchend', handleTouchEnd);

// Listen for resize to adjust indicators dynamically
window.addEventListener('resize', checkScreenSize);

// Initialize on page load
checkScreenSize();

</script>