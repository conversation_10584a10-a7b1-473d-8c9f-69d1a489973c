
  
  
    {% style %}
        .text-3xl {
            font-size: 1.875rem;
            line-height: 3.25rem;
        }
        .font-bold {
            font-weight: 700;
        }
        .mb-2 {
            margin-bottom: 0.5rem;
        }
        .mb-8 {
            margin-bottom: 2rem;
        }
        .relative {
            position: relative;
        }
        .carousel-container {
            overflow: hidden;
            position: relative;
            width: 100%;
            max-width: 1300px;
            margin: 0 auto;
        }
        .carousel-inner {
            display: flex;
            transition: transform 0.5s ease-in-out;
          gap: 10px;
        }
        .space-x-4 > * + * {
            margin-left: 1rem;
        }.rounded-lg {
            border-radius: 0.5rem;
        }
        .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .p-6 {
            padding: 1.5rem;
        }
        .w-full {
            width: 100%;
        }
        .md\:w-1\/2 {
            width: 100%;
        }
        .carousel-item {
            transition: transform 0.5s ease-in-out;
        }
        .flex {
            display: flex;
        }
        .flex-col {
            flex-direction: column;
        }
        .md\:flex-row {
            flex-direction: row;
        }
        .items-start {
            align-items: flex-start;
        }
        .w-24 {
            width: 6rem;
        }
        .h-24 {
            height: 6rem;
        }
        .rounded-lg {
            border-radius: 0.5rem;
        }
        .mb-4 {
            margin-bottom: 1rem;
        }
        .md\:mb-0 {
            margin-bottom: 0;
        }
        .md\:mr-4 {
            margin-right: 1rem;
        }
        .text-xl {
            font-size: 1.25rem;
            line-height: 1.75rem;
        }
        .font-semibold {
            font-weight: 600;
        }
        .text-gray-700 {
            color: #4a5568;
        }
        .font-bold {
            font-weight: 700;
        }
        .mt-8 {
            margin-top: 2rem;
        }
        .space-x-2 > * + * {
            margin-left: 0.5rem;
        }
        .w-3 {
            width: 0.5rem;
        }
        .h-3 {
            height: 0.5rem;
        }
        .bg-gray-400 {
            background-color: #cbd5e0;
        }
        .bg-gray-800 {
            background-color: #2d3748;
        }
        .rounded-full {
            border-radius: 9999px;
        }
        .mt-4 {
            margin-top: 1rem;
        }
        .w-10 {
            width: 2.5rem;
        }
        .h-10 {
            height: 2.5rem;
        }
.shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .items-center {
            align-items: center;
        }
        .justify-center {
            justify-content: center;
        }
        .fas {
            font-family: "Font Awesome 5 Free";
            font-weight: 900;
        }
        .fa-star {
            color: #fbbf24;
        }
        .fa-arrow-left, .fa-arrow-right {
            color: #4a5568;
        }
      .bg-white {
        background: white;
            border: 1px solid #dddddd;
      }
      .main-container {
    max-width: 1340px;
    margin: auto;
    padding: 20px;
}
      .review-section-container {
    text-align: center;
    background: #f0f4ee;
}
        .carousel-item {
            transition: transform 0.5s ease-in-out;
        }
        .carousel-container {
            overflow: hidden;
            position: relative;
            width: 100%;
            max-width: 1300px;
            margin: 0 auto;
        }
		.review-navigator {
		display: flex;
		justify-content: space-between;
		}
img.img-review {
    width: 200px;
    height: 240px;
    border-radius: 10px;
  object-fit: cover;
}
.review-content {
    gap: 15px;
}
.review-text {
text-align: left;
margin: auto;
}

.review-container {
    min-height: 350px;
}

.review-heading-1{
  font-size: 40px;
  color:black;
}
 .review-heading-2{
       font-size: 18px;
      color:#656565;
   font-weight: 400;
   margin-bottom: 20px;
      }
@media (max-width: 950px) {
.review-text {
text-align: center;
}

.img-review {
    align-self: center;
}

.review-rating {
justify-content: center;
}
.review-container {
    min-height: 500px;
}
.review-2 {
        margin-left: 0 !important;
    }
.review-heading-1 {
  font-size: 24px;
        color: black;
        line-height: 1.25;
}
  .review-heading-2{
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 20px;
    color:#656565;
  }
  .review-heading {
    font-size: 18px;
    font-weight: 600;
  }
  .review-text {
    font-size: 16px;
    font-weight: 400;
  }
  .review-name {
    font-size: 16px;
    weight: 600;
    margin-top: 10px;
  }
  img.img-review {
    width: 300px;
    height: 400px;
}
}
       @media (min-width: 768px) {
            .md\:w-1\/2 {
                width: 100%;
            }
        }
        .carousel-item {
            transition: transform 0.5s ease-in-out;
        }
        .flex {
            display: flex;
        }
        .flex-col {
            flex-direction: column;
        }
        .md\:flex-row {
            flex-direction: column;
        }
        @media (min-width: 950px) {
            .md\:flex-row {
                flex-direction: row;
            }
        }
        .items-start {
            align-items: flex-start;
        }
        .w-24 {
            width: 6rem;
        }
        .h-24 {
            height: 6rem;
        }
        .rounded-lg {
            border-radius: 0.5rem;
        }
        .mb-4 {
            margin-bottom: 1rem;
        }
        .md\:mb-0 {
            margin-bottom: 1rem;
        }
        @media (min-width: 768px) {
            .md\:mb-0 {
                margin-bottom: 0;
            }
        }
        .md\:mr-4 {
            margin-right: 0;
        }
        @media (min-width: 768px) {
            .md\:mr-4 {
                margin-right: 1rem;
            }
        }
        .text-xl {
            font-size: 1.25rem;
            line-height: 1.75rem;
        }
        .font-semibold {
            font-weight: 600;
        }
        .text-gray-700 {
            color: #4a5568;
        }
        .font-bold {
            font-weight: 700;
        }
        .mt-8 {
            margin-top: 2rem;
        }
        .space-x-2 > * + * {
            margin-left: 0.5rem;
        }
        .w-3 {
            width: 0.5rem;
        }
        .h-3 {
            height: 0.5rem;
        }
        .bg-gray-400 {
            background-color: #cbd5e0;
        }
        .bg-gray-800 {
            background-color: #2d3748;
        }
        .rounded-full {
            border-radius: 9999px;
        }
        .mt-4 {
            margin-top: 1rem;
        }
        .w-10 {
            width: 2.5rem;
        }
        .h-10 {
            height: 2.5rem;
        }
        .bg-white {
            background-color: #ffffff;
        }
        .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .items-center {
            align-items: center;
        }
        .justify-center {
            justify-content: center;
        }
    {% endstyle %}
    <div class="review-section-container" id="review-section">
      <div class="main-container">
        <div class="text-3xl font-bold mb-2 review-heading-1">
            {{ section.settings.heading_1 }} 
            {{ section.settings.heading_2 }}
        </div>
        <div class="text-gray-600 mb-8 review-heading-2">
            {{ section.settings.heading_3 }}
        </div>
        <div class="relative carousel-container">
		<div class="review-container">
            <div id="carousel" class="carousel-inner space-x-4">
                <div class="bg-white rounded-lg shadow-lg p-6 w-full md:w-1/2 carousel-item">
                    <div class="flex flex-col md:flex-row items-start review-content">
                        <img class="img-review" alt="Person's feet on a grounding mat" class="w-24 h-24 rounded-lg mb-4 md:mb-0 md:mr-4" height="100" src="{{ section.settings.review_image_1 | img_url: '500x500' }}" width="100"/>
                        <div class="review-text">
                            <div class="flex items-center mb-2 review-rating">
                                <img src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg">
                            </div>
                            <div class="text-xl font-semibold mb-2 review-heading">
                               {{ section.settings.review_title_1 }} 
                            </div>
                            <div class="text-gray-700 mb-4 review-text">
                                {{ section.settings.review_text_1 }} 
                            </div>
                            <div class="font-bold review-name">
                                {{ section.settings.reviewer_name_1 }} 
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6 w-full md:w-1/2 carousel-item review-2">
                    <div class="flex flex-col md:flex-row items-start review-content">
                        <img class="img-review" alt="Person using a grounding mat under a desk" class="w-24 h-24 rounded-lg mb-4 md:mb-0 md:mr-4" height="100" src="{{ section.settings.review_image_2 | img_url: '500x500' }}" width="100"/>
                        <div class="review-text">
                            <div class="flex items-center mb-2 review-rating">
                                <img src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg">
                            </div>
                             <div class="text-xl font-semibold mb-2 review-heading">
                                {{ section.settings.review_title_2 }} 
                            </div>
                            <div class="text-gray-700 mb-4 review-text">
                                {{ section.settings.review_text_2 }} 
                            </div>
                            <div class="font-bold review-name">
                               {{ section.settings.reviewer_name_2 }} 
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6 w-full md:w-1/2 carousel-item hidden review-2">
                    <div class="flex flex-col md:flex-row items-start review-content">
                        <img class="img-review" alt="Person using a grounding mat while reading" class="w-24 h-24 rounded-lg mb-4 md:mb-0 md:mr-4" height="100" src="{{ section.settings.review_image_3 | img_url: '500x500' }}" width="100"/>
                        <div class="review-text">
                           <div class="flex items-center mb-2 review-rating">
                                <img src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg">
                            </div>
                             <div class="text-xl font-semibold mb-2 review-heading">
                               {{ section.settings.review_title_3 }} 
                            </div>
                            <div class="text-gray-700 mb-4 review-text">
                                {{ section.settings.review_text_3 }} 
                            </div>
                            <div class="font-bold review-name">
                                {{ section.settings.reviewer_name_3 }} 
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6 w-full md:w-1/2 carousel-item hidden review-2">
                    <div class="flex flex-col md:flex-row items-start review-content">
                        <img class="img-review" alt="Person using a grounding mat while meditating" class="w-24 h-24 rounded-lg mb-4 md:mb-0 md:mr-4" height="100" src="{{ section.settings.review_image_4 | img_url: '500x500' }}" width="100"/>
                        <div class="review-text">
                            <div class="flex items-center mb-2 review-rating">
                                <img src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg">
                            </div>
                             <div class="text-xl font-semibold mb-2 review-heading">
                                {{ section.settings.review_title_4 }} 
                            </div>
                            <div class="text-gray-700 mb-4 review-text">
                                {{ section.settings.review_text_4 }} 
                            </div>
                            <div class="font-bold review-name">
                                {{ section.settings.reviewer_name_4 }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6 w-full md:w-1/2 carousel-item hidden review-2">
                    <div class="flex flex-col md:flex-row items-start review-content">
                        <img class="img-review" alt="Person using a grounding mat while working out" class="w-24 h-24 rounded-lg mb-4 md:mb-0 md:mr-4" height="100" src="{{ section.settings.review_image_5 | img_url: '500x500' }}" width="100"/>
                        <div class="review-text">
                            <div class="flex items-center mb-2 review-rating">
                                <img src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg">
                            </div>
                             <div class="text-xl font-semibold mb-2 review-heading">
                                {{ section.settings.review_title_5 }}
                            </div>
                            <div class="text-gray-700 mb-4 review-text">
                                {{ section.settings.review_text_5 }} 
                            </div>
                            <div class="font-bold review-name">
                                {{ section.settings.reviewer_name_5 }} 
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-white rounded-lg shadow-lg p-6 w-full md:w-1/2 carousel-item hidden review-2">
                    <div class="flex flex-col md:flex-row items-start review-content">
                        <img class="img-review" alt="Person using a grounding mat while working on a laptop" class="w-24 h-24 rounded-lg mb-4 md:mb-0 md:mr-4" height="100" src="{{ section.settings.review_image_6 | img_url: '500x500' }}" width="100"/>
                        <div class="review-text">
                            <div class="flex items-center mb-2 review-rating">
                                <img src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/ICzMh0OOhGQmlDJd/8f79297654be65db2f4ce72a79882262162d67dc.svg">
                            </div>
                             <div class="text-xl font-semibold mb-2 review-heading">
                                {{ section.settings.review_title_6 }} 
                            </div>
                            <div class="text-gray-700 mb-4 review-text">
                                {{ section.settings.review_text_6 }} 
                            </div>
                            <div class="font-bold review-name">
                                {{ section.settings.reviewer_name_6 }} 
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
		</div>
			<div class="review-navigator">
            <div class="flex justify-center mt-8 space-x-2" id="indicators">
                <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                <div class="w-3 h-3 bg-gray-400 rounded-full"></div>
                <div class="w-3 h-3 bg-gray-400 rounded-full hidden"></div>
                <div class="w-3 h-3 bg-gray-400 rounded-full hidden"></div>
                <div class="w-3 h-3 bg-gray-400 rounded-full hidden"></div>
            </div>
            <div class="flex justify-center mt-4 space-x-4 nav-button">
                <button id="prev" class="w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <button id="next" class="w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center">
                    <i class="fas fa-arrow-right"></i>
                </button>
            </div>
			</div>
        </div>
    </div>
    <script>
    const carouselItems = document.querySelectorAll('#carousel > div');
    const indicators = document.querySelectorAll('#indicators > div');
    const prevButton = document.getElementById('prev');
    const nextButton = document.getElementById('next');
    let currentIndex = 0;

    function showItems(index) {
        const isMobile = window.innerWidth < 640;
        const itemsPerPage = isMobile ? 1 : 2;
        const offset = index * itemsPerPage;
        const end = offset + itemsPerPage;

        carouselItems.forEach((item, i) => {
            item.classList.toggle('hidden', i < offset || i >= end);
        });

        updateIndicators(index);
    }

    function updateIndicators(index) {
        const isMobile = window.innerWidth < 640;
        const itemsPerPage = isMobile ? 1 : 2;
        const activeIndex = Math.floor(index); // Corrected the calculation

        indicators.forEach((indicator, i) => {
            indicator.classList.toggle('bg-gray-400', i !== activeIndex);
            indicator.classList.toggle('bg-gray-800', i === activeIndex);
            indicator.classList.toggle('hidden', isMobile ? i >= 6 : i >= 3);
        });
    }

    prevButton.addEventListener('click', () => {
        const isMobile = window.innerWidth < 640;
        const itemsPerPage = isMobile ? 1 : 2;
        const totalSlides = Math.ceil(carouselItems.length / itemsPerPage);
        currentIndex = (currentIndex - 1 + totalSlides) % totalSlides;
        showItems(currentIndex);
    });

    nextButton.addEventListener('click', () => {
        const isMobile = window.innerWidth < 640;
        const itemsPerPage = isMobile ? 1 : 2;
        const totalSlides = Math.ceil(carouselItems.length / itemsPerPage);
        currentIndex = (currentIndex + 1) % totalSlides;
        showItems(currentIndex);
    });

    showItems(currentIndex);

    window.addEventListener('resize', () => {
        showItems(currentIndex);
    });
</script>

{% schema %}
  {
  "name": "Custom Review Slider",
  "settings": [
    {
      "type": "richtext",
      "id": "heading_1",
      "label": "Heading 1"
    },
    {
      "type": "richtext",
      "id": "heading_2",
      "label": "Heading 2"
    },
    {
      "type": "richtext",
      "id": "heading_3",
      "label": "Heading 3"
    },
    {
      "type": "image_picker",
      "id": "review_image_1",
      "label": "Review Image 1"
    },
    {
      "type": "richtext",
      "id": "review_title_1",
      "label": "Review Title 1"
    },
    {
      "type": "richtext",
      "id": "review_text_1",
      "label": "Review Text 1"
    },
    {
      "type": "richtext",
      "id": "reviewer_name_1",
      "label": "Reviewer Name 1"
    },
    {
      "type": "image_picker",
      "id": "review_image_2",
      "label": "Review Image 2"
    },
    {
      "type": "richtext",
      "id": "review_title_2",
      "label": "Review Title 2"
    },
    {
      "type": "richtext",
      "id": "review_text_2",
      "label": "Review Text 2"
    },
    {
      "type": "richtext",
      "id": "reviewer_name_2",
      "label": "Reviewer Name 2"
    },
    {
      "type": "image_picker",
      "id": "review_image_3",
      "label": "Review Image 3"
    },
    {
      "type": "richtext",
      "id": "review_title_3",
      "label": "Review Title 3"
    },
    {
      "type": "richtext",
      "id": "review_text_3",
      "label": "Review Text 3"
    },
    {
      "type": "richtext",
      "id": "reviewer_name_3",
      "label": "Reviewer Name 3"
    },
    {
      "type": "image_picker",
      "id": "review_image_4",
      "label": "Review Image 4"
    },
    {
      "type": "richtext",
      "id": "review_title_4",
      "label": "Review Title 4"
    },
    {
      "type": "richtext",
      "id": "review_text_4",
      "label": "Review Text 4"
    },
    {
      "type": "richtext",
      "id": "reviewer_name_4",
      "label": "Reviewer Name 4"
    },
    {
      "type": "image_picker",
      "id": "review_image_5",
      "label": "Review Image 5"
    },
    {
      "type": "richtext",
      "id": "review_title_5",
      "label": "Review Title 5"
    },
    {
      "type": "richtext",
      "id": "review_text_5",
      "label": "Review Text 5"
    },
    {
      "type": "richtext",
      "id": "reviewer_name_5",
      "label": "Reviewer Name 5"
    },
    {
      "type": "image_picker",
      "id": "review_image_6",
      "label": "Review Image 6"
    },
    {
      "type": "richtext",
      "id": "review_title_6",
      "label": "Review Title 6"
    },
    {
      "type": "richtext",
      "id": "review_text_6",
      "label": "Review Text 6"
    },
    {
      "type": "richtext",
      "id": "reviewer_name_6",
      "label": "Reviewer Name 6"
    }
  ],
  "presets": [
    {
      "name": "Custom Review Slider"
    }
  ]
}  
{% endschema %}