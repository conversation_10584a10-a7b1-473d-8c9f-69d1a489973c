{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant iyuwg31eEjQgPyYln" data-instant-id="yuwg31eEjQgPyYln" data-instant-version="3.0.3" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-yuwg31eEjQgPyYln.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="i83pkD3pV5UAJZ1Ma">
    {%- liquid
      assign loading = 'eager'
      assign fetchpriority = 'auto'
      if section.location == 'footer'
        assign loading = 'lazy'
      elsif section.location == 'header'
        assign fetchpriority = 'high'
      elsif section.location == 'template'
        if section.index == 1
          assign fetchpriority = 'high'
        elsif section.index > 2
          assign loading = 'lazy'
        endif
      endif
    -%}
    <div class="iJTt0l96JP715jHGw" data-instant-type="container" id="iJTt0l96JP715jHGw">
      <div class="i5JPYPMH3OqsQb16L" data-instant-type="container">
        <div class="iFAih7o4kWfDBWlyp" data-instant-type="container">
          <div class="iXJtjBZ48OoOIlcAr" data-instant-type="container">
            <div class="i0vkXtL4oyQWN5tvs" data-instant-type="container">
              <div data-instant-type="image" class="isvdpXBtjcygxopSp">
                {% if section.settings.image_svdpXBtjcygxopSp and section.settings.image_svdpXBtjcygxopSp != blank %}
                  {{ section.settings.image_svdpXBtjcygxopSp | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_svdpXBtjcygxopSp.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                {% else %}
                  <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/momqCYpeBSrZs7Db/0a5caeffd3a1a87bdb5f5241b5ac3196ef21dbe9.svg" width="40" height="40" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                {% endif %}
              </div>
            </div>
            <div class="iBW43UbKGt4VNUfgv" data-instant-type="container">
              <div data-instant-type="text" class="instant-rich-text iSEsvVE1jdVnTqf5k">
                <div>{{ section.settings.text_SEsvVE1jdVnTqf5k }}</div>
              </div>
              <div data-instant-type="text" class="instant-rich-text iPOojFpMynBD6X45v">
                <div>{{ section.settings.text_POojFpMynBD6X45v }}</div>
              </div>
            </div>
          </div>
          <div class="iJ0hKXiflha1zcCxQ" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text iL03OiUoAxieAWorH">
              <div>{{ section.settings.text_L03OiUoAxieAWorH }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text iVw0JNIfkxUpgYr2G maison-delivery-estimate">
              <div>{{ section.settings.text_Vw0JNIfkxUpgYr2G }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.3",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison PDP Top Banner",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "image_picker",
      "id": "image_svdpXBtjcygxopSp",
      "label": "Image"
    },
    {
      "type": "richtext",
      "id": "text_SEsvVE1jdVnTqf5k",
      "label": "Heading",
      "default": "<p>50% OFF TODAY (NEW PRODUCT LAUNCH DEAL!)</p>"
    },
    {
      "type": "richtext",
      "id": "text_POojFpMynBD6X45v",
      "label": "Text",
      "default": "<p>Just add the shoes below to your cart. Discount automatically applied at checkout!</p>"
    },
    {
      "type": "richtext",
      "id": "text_L03OiUoAxieAWorH",
      "label": "Button",
      "default": "<p>ENDS IN:</p>"
    },
    {
      "type": "richtext",
      "id": "text_Vw0JNIfkxUpgYr2G",
      "label": "Button",
      "default": "<p>1 dAY: 10 hOURS: 9 mINUTES: 11 sECONDS</p>"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison PDP Top Banner",
      "settings": {
        "text_SEsvVE1jdVnTqf5k": "<p>50% OFF TODAY (NEW PRODUCT LAUNCH DEAL!)</p>",
        "text_POojFpMynBD6X45v": "<p>Just add the shoes below to your cart. Discount automatically applied at checkout!</p>",
        "text_L03OiUoAxieAWorH": "<p>ENDS IN:</p>",
        "text_Vw0JNIfkxUpgYr2G": "<p>1 dAY: 10 hOURS: 9 mINUTES: 11 sECONDS</p>"
      }
    }
  ]
}
{% endschema %}
