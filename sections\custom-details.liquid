<section class="detail-section">
  <div class="container">
<div class="header-section">
  <span class="header-text {{ section.settings.header_size }} ">{{ section.settings.header_text }}</span>
</div>
    <div class="detail-right">
    {% for block in section.blocks %}
      <div class="detail-item">
        <!-- detail main button with toggle action -->
        <button class="detail-main" onclick="toggledetail(this)">
          {{ block.settings.main }}
          <span class="toggle-icon">+</span>
        </button>
        <!-- detail answer, initially hidden -->
        <div class="detail-answer" style="display: none;">
          <span>{{ block.settings.answer }}</span>
        </div>
      </div>
    {% endfor %}
  </div>
</div>
</section>

<style>
  .header-section {
    margin-top: 20px;
    margin-bottom: 20px;
  }
  /* General Styles */
  .detail-section {
    padding: 0px 20px;
    max-width: 1300px;
    margin: 0 auto;
  }

  .detail-right {
    flex: 1.5;
    min-width: 300px;
    border-radius: 10px;
    padding: 5px 20px;
  }

  .detail-item {
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
  }

  .detail-item button {
    background: none;
    border: none;
    text-align: left;
    font-size: 16px;
    font-weight: bold;
    width: 100%;
    padding: 10px 0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
  }

  .detail-item button:focus {
    outline: none;
  }

  .detail-content {
    display: none;
    font-size: 14px;
    color: #555;
    margin-top: 10px;
  }

  .detail-answer ul {
    display: block;
    padding-left: 40px;
    list-style: disc;
}
  .detail-answer ol {
    display: block;
    padding-left: 40px;
    list-style: decimal;
}

  /* Responsive Styles */
  @media (max-width: 768px) {
    .detail-section {
      flex-direction: column;
    }
  }
  .detail-container {
  display: flex;
  gap: 20px;
}

.detail-left {
  flex: 1;
}

.detail-right {
  flex: 2;
}

.detail-item {
  border-bottom: 1px solid #ddd;
  padding: 5px 0;
}

.detail-main {
  background: none;
  border: none;
  font-size: 16px;
  font-weight: bold;
  text-align: left;
  width: 100%;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-main:focus {
  outline: none;
}

.toggle-icon {
  font-size: 18px;
  font-weight: bold;
  margin-left: 10px;
}

.detail-answer {
  font-size: 18px;
  margin-top: 10px;
  display: none; /* Initially hidden */
}
.header-x-large {
  font-size:32px;
}
.header-large {
  font-size: 24px;
}
.header-x-medium {
  font-size: 18.72px;
}  
  .header-medium {
  font-size: 16px;
}  
.header-x-small {
  font-size: 13.28px;
}  
  .header-small {
  font-size: 10.72px;
} 
</style>

<script>
  function toggledetail(button) {
    const answer = button.nextElementSibling; // Get the adjacent detail answer div
    const icon = button.querySelector(".toggle-icon"); // Get the icon inside the button
    
    // Toggle visibility of the answer div and change the icon
    if (answer.style.display === "none" || !answer.style.display) {
      answer.style.display = "block"; // Show the answer
      icon.textContent = "-"; // Change the icon to minus
    } else {
      answer.style.display = "none"; // Hide the answer
      icon.textContent = "+"; // Change the icon to plus
    }
  }
</script>
{% schema %}
  {
  "name": "Custom Detail",
  "settings": [
{
          "type": "richtext",
          "id": "header_text",
          "label": "Header text"
        },
    {
      "type": "select",
      "id": "header_size",
      "label": "Header size",
      "options": [
        {
          "value": "header-x-large",
          "label": "X-Large"
        },
        {
          "value": "header-large",
          "label": "Large"
        },
        {
          "value": "header-x-medium",
          "label": "X-Medium"
        },
        {
          "value": "header-medium",
          "label": "Medium"
        },
        {
          "value": "header-x-small",
          "label": "X-Small"
        },
        {
          "value": "header-small",
          "label": "Small"
        }
      ],
      "default": "header-x-large"
    }
  ],
  "blocks": [
    {
      "name": "Detail",
      "type": "detail",
      "settings": [
        {
          "type": "text",
          "id": "main",
          "label": "main"
        },
        {
          "type": "richtext",
          "id": "answer",
          "label": "Answer"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Custom Detail"
    }
  ]
}  
{% endschema %}

{% stylesheet %}
{% endstylesheet %}

{% javascript %}
{% endjavascript %}