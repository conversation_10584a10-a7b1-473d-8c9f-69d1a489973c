.custom-marquee { 
  --_fade: var(--cm-fade, 48px);
  background: linear-gradient(90deg, rgb(var(--cm-bg-start)), rgb(var(--cm-bg-end)));
  color: rgb(var(--cm-text));
}

.custom-marquee .cm-viewport {
  position: relative;
  overflow: hidden;
}

/* Edge blur fades */
.custom-marquee .cm-viewport {
  -webkit-mask-image: linear-gradient(90deg,
    transparent 0%,
    black var(--_fade),
    black calc(100% - var(--_fade)),
    transparent 100%);
  mask-image: linear-gradient(90deg,
    transparent 0%,
    black var(--_fade),
    black calc(100% - var(--_fade)),
    transparent 100%);
}

/* Track */
.custom-marquee .cm-track {
  display: grid;
  grid: auto / auto-flow max-content; /* single row with as-many columns as needed */
  justify-content: start;
  overflow: hidden;
}
.custom-marquee .cm-row {
  display: grid;
  grid: auto / auto-flow max-content;
  align-items: center;
  gap: 20px;
  padding: 12px 8px;
}

@media (prefers-reduced-motion: no-preference) {
  .custom-marquee .cm-row {
    animation: translateFull var(--marquee-animation-duration, 0s) linear infinite;
  }
}

/* Items */
.custom-marquee .cm-item {
  display: inline-flex;
  gap: calc(var(--cm-font-size) * 0.5);
  align-items: center;
  white-space: nowrap;
}
.custom-marquee .cm-icon {
  height: calc(var(--cm-font-size) * 1.2);
  width: auto;
  filter: brightness(0) invert(1); /* make white */
}
.custom-marquee .cm-label {
  color: rgb(var(--cm-text));
  opacity: .95;
  font-size: var(--cm-font-size);
  line-height: 1.2;
  margin: 0;
  font-weight: 800;
  letter-spacing: .2px;
}
.custom-marquee .cm-sep {
  width: calc(var(--cm-font-size) * 0.8);
  height: calc(var(--cm-font-size) * 0.8);
  border-radius: 999px;
  background: transparent;
  display: inline-block;
  margin-inline: calc(var(--cm-font-size) * 0.7);
}

/* Motion preference */
@media (prefers-reduced-motion: reduce) {
  .custom-marquee .cm-track span { animation: none !important; }
}

