document.addEventListener("DOMContentLoaded", function() {
    // Check for the country flag class to detect the user's region
    var countryFlag = document.querySelector('.country-flags');
    console.log('Country flag element:', countryFlag);

    // Get the options for USA and EU
    var usaOption = document.querySelector('input[value*="US"]');
    var euOption = document.querySelector('input[value*="EU"]');
    var ukOption = document.querySelector('input[value*="UK"]');
    var auOption = document.querySelector('input[value*="AUS"]');
  
    console.log('USA Option:', usaOption);
    console.log('EU Option:', euOption);
    console.log('UK Option:', ukOption);
  console.log('AU Option:', auOption);
  
    // Make sure the country flag element exists
    if (countryFlag) {
        if (countryFlag.classList.contains('country-flags--US')) {
            console.log('User is from the US');
            // If the country is US, use default order
            reorderOptions('US');
        } else if (countryFlag.classList.contains('country-flags--FR') || countryFlag.classList.contains('country-flags--DE') || countryFlag.classList.contains('country-flags--ES')) {
            console.log('User is from the EU');
            // If the country is EU, reorder the options with EU first
            reorderOptions('EU');
        } else if (countryFlag.classList.contains('country-flags--AU')) {
          console.log('User is from the AUS');
          reorderOptions('AUS');
        } else if (countryFlag.classList.contains('country-flags--GB')) {
          console.log('User is from the UK');
          reorderOptions('UK');
        }
        }
    

    function reorderOptions(region) {
    console.log('Reordering options based on region:', region);

    var usaWrapper = usaOption ? usaOption.closest('div') : null;
    var euWrapper = euOption ? euOption.closest('div') : null;
    var ukWrapper = ukOption ? ukOption.closest('div') : null;
    var auWrapper = auOption ? auOption.closest('div') : null;

    if (usaWrapper && euWrapper && ukWrapper && auWrapper) {
        const parent = usaWrapper.parentNode;

        if (region === 'EU') {
            // Append the options in EU order: EU, USA, UK, AU
            parent.appendChild(euWrapper);
            parent.appendChild(usaWrapper);
            parent.appendChild(ukWrapper);
            parent.appendChild(auWrapper);
            console.log('Reordered for EU: EU -> USA -> UK -> AU');
        } else if (region === 'AUS') {
            parent.appendChild(auWrapper);
            parent.appendChild(usaWrapper);
            parent.appendChild(euWrapper);
            parent.appendChild(ukWrapper);
        } else if (region === 'UK') {
          parent.appendChild(ukWrapper); 
            parent.appendChild(usaWrapper);
            parent.appendChild(euWrapper);
            parent.appendChild(auWrapper);
        }else {
            // Default order: USA, EU, UK, AU
            console.log('Reordered for default: USA -> EU -> UK -> AU');
        }
    }
}


});

