{% style %}
  .section-{{ section.id }} {
      padding-top: {{section.settings.padding_top}}px;
      padding-bottom: {{section.settings.padding_bottom}}px;
      background-color: {{section.settings.bg_color}};
  }
  .section-{{ section.id }} .custom-sponsor.header span {
     color: {{section.settings.text_color}};
  }
  .wrapper.custom-full-width {
    max-width: 100%;
}
  .custom-sponsor .wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 80px;
}
  .custom-sponsor.body {
    display: flex;
    align-items: center;
    gap: 80px;
}
  .sponsor-img {
    display: flex;
}
  .sponsor-img img {
    width: 100%;
    max-height: 100px;
}
  @media(max-width: 767px){
    .section-{{ section.id }} {
      padding-top:15px;
      padding-bottom: calc({{section.settings.padding_bottom}}px/2);
    }
    .custom-sponsor.body {
    display: flex;
    align-items: center;
    gap: 45px;
}
    .custom-sponsor .wrapper {
    gap: 0px;
    flex-direction: column;
}
    .sponsor-img {
    max-width: 50px;
} 
  }
{% endstyle %}

<div class="custom-sponsor section-{{ section.id }} {{ section.settings.custom_class }}">
  <div class="wrapper{% if section.settings.full_width %} custom-full-width{% endif %}">
    <div class="custom-sponsor">
      <span>{{ section.settings.text }}</span>
    </div>
    <div class="custom-sponsor body">
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          
          {%- when 'sponsor_image' -%}
            {%- if block.settings.sponsor_image != blank -%}
            <div class="sponsor-img">
              <img src="{{ block.settings.sponsor_image | img_url: 'master' }}">
            </div>
            {%- endif -%}

        {%- endcase -%}
      {%- endfor -%}
    </div>
  </div>
</div>
{% schema %}
  {
    "name": "Custom: Sponsor",
    "settings": [
      {
        "type": "text",
        "id": "text",
        "label": "Text",
        "default": "Sponsored"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text Color",
        "default": "#000000"
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 1,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Padding Top",
        "default": 50
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "min": 1,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Padding Bottom",
        "default": 50
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full Width",
        "default": false
      },
      {
        "type": "text",
        "id": "custom_class",
        "label": "Custom Class"
      }
    ],
    "blocks": [
       {
         "name": "Sponsor Image",
         "type": "sponsor_image",
         "settings": [
           {
              "type": "image_picker",
              "id": "sponsor_image",
              "label": "Sponsor Image"
           }
         ]
       }
    ],
    "presets": [
      {
        "name": "Custom: Sponsor"
      }
    ]
  }
{% endschema %}