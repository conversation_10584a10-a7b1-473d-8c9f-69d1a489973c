<style>
  .custom-tracking {
    max-width: 1200px;
    margin: 0 auto;
    place-items: center;
    padding-top: 30px;
  }
  .tracking-input {
    padding: 10px;
    width: 300px;
    margin-right: 10px;
    border-width: 1px;
    border-radius: 10px;
    background: #f6c0b20f;
  }
  .tracking-button {
    cursor: pointer;
    border-width: 1px;
    padding: 10px 25px;
    border-radius: 25px;
    background: #d6684b;
    font-weight: 600;
    color: white;
    font-size: 1rem;
  }
  .tracking-results {
    margin-top: 20px;
    text-align: left;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-left: 20px;
  }
  .tracking-entry {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 1px solid #ddd;
    margin-bottom: 10px;
    background: #f6c0b20f;
    border-radius: 10px;
    position: relative;
    width: 800px;
  }
  .tracking-timeline {
    display: flex;
    flex-direction: column;
    position: relative;
  }
  .tracking-indicator {
  width: 30px; /* Adjust size as needed */
  height: 30px;
  position: absolute;
  left: -39px;
  z-index: 1;
}
  .tracking-line {
    position: absolute;
    left: -25px;
    width: 2px;
    background-color: #ddd;
    height: 100%;
  }
  .loader {
    display: none;
    margin-top: 5px;
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #eb6516;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: auto;
    margin-right: auto;
  }
  .load-text{
    margin-top: 5px;
    display: none;
    justify-self: center;
  }
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
  .track-heading {
    font-size: 30px;
    font-weight: 600;
  }
  .tracking-details {
    font-size: 25px;
    font-weight: 600;
    padding-bottom: 10px;
  }
  .track-main {
    margin-top: 20px;
  }
.tracking-date{
    color: darkgray;
  }
.track-body {
    width: 800px;
    justify-items: center;
    padding: 20px 40px 60px 40px;
    border: 1px solid #e3e3e3;
    border-radius: 10px;
    margin-left: 20px;
    margin-right: 20px;
}
  .tracking-alert {
  color: #d32f2f;
  font-size: 14px;
  margin-top: 10px;
  display: none; /* hidden by default */
}
  @media (max-width: 875px) {
  .tracking-entry, .track-body {
  max-width: 600px;
  }
  }
  @media (max-width: 685px) {
  .tracking-entry, .track-body {
  max-width: 400px;
  }
  .track-main {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  .tracking-button {
    width: 300px;
    text-align: center;
  }
  }
    @media (max-width: 500px) {
  .tracking-entry, .track-body {
  max-width: 300px;
  }
  .tracking-button, .tracking-input {
    width: 250px;
  }
 .track-heading {
   font-size: 25px;
    text-align: center;
    display: block;
 }    
}

</style>
<div class="custom-tracking">
<div class="track-body">
  <span class="track-heading">{{ section.settings.header }}</span>
  <div class="track-main">
    <input class="tracking-input" type="text" id="trackingNumber" placeholder="Enter tracking number...">
    <button class="tracking-button" onclick="fetchTrackingInfo()">Track</button>
    <div class="tracking-alert" id="trackingAlert">Please enter your tracking number</div>
  </div>
</div>
    <div class="load-text" id="load-text"> Loading please wait...</div>
    <div class="loader" id="loader"></div>

  <div class="tracking-results" id="results"></div>
<script>
  let isRequestInProgress = false;
  let requestQueue = [];

  async function fetchTrackingInfo() {
     const trackingNumber = document.getElementById('trackingNumber').value.trim();
  const alertBox = document.getElementById('trackingAlert');

  if (!trackingNumber) {
    alertBox.style.display = 'block';
    return;
  } else {
    alertBox.style.display = 'none';
  }

    const task = () => handleTrackingRequest(trackingNumber);

    if (isRequestInProgress) {
      requestQueue.push(task);
      alert('Another user is currently tracking. You’ll be queued for 30 seconds.');
    } else {
      task();
    }
  }

  async function handleTrackingRequest(trackingNumber) {
    isRequestInProgress = true;
    document.getElementById('load-text').style.display = 'block';
    document.getElementById('loader').style.display = 'block';
    document.getElementById('results').innerHTML = '';

    try {
      const response = await fetch(`https://render-playwright-1.onrender.com/api/track?num=${trackingNumber}`);
      const data = await response.json();
      document.getElementById('load-text').style.display = 'none';
      document.getElementById('loader').style.display = 'none';

      if (data.status !== 'success') {
        document.getElementById('results').innerHTML = '<p>Tracking info not found.</p>';
        return;
      }

      let output = '<span class="tracking-details">Tracking Details:</span><div class="tracking-timeline">';
      const excludedWords = "{{ section.settings.excluded_keywords }}".split(',');

      data.tracking_details.forEach((detail) => {
        let cleanedStatus = excludedWords.reduce((status, word) => 
          status.replace(new RegExp(word, 'gi'), ''), detail.status).trim();
        let cleanedCourier = excludedWords.reduce((courier, word) => 
          courier.replace(new RegExp(word, 'gi'), ''), detail.courier).trim();

        output += `<div class="tracking-entry">
                    <img class="tracking-indicator" src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/tracking-ico.webp?v=1743590463" alt="Tracking Status">
                    <div class="tracking-line" style="height: 120%;"></div>
                    <div class="tracking-info">
                      <div class="tracking-date">${detail.date}:</div>
                      <div class="tracking-status">${cleanedStatus}</div>
                    </div>
                  </div>`;
      });
      output += '</div>';
      document.getElementById('results').innerHTML = output || '<p>No relevant tracking updates available.</p>';
    } catch (error) {
      document.getElementById('loader').style.display = 'none';
      document.getElementById('results').innerHTML = '<p>Error fetching tracking data. Please try again later!</p>';
    }

    // Wait 30 seconds before releasing the lock and serving next request
    setTimeout(() => {
      isRequestInProgress = false;
      if (requestQueue.length > 0) {
        const nextRequest = requestQueue.shift();
        nextRequest();
      }
    }, 30000); // 30 seconds
  }
</script>

</div>
{% schema %}
  {
  "name": "Custom Tracking",
  "settings": [
    {
      "type": "text",
      "id": "header",
      "label": "Header"
    },
    {
      "type": "textarea",
      "id": "excluded_keywords",
      "label": "Excluded Keywords (Separate using Comma)"
    }
  ],
  "presets": [
    {
      "name": "Custom Tracking"
    }
  ]
}  
{% endschema %}