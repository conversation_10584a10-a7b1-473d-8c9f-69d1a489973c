{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
PRODUCT RATING
----------------------------------------------------------------------------------------------------------------------

This component generates a review badge from the product metafields information.

********************************************
Supported variables
********************************************

* product: the product from which reviews are extracted
* show_empty: if set to true, the theme shows a 0.0 if there are no rating yet
* show_placeholder: if set to true, show random value (useful for the product card placeholder)
* display_mode: either "rating" (e.g.: 3.5) or "count" (e.g.: 4 reviews). Default to "rating" if none is passed
* class: extra class to add
{%- endcomment -%}

{%- assign rating_max = 5 -%}

{%- if product.metafields.reviews.rating.value != blank -%}
  {%- assign rating_value = product.metafields.reviews.rating.value.rating | round: 1 -%}
  {%- assign rating_count = product.metafields.reviews.rating_count.value -%}
  {%- assign rating_max = product.metafields.reviews.rating.value.scale_max -%}
{%- elsif show_empty -%}
  {%- assign rating_value = 0.0 -%}
  {%- assign rating_count = 0 -%}
{%- elsif show_placeholder -%}
  {%- assign rating_value = 4.5 -%}
  {%- assign rating_count = 2 -%}
{%- else -%}
  {%- assign hide_rating = true -%}
{%- endif -%}

{%- unless hide_rating -%}
  {%- if request.page_type == 'product' and block != blank -%}
    {%- assign is_main_product = true -%}
  {%- endif -%}

  <a href="{% unless is_main_product %}{{ product.url }}{% endunless %}#shopify-product-reviews" class="rating {{ class }}" title="{{ 'product.rating_count' | t: count: rating_count }}" {{ block.shopify_attributes }}>
    {%- if display_mode == 'count' -%}
      <span class="text-sm">{{- 'product.rating_count' | t: count: rating_count -}}</span>
    {%- else -%}
      <span class="text-sm">{{ rating_value }}</span>
    {%- endif -%}

    <div class="rating__stars" role="img" aria-label="{{ 'general.rating.info' | t: rating_value: rating_value, rating_max: rating_max }}">
      {%- render 'icon' with 'rating-star', width: 12, height: 12, class: 'rating__star' -%}
    </div>
  </a>
{%- endunless -%}