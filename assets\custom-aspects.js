// Custom Aspects section modal logic (scoped to .custom-aspects instances)
(function(){
  function initSection(root){
    if(!root) return;
    const wrappers = root.querySelectorAll('[data-nutrition]');
    wrappers.forEach((wrap)=>{
      const openBtn = wrap.querySelector('[data-nutrition-open]');
      const backdrop = wrap.querySelector('[data-modal-backdrop]');
      const closeBtn = wrap.querySelector('[data-modal-close]');
      if(!openBtn || !backdrop || !closeBtn) return;
      const open = ()=>{ backdrop.setAttribute('aria-hidden','false'); closeBtn.focus({preventScroll:true}); };
      const close = ()=>{ backdrop.setAttribute('aria-hidden','true'); openBtn.focus({preventScroll:true}); };
      openBtn.addEventListener('click', open);
      closeBtn.addEventListener('click', close);
      backdrop.addEventListener('click', (e)=>{ if(e.target === backdrop) close(); });
      document.addEventListener('keydown', (e)=>{ if(e.key === 'Escape' && backdrop.getAttribute('aria-hidden') === 'false') close(); });
    });
  }
  if(document.readyState === 'loading'){
    document.addEventListener('DOMContentLoaded',()=>{
      document.querySelectorAll('.custom-aspects').forEach(initSection);
    });
  } else {
    document.querySelectorAll('.custom-aspects').forEach(initSection);
  }
})();

