 {% style %}
  .section-{{ section.id }} {
    padding-top: {{section.settings.padding_top}}px;
    padding-bottom: {{section.settings.padding_bottom}}px;
    background-image: {{ section.settings.bg_color }};
  }
  .section-{{ section.id }} .customer-info {
    color: white;
    font-weight: bold;
  }
  .section-{{ section.id }} .card-tes .card {
    background-color: {{ section.settings.card_bg_color2 }};
    color: {{section.settings.text_color}};
  }
   .wrapper {
    max-width: 1180px;
    margin: auto;
}
.custom-tes h2.section-header__title {
    margin-bottom: 40px;
    font-size: 30px;
    text-align: center;
}
   .custom-tes.body {
    display: flex;
    gap: 2rem;
    }
   .card-tes.slick-slide {
    margin: 0 1rem;
}
  .card {
    background-color: #fff;
    display: flex;
    flex-direction: column;
    width: 100%;
    padding: 1rem;
    border-radius: .25rem;
}
   svg.custom-star-tes {
    width: 100%;
    max-width: 20px;
}
   .card-tes {
    padding: 0 10px;
}
  @media(max-width: 767px){
    .section-{{ section.id }} {
      padding-top: calc({{section.settings.padding_top}}px/2);
      padding-bottom: calc({{section.settings.padding_bottom}}px/2);
    }
  }
{% endstyle %}


<div class="custom-testimonial section-{{ section.id }} {{ section.settings.custom_class }}">
  <div class="wrapper{% if section.settings.full_width %} custom-full-width{% endif %}">
    <div class="custom-tes header">
      <h2 class="section-header__title">{{ section.settings.heading }}</h2>
      {{ section.settings.paragraph }}
    </div>
    <div class="custom-tes body">
      {%- for block in section.blocks -%}
        {%- case block.type -%}

          {%- when 'testimonials' -%}
           <div class="card-tes">
             <div class="card">
               <div class="tes-rating">
                 {% for rating in (1..5) %}
                    {% if block.settings.rating >= rating %}
                      <svg role="presentation" fill="none" focusable="false" class="custom-star-tes" viewBox="0 0 15 15">
                        <path d="M7.5 0L9.58587 5.2731L15 5.72949L10.875 9.44483L12.1353 15L7.5 12.0231L2.86475 15L4.125 9.44483L0 5.72949L5.41414 5.2731L7.5 0Z" fill="#FABE1E"></path>
                      </svg>
                    {% endif %}
                  {% endfor %}
               </div>
               <div class="tes-info">
                {{block.settings.testimonial}}
               </div>
               <div class="tes-product">
                  <span><b>Product: <a href="{{ block.settings.product_url }}">{{ block.settings.product_name }}</a></b></span>
               </div>
             </div>
              <svg style="margin-top: -3px; margin-left: 15px;" width="30" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 490 490" style="enable-background:new 0 0 490 490;" xml:space="preserve">
                <polygon points="245,456.701 490,33.299 0,33.299 " style="fill:{{ section.settings.card_bg_color2}}"/>
              </svg>
             <div class="customer-info">
               {%- if block.settings.customer_image != blank -%}
                  <img src="{{ block.settings.customer_image | img_url: 'master' }}">
               {%- endif -%}
               <span>{{ block.settings.customer_name }}</span>
             </div>
           </div>
          
        {%- endcase -%}
      {%- endfor -%}
    </div>
  </div>
</div>
                  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick.css" integrity="sha512-wR4oNhLBHf7smjy0K4oqzdWumd+r5/+6QO/vDda76MW5iug4PT7v86FoEkySIJft3XA0Ae6axhIvHrqwm793Nw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
                  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick-theme.css" integrity="sha512-6lLUdeQ5uheMFbWm3CP271l14RsX1xtx+J5x2yeIDkkiBpeVTNhTqijME7GgRKKi6hCqovwCoBTlRBEC20M8Mg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
                  <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
                  <script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.9.0/slick.min.js" integrity="sha512-HGOnQO9+SP1V92SrtZfjqxxtLmVzqZpjFFekvzZVWoiASSQgSr4cw9Kqd2+l8Llp4Gm0G8GIFJ4ddwZilcdb8A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
                  
        <script>
          $('.custom-tes.body').slick({
  infinite: true,
  slidesToShow: 3,
  slidesToScroll: 1,
  arrows: false,
  responsive: [
    {
      breakpoint: 767,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        dots: false,
      }
    }
  ]
});
                    
        </script>

{% schema %}
  {
    "name": "Custom: Testimonials",
    "settings": [
      {
        "type": "text",
        "id": "heading",
        "label": "Heading",
        "default": "Heading"
      },
      {
        "type": "richtext",
        "id": "paragraph",
        "label": "Paragraph"
      },
      {
        "type": "color",
        "id": "text_color",
        "label": "Text Color",
        "default": "#000000"
      },
      {
        "type": "color_background",
        "id": "bg_color",
        "label": "Background",
        "default": "linear-gradient(#ffffff, #ffffff)"
      },
      {
        "type": "color",
        "id": "card_bg_color2",
        "label": "Card Background",
        "default": "#ffffff"
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full Width",
        "default": false
      },
      {
        "type": "range",
        "id": "padding_top",
        "min": 1,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Padding Top",
        "default": 50
      },
      {
        "type": "range",
        "id": "padding_bottom",
        "min": 1,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Padding Bottom",
        "default": 50
      },
      {
        "type": "text",
        "id": "custom_class",
        "label": "Custom Class"
      }
    ],
    "blocks": [
       {
         "name": "Testimonials",
         "type": "testimonials",
         "limit": 3,
         "settings": [
           {
              "type": "range",
              "id": "rating",
              "min": 0,
              "max": 5,
              "step": 0.1,
              "label": "Rating",
              "default": 5
           },
           {
              "type": "richtext",
              "id": "testimonial",
              "label": "Testimonial"
            },
            {
              "type": "text",
              "id": "product_name",
              "label": "Product Name"
            },
            {
              "type": "url",
              "id": "product_url",
              "label": "Product URL"
            },
            {
              "type": "image_picker",
              "id": "customer_image",
              "label": "Customer Image"
            },
            {
              "type": "text",
              "id": "customer_name",
              "label": "Customer Name"
            }
         ]
       }
    ],
    "presets": [
      {
        "name": "Custom: Testimonials"
      }
    ]
  }
{% endschema %}