{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
VARIANT PICKER
----------------------------------------------------------------------------------------------------------------------

Render the variant picker option selectors.

********************************************
Supported variables
********************************************

* product: the product for which to render the selector (required)
* update_url: if set to true, the URL is updated when the variant changes (usually used for product page)
* hide_sold_out_variants: if set to true, the sold out or unavailable variants are hidden
* hide_size_chart: if set to true, size chart is always hidden
* force_dropdown_as_block: if set to true, the dropdown selectors are replaced by blocks, which is useful for quick buy where dropdown are not convenient to use
* form_id: the ID of the form that is listening
* block: the block itself, for theme-specific options (such as selector types)
{%- endcomment -%}

{%- assign color_label_list = 'general.label.color' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign size_label_list = 'general.label.size' | t | replace: ', ', ',' | downcase | split: ',' -%}
{%- assign variant_image_options = block.settings.variant_image_options | replace: ', ', ',' | downcase | split: ',' -%}

{%- unless product.has_only_default_variant or hide_sold_out_variants and product.available == false -%}
  <variant-picker class="variant-picker" handle="{{ product.handle }}" form="{{ form_id }}" {% if update_url %}update-url{% endif %} {% if hide_sold_out_variants %}hide-sold-out-variants{% endif %}>
    {%- for option in product.options_with_values -%}
      {% liquid
        assign option_downcase = option.name | downcase
        assign resolved_option_selector_style = block.settings.selector_style

        assign swatch_count = option.values | map: 'swatch' | compact | size

        if swatch_count > 0 and block.settings.swatch_selector_style != 'none'
          # Use the swatch selector type only if we have at least one swatch and a supported swatch selector type
          assign resolved_option_selector_style = block.settings.swatch_selector_style
        endif

        # Implementation note: if the option value has no native swatch, BUT that the option name matches a hardcoded list of color names,
        # we fallback to the legacy config-based system. This allows to keep compatibility with stores that were using the config-based, and
        # allow those merchants to upgrade to the new system at their own pace.
        if swatch_count == 0 and color_label_list contains option_downcase and block.settings.swatch_selector_style != 'none'
          assign resolved_option_selector_style = block.settings.swatch_selector_style
        endif

        if resolved_option_selector_style == 'dropdown' and force_dropdown_as_block
          assign resolved_option_selector_style = 'block'
        endif

        if variant_image_options contains option_downcase
          assign resolved_option_selector_style = 'variant_image'
        endif
      %}

      <fieldset class="variant-picker__option no-js:hidden">
        <div class="variant-picker__option-info">
          <div class="h-stack gap-2">
            <legend class="text-subdued">{{ option.name }}:</legend>
            <variant-option-value form="{{ form_id }}" for="option{{ option.position }}">{{ option.selected_value }}</variant-option-value>
          </div>

          {%- if hide_size_chart != true and block.settings.size_chart_page != blank and size_label_list contains option_downcase -%}
            {%- capture drawer_id -%}size-chart-{{ section.id }}-{{ block.id }}{%- endcapture -%}

            <button type="button" class="text-sm text-subdued" aria-controls="{{ drawer_id | escape }}" aria-expanded="false">
              <span class="link">{{ 'product.general.size_chart' | t }}</span>
            </button>

            <x-drawer id="{{ drawer_id }}" class="drawer drawer--lg">
              <span class="h5" slot="header">{{ block.settings.size_chart_page.title }}</span>

              <div class="prose">
                {{- block.settings.size_chart_page.content -}}
              </div>
            </x-drawer>
          {%- endif -%}
        </div>

        {%- if resolved_option_selector_style == 'dropdown' -%}
          {%- capture popover_id -%}popover-variant-picker-{{ section.id }}-{{ product.id }}-{{ option.position }}{%- endcapture -%}

          <div class="relative">
            <button type="button" class="select" aria-controls="{{ popover_id }}" aria-expanded="false">
              <span id="{{ popover_id }}-selected-value">{{- option.selected_value -}}</span>
              {%- render 'icon' with 'chevron-bottom', class: 'select-chevron' -%}
            </button>

            <x-popover id="{{ popover_id }}" class="popover" initial-focus="[aria-selected='true']" close-on-listbox-select anchor-horizontal="start" anchor-vertical="end">
              <p class="h5" slot="title">{{ option.name }}</p>

              <x-listbox data-option-selector class="popover-listbox" aria-owns="{{ popover_id }}-selected-value">
                <input type="hidden" name="option{{ option.position }}" form="{{ form_id }}" value="{{ option.selected_value | escape }}">

                {%- for value in option.values -%}
                  <button type="button" class="popover-listbox__option" role="option" value="{{ value | escape }}" {% if value == option.selected_value %}aria-selected="true"{% endif %} data-option-value>{{ value }}</button>
                {%- endfor -%}
              </x-listbox>
            </x-popover>
          </div>
        {%- else -%}
          <div {% unless block.settings.stack_blocks %}class="scroll-area bleed sm:unbleed"{% endunless %}>
            <div data-option-selector class="variant-picker__option-values {% if block.settings.stack_blocks %}wrap{% else %}scroll-area bleed sm:unbleed{% endif %} {% if resolved_option_selector_style == 'swatch' and settings.color_swatch_style == 'rectangle' %}variant-picker__option-values--color gap-4{% else %}gap-2{% endif %}">
              {% liquid
                assign name = 'option' | append: option.position
                assign form_attribute = 'form="' | append: form_id | append: '"'
                for value in option.values
                  assign selected = false
  
                  if value == option.selected_value
                    assign selected = true
                  endif
  
                  case resolved_option_selector_style
                    when 'variant_image'
                      render 'option-value', type: 'thumbnail', value: value, image: value.variant.featured_media, selected: selected, name: name, form: form_id, bordered: true
                    when 'swatch'
                      if value.swatch != nil
                        render 'option-value', type: 'swatch', swatch: value.swatch, value: value, selected: selected, name: name, form: form_id
                      else
                        # Use the configuration based approach for compatibility reason
                        render 'option-value', type: 'swatch', value: value, selected: selected, name: name, form: form_id
                      endif
                    when 'block'
              echo '<div class="variant__option" ' | append: form_attribute | append: '>'
                      render 'option-value', type: 'block', value: value, selected: selected, name: name, form: form_id
                echo '</div>'
                    when 'block_swatch'
                      if value.swatch != nil
                        render 'option-value', type: 'block', swatch: value.swatch, value: value, selected: selected, name: name, form: form_id, show_swatch: true
                      else
                        # Use the configuration based approach for compatibility reason
                        render 'option-value', type: 'block', value: value, selected: selected, name: name, form: form_id, show_swatch: true
                      endif
                  endcase
                endfor
              %}
            </div>
          </div>
        {%- endif -%}
      </fieldset>
    {%- endfor -%}

    <noscript>
      {%- capture input_label -%}{{ 'product.general.variant' | t }}{%- endcapture -%}
      {%- capture select_options -%}
        {%- for variant in product.variants -%}
          <option {% if variant == product.selected_or_first_available_variant %}selected="selected"{% endif %} {% unless variant.available %}disabled="disabled"{% endunless %} value="{{ variant.id }}">{{ variant.title }} - {{ variant.price | money }}</option>
        {%- endfor -%}
      {%- endcapture -%}

      {%- render 'select', label: input_label, form: form_id, name: 'id', options: select_options -%}
    </noscript>
  </variant-picker>
{%- else -%}
  <noscript>
    <input type="hidden" name="id" form="{{ form_id }}" value="{{ product.selected_or_first_available_variant.id }}">
  </noscript>
{%- endunless -%}