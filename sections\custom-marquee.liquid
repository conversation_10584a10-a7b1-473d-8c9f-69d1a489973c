{%- comment -%}
Custom Marquee section
- Uses built-in <marquee-text> web component (no extra JS)
- Blocks: icon + text
- Section settings: speed, colors, fade width
- Own CSS: assets/custom-marquee.css
{%- endcomment -%}

<section class="custom-marquee" aria-label="Features Marquee">
  <style>
    #shopify-section-{{ section.id }} {
      --cm-bg-start: {{ section.settings.bg_start.rgb }};
      --cm-bg-end: {{ section.settings.bg_end.rgb }};
      --cm-text: {{ section.settings.text_color.rgb }};
      --cm-fade: {{ section.settings.fade_width }}px;
      --cm-font-size: {{ section.settings.font_size }}px;
    }
    /* Critical styles to guarantee single-row marquee even if asset caching lags */
    #shopify-section-{{ section.id }} .cm-viewport{position:relative;overflow:hidden}
    #shopify-section-{{ section.id }} .cm-track{display:grid;grid:auto/auto-flow max-content;justify-content:start;overflow:hidden}
    #shopify-section-{{ section.id }} .cm-row{display:grid;grid:auto/auto-flow max-content;align-items:center;gap:20px;padding:12px 8px}
    @media (prefers-reduced-motion:no-preference){
      #shopify-section-{{ section.id }} .cm-row{animation:translateFull var(--marquee-animation-duration,0s) linear infinite}
    }
  </style>
  {{ 'custom-marquee.css' | asset_url | stylesheet_tag }}

  <div class="cm-viewport">
    {%- capture row -%}
      {%- for block in section.blocks -%}
        <span class="cm-item" {{ block.shopify_attributes }}>
          {%- if block.settings.icon != blank -%}
            <img src="{{ block.settings.icon | image_url: width: 64 }}" alt="{{ block.settings.alt | escape }}" class="cm-icon" loading="lazy" width="{{ block.settings.icon.width }}" height="{{ block.settings.icon.height }}">
          {%- endif -%}
          {%- if block.settings.text != blank -%}
            <h2 class="cm-label">{{ block.settings.text }}</h2>
          {%- endif -%}
        </span>
        <span class="cm-sep" aria-hidden="true"></span>
      {%- endfor -%}
    {%- endcapture -%}

    <marquee-text scrolling-speed="{{ section.settings.speed }}" class="cm-track">
      {%- for i in (1..10) -%}
        <span class="cm-row" {% unless forloop.first %}aria-hidden="true"{% endunless %}>{{- row -}}</span>
      {%- endfor -%}
    </marquee-text>
  </div>
</section>

{% schema %}
{
  "name": "Custom Marquee",
  "class": "shopify-section--custom-marquee",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
    {"type":"range","id":"speed","label":"Marquee speed","min":2,"max":20,"step":1,"default":5},
    {"type":"range","id":"font_size","label":"Font size","min":16,"max":100,"step":2,"unit":"px","default":24},
    {"type":"color","id":"bg_start","label":"Background start","default":"#C45B3E"},
    {"type":"color","id":"bg_end","label":"Background end","default":"#91442F"},
    {"type":"color","id":"text_color","label":"Text/Icon color","default":"#FFFFFF"},
    {"type":"range","id":"fade_width","label":"Edge fade width","min":16,"max":96,"step":4,"unit":"px","default":48}
  ],
  "blocks": [
    {"type":"item","name":"Item","settings":[
      {"type":"image_picker","id":"icon","label":"Icon"},
      {"type":"text","id":"alt","label":"Icon alt text"},
      {"type":"text","id":"text","label":"Text","default":"SHARP FOCUS"}
    ]}
  ],
  "presets": [
    {"name":"Custom Marquee","blocks":[{"type":"item"},{"type":"item"},{"type":"item"}]}
  ]
}
{% endschema %}

