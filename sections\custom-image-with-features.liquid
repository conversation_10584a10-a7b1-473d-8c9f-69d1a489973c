{% style %}

    .custom-container {
    padding: 20px;
    max-width: 1340px;
    margin: 0 auto;
    text-align: center;
    justify-items: center;
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .heading-1 {
          font-size: 20px;
          color: #30511d;
          font-weight: 600;
          padding-bottom: 20px;
        }

        .heading-2 {
            font-size: 32px;
            color: #000;
            font-weight: bold;
            max-width: 768px;
           margin: auto;
          line-height: 1.3;
        }

        .header-text {
            padding-top: 20px;
            color: #555;
           max-width: 768px;
           margin: auto;
          font-size: 18px;
        }

        .benefit-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            max-width: 1340px;
        }

        .image {
            max-width: 724px;
              margin: auto;
        }

        .image img {
            width: 100%;
            border-radius: 20px;
          
        }
     .ico-container {
    width: 100px;
    height: 100px;
    align-content: center;
     }

        .benefits-container {
            display: flex;
            justify-content: space-between;
            gap: 30px;
            max-width: 1340px;
            margin-top: 20px;
        }

        .benefits-column {
            display: flex;
            flex-direction: column;
            margin: auto;
            max-width: 240px;
            gap: 30px;
        }

        .benefit {
    align-items: center;
    display: flex;
    gap: 10px;
    flex-direction: column;
    text-align: center;
        }

        .benefit-icon {
            width: 100px;
            height: 100px;
            
        }


        .benefit-text {
            margin: 5px 0 0;
            font-size: 16px;
            color: #555;
        }
.benefit-2 {
    margin: auto;
}
.benefit-heading {
font-weight:900;
font-size: 20px;
color: #30511d;
margin-bottom: 10px;
}
 @media (max-width: 768px) {
.benefits-container {
      flex-direction: column;
      align-items: center;
      gap: 20px;
            }
.benefit-icon {
    max-width: 80px;
    max-height: 80px;
    margin: 10px;
}
.ico-container {
  min-width: 100px;
}   
.benefit {
    flex-direction:row;
}
.benefit-1 {
    order: 2;
}
.benefit-2 {
    order: 1;
}
.benefit-3 {
    order:3;
}
.benefit-text {
    text-align: left;
  font-size: 16px;
}
.benefits-column {
                max-width: 100%;
            }
.header h3 {
  font-size: 16px;
}

.header h2 {
  font-size: 22px;
}
.benefits-column {
  gap: 15px;
}
        }
.featured_video {
  max-width: 724px;
}
.main_video {
  --default-aspect-ratio: 1/1 !important;
}
{% endstyle %}
 <div class="custom-container">
        <div class="header">
            <h3 class="heading-1">{{ section.settings.heading_1 }}</h3>
          <h2 class="heading-2">{{ section.settings.heading_2 }}</h2>
            <h2 class="heading-2" style=" text-decoration: underline;">{{ section.settings.heading_3 }}</h2>
            <div class="header-text">{{ section.settings.heading_text }} </div>
        </div>
        <div class="benefit-content">
            <div class="benefits-container">
                <div class="benefits-column benefit-1">
                    <div class="benefit">
                        <div class="ico-container">
                        <img src="{{ section.settings.benefit_image_1 | img_url: '300x300' }}" alt="Enhanced Energy Levels" class="benefit-icon">
                        </div>
                        <div class="benefit-text">
                            <div class="benefit-heading">{{ section.settings.benefit_heading_1 }} </div>
                            {{ section.settings.benefit_text_1 }}
                        </div>
                    </div>
                    <div class="benefit">
                        <div class="ico-container">
                        <img src="{{ section.settings.benefit_image_2| img_url: '300x300' }}" alt="Stress Reduction" class="benefit-icon">
                        </div>
                        <div class="benefit-text">
                            <div class="benefit-heading">{{ section.settings.benefit_heading_2 }}</div>
                            {{ section.settings.benefit_text_2 }}
                        </div>
                    </div>
                </div>
  <div class=" benefit-2">
   {% if section.settings.main_video %}
     <div class="featured_video">
            <video-media autoplay loop muted class="main_video">
      {{ section.settings.main_video | video_tag: playsinline: true, muted: true, loop: true, preload: 'metadata', class: 'rounded' }}
  </video-media>
           </div>
                
   {% else %}
           <div class="image">
                    <img src="{{ section.settings.main_image | img_url: '1500x1500' }}" alt="Grounding Mat">
                </div>
     {% endif %}
</div>
                <div class="benefits-column benefit-3">
                    <div class="benefit">
                        <div class="ico-container">
                        <img src="{{ section.settings.benefit_image_3 | img_url: '300x300' }}" alt="Better Blood Flow" class="benefit-icon">
                        </div>
                        <div class="benefit-text">
                            <div class="benefit-heading">{{ section.settings.benefit_heading_3 }}</div>
                            {{ section.settings.benefit_text_3 }}
                        </div>
                    </div>
                    <div class="benefit">
                        <div class="ico-container">
                        <img src="{{ section.settings.benefit_image_4 | img_url: '300x300' }}" alt="Improved Sleep Quality" class="benefit-icon">
                        </div>
                        <div class="benefit-text">
                            <div class="benefit-heading">{{ section.settings.benefit_heading_4 }}</div>
                          {{ section.settings.benefit_text_4 }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
 </div>
{% schema %}
  {
  "name": "Image with features",
  "settings": [
    {
      "type": "richtext",
      "id": "heading_1",
      "label": "Heading 1"
    },
    {
      "type": "richtext",
      "id": "heading_2",
      "label": "Heading 2"
    },
    {
      "type": "richtext",
      "id": "heading_3",
      "label": "Heading 3"
    },
    {
      "type": "richtext",
      "id": "heading_text",
      "label": "Heading Text"
    },
    {
      "type": "image_picker",
      "id": "main_image",
      "label": "Main Image"
    },
    {
      "type": "video",
      "id": "main_video",
      "label": "Main Video"
    },
    {
      "type": "richtext",
      "id": "benefit_heading_1",
      "label": "Benefit Heading 1"
    },
    {
      "type": "richtext",
      "id": "benefit_text_1",
      "label": "Benefit Text 1"
    },
    {
      "type": "image_picker",
      "id": "benefit_image_1",
      "label": "Benefit Image 1"
    },
    {
      "type": "richtext",
      "id": "benefit_heading_2",
      "label": "Benefit Heading 2"
    },
    {
      "type": "richtext",
      "id": "benefit_text_2",
      "label": "Benefit Text 2"
    },
    {
      "type": "image_picker",
      "id": "benefit_image_2",
      "label": "Benefit Image 2"
    },
    {
      "type": "richtext",
      "id": "benefit_heading_3",
      "label": "Benefit Heading 3"
    },
    {
      "type": "richtext",
      "id": "benefit_text_3",
      "label": "Benefit Text 3"
    },
    {
      "type": "image_picker",
      "id": "benefit_image_3",
      "label": "Benefit Image 3"
    },
    {
      "type": "richtext",
      "id": "benefit_heading_4",
      "label": "Benefit Heading 4"
    },
    {
      "type": "richtext",
      "id": "benefit_text_4",
      "label": "Benefit Text 4"
    },
    {
      "type": "image_picker",
      "id": "benefit_image_4",
      "label": "Benefit Image 4"
    }
  ],
  "presets": [
    {
      "name": "Custom image with features"
    }
  ]
}  
{% endschema %}
