{%- if collections.size == 0 -%}
  <div class="empty-state">
    <div class="empty-state__icon-wrapper">
      {%- render 'icon' with 'picto-box', width: 32, height: 32, stroke_width: 1 -%}
      <span class="count-bubble count-bubble--lg">0</span>
    </div>

    <div class="prose">
      <p class="h4">{{ 'collection.general.no_collections' | t }}</p>

      {%- assign button_content = 'collection.general.continue_shopping' | t -%}
      {%- render 'button', href: settings.routes.root_url, size: 'xl', content: button_content -%}
    </div>
  </div>
{%- else -%}
  <style>
    #shopify-section-{{ section.id }} .collection-list {
      --collection-list-grid: auto / repeat({{ section.settings.collections_per_row_mobile | times: 1 }}, minmax(0, 1fr));
    }

    @media screen and (min-width: 700px) {
      #shopify-section-{{ section.id }} .collection-list {
        --collection-list-grid: auto / repeat(2, minmax(0, 1fr));
      }
    }

    @media screen and (min-width: 1000px) {
      #shopify-section-{{ section.id }} .collection-list {
        --collection-list-grid: auto / repeat({{ 3 | at_most: section.settings.collections_per_row }}, minmax(0, 1fr));
      }
    }

    @media screen and (min-width: 1400px) {
      #shopify-section-{{ section.id }} .collection-list {
        --collection-list-grid: auto / repeat({{ section.settings.collections_per_row }}, minmax(0, 1fr));
      }
    }
  </style>

  <div class="container">
    <div class="page-spacer">
      <div class="v-stack gap-12">
        <h1 class="h1 text-center">{{ 'collection.general.all_collections' | t }}</h1>

        {%- paginate collections by 48 -%}
          <collection-list class="collection-list">
            {% assign collections_list = section.settings.collections | default: collections %}

            {%- for collection in collections_list -%}
              <a href="{{ collection.url }}" class="collection-card" reveal-js>
                <div class="content-over-media group rounded-sm" style="--content-over-media-overlay: {{ section.settings.overlay_color.rgb }} / {{ section.settings.overlay_opacity | divided_by: 100.0 }}">
                  {%- if collection.featured_image != blank -%}
                    {%- capture sizes -%}(max-width: 699px) 73vw, {{ settings.page_width | divided_by: section.settings.collections_per_row }}px{%- endcapture -%}
                    {{- collection.featured_image | image_url: width: collection.featured_image.width | image_tag: loading: 'lazy', sizes: sizes, widths: '200,300,400,500,600,800,1000,1200,1400,1600', class: 'zoom-image' -}}
                  {%- endif -%}

                  <div class="collection-card__content-wrapper place-self-center text-center text-custom" style="--text-color: {{ section.settings.text_color.rgb }}">
                    <div class="collection-card__content">
                      <p class="h2">{{ collection.title | escape }}</p>
                    </div>

                    {%- render 'icon' with 'circle-button-right-clipped', width: 48, height: 48 -%}
                  </div>
                </div>
              </a>
            {%- endfor -%}
          </collection-list>

          {%- if section.settings.collections == empty -%}
            {%- render 'pagination', paginate: paginate -%}
          {%- endif -%}
        {%- endpaginate -%}
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "List collections",
  "class": "shopify-section--main-list-collections",
  "tag": "section",
  "settings": [
    {
      "type": "collection_list",
      "id": "collections",
      "label": "Selected collections",
      "limit": 48
    },
    {
      "type": "select",
      "id": "collections_per_row_mobile",
      "label": "Collections per row (mobile)",
      "options": [
        {
          "value": "1",
          "label": "1"
        },
        {
          "value": "2",
          "label": "2"
        }
      ],
      "default": "1"
    },
    {
      "type": "range",
      "id": "collections_per_row",
      "min": 2,
      "max": 5,
      "label": "Collections per row (desktop)",
      "default": 3
    },
    {
      "type": "header",
      "content": "Colors"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "overlay_color",
      "label": "Overlay",
      "default": "#000000"
    },
    {
      "type": "range",
      "id": "overlay_opacity",
      "label": "Overlay opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 30
    }
  ]
}
{% endschema %}

