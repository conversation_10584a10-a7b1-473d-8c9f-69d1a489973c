{%- if link.links.size > 0 -%}
  <style>
    @media screen and (min-width: 1150px) {
      #mega-menu-{{ mega_menu_block.id }} {
        --mega-menu-nav-column-max-width: {% if mega_menu_block.settings.submenu_style == 'bold_text' %}{% if link.links.size <= 2 %}240px{% else %}200px{% endif %}{% else %}{% if link.links.size <= 2 %}180px{% else %}160px{% endif %}{% endif %};
        --mega-menu-justify-content: {% if link.links.size == 0 or mega_menu_block.settings.layout == 'horizontal_center' and mega_menu_block.settings.stretch_promo == false %}center{% else %}space-between{% endif %};
        --mega-menu-nav-gap: {% if mega_menu_block.settings.stretch_promo %}var(--spacing-12){% else %}var(--spacing-8){% endif %};

        {% if link.links.size > 3 %}
          --column-list-max-width: 75%;
        {% endif %}
      }
    }

    @media screen and (min-width: 1400px) {
      #mega-menu-{{ mega_menu_block.id }} {
        --mega-menu-nav-column-max-width: {% if mega_menu_block.settings.submenu_style == 'bold_text' %}{% if link.links.size == 1 %}260px{% elsif link.links.size == 2 %}240px{% else %}210px{% endif %}{% else %}{% if link.links.size == 1 %}220px{% elsif link.links.size == 2 %}200px{% else %}180px{% endif %}{% endif %};
        --mega-menu-nav-gap: {% if mega_menu_block.settings.layout == 'horizontal_center' %}var(--spacing-12){% else %}var(--spacing-16){% endif %};

        {% if link.links.size > 4 %}
          --column-list-max-width: 75%;
        {% else %}
          --column-list-max-width: max-content;
        {% endif %}
      }
    }

    @media screen and (min-width: 1600px) {
      #mega-menu-{{ mega_menu_block.id }} {
        --mega-menu-nav-gap: var(--spacing-16);
      }
    }

    @media screen and (min-width: 1800px) {
      #mega-menu-{{ mega_menu_block.id }} {
        --mega-menu-nav-gap: var(--spacing-20);
      }
    }
  </style>
{%- endif -%}

<div id="mega-menu-{{ mega_menu_block.id }}" class="mega-menu {% if link.links.size == 0 %}justify-center{% endif %}">
  {%- if link.links.size > 0 -%}
    <ul class="mega-menu__nav" role="list">
      {%- for sub_link in link.links -%}
        <li class="v-stack gap-4 justify-items-start">
          <a {% if sub_link.url != '#' %}href="{{ sub_link.url }}"{% endif %} class="{% if mega_menu_block.settings.submenu_style == 'bold_heading' %}h5{% endif %}" {% if sub_link.current %}aria-current="page"{% endif %}>
            <span {% if sub_link.url != '#' %}class="{% if mega_menu_block.settings.submenu_style == 'bold_text' %}link-faded{% else %}reversed-link hover:show{% endif %}"{% endif %}>
              {{- sub_link.title -}}
            </span>
          </a>

          {%- if sub_link.levels > 0 -%}
            <ul class="v-stack gap-2 justify-items-start" role="list">
              {%- for sub_sub_link in sub_link.links -%}
                <li>
                  <a href="{{ sub_sub_link.url }}" class="{% if mega_menu_block.settings.submenu_style == 'bold_text' %}h5{% else %}link-faded{% endif %}">
                    <span {% if mega_menu_block.settings.submenu_style == 'bold_text' %}class="reversed-link hover:show"{% endif %}>{{- sub_sub_link.title -}}</span>
                  </a>
                </li>
              {%- endfor -%}
            </ul>
          {%- endif -%}
        </li>
      {%- endfor -%}
    </ul>
  {%- endif -%}

  {% liquid
    # To maximize space, we force a carousel mode depending on the actual content

    assign promo_item_count = 0
    assign use_carousel_fallback_until = ''

    for i in (1..3)
      assign image_setting = 'image_' | append: i

      if mega_menu_block.settings[image_setting] != blank
        assign promo_item_count = promo_item_count | plus: 1
      endif
    endfor

    if mega_menu_block.settings.product != blank
      assign promo_item_count = promo_item_count | plus: 1
    endif

    if link.links.size > 0 and promo_item_count == 4 or mega_menu_block.settings.promo_content_layout == 'carousel'
      assign force_carousel_mode = true
    else
      if link.links.size == 2
        if promo_item_count == 3 and mega_menu_block.settings.layout == 'grid'
          assign use_carousel_fallback_until = 'xl'
        endif
      elsif link.links.size >= 3 and promo_item_count == 3
        if use_promo_collage
          assign use_carousel_fallback_until = 'xl'
        else
          assign use_carousel_fallback_until = '2xl'
        endif
      endif
    endif
  %}

  {%- if force_carousel_mode -%}
    {%- render 'navigation-promo-block', mega_menu_block: mega_menu_block, force_carousel_mode: force_carousel_mode -%}
  {% else %}
    {%- if use_carousel_fallback_until != '' -%}
      {%- render 'navigation-promo-block', navigation_layout: navigation_layout, mega_menu_block: mega_menu_block, link_col: link.links.size, is_navigation_drawer: false, use_carousel_fallback_until: use_carousel_fallback_until -%}
    {%- endif -%}

    {%- render 'navigation-promo-block', navigation_layout: navigation_layout, mega_menu_block: mega_menu_block, link_col: link.links.size, is_navigation_drawer: false, hide_promo_until: use_carousel_fallback_until -%}
  {%- endif -%}
</div>