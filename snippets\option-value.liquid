{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
OPTION VALUE
----------------------------------------------------------------------------------------------------------------------

This component renders a single option value. It is a generic component that is used both on product and collection
pages (for metaobject filtering). It ca be used to render a swatch, a block or a thumbnail.

********************************************
Supported variables
********************************************


* type: control the presentational type of the swatch. Can be "swatch", "block" or "thumbnail". [REQUIRED]
* value: the value submitted to the server [REQUIRED]
* name: the name attribute when field is submitted to the server [REQUIRED]
* selected: if set to true, this swatch is preselected
* allow_multiple: if set to true, it allows several values to be selected (by using a checkbox instead of radio)
* label: an optional label to show as tooltip (if no provided, it uses the value)
* form: the form ID that this swatch is linked to
* disabled: if set to true, this swatch is disabled
* href: if defined, the swatch will be outputted as a link instead of an input (most of the other attributes are ignored)

Thumbnail specific parameters:

* image: the image to use
* size: can be 'sm' to make it smaller
* bordered: if set to true, a border is added when the image is not selected

Block swatch specific parameters

* show_swatch: if set to true, show the color as a small tile.
* color: a single color drop (used for product color swatches)
* colors: an optional list of color drop (if not set, the theme will use the config based approach as a fallback). When more
          than one color is given, a conic gradient is generated. It supports up to 4 colors.
* image: an optional image used as the background (if not set, the theme will use the config based approach as a fallback)

Color swatch specific parameters:

* show_tooltip: if set to true, display a tooltip on hover
* swatch: a swatch drop that can contain either an image or a color
{%- endcomment -%}


{%- capture id -%}option-value-{{ section.id }}-{{ form }}-{{ context }}-{{ name | handle }}-{{ value | handle }}{%- endcapture -%}

{%- liquid
  if type == 'swatch' or type == 'block' and show_swatch
    if swatch.image != blank
      assign swatch_image = swatch.image | image_url: width: 72
      assign swatch_style = '--swatch-background: url(' | append: swatch_image | append: ')'
    elsif swatch.color != blank
      assign swatch_style = '--swatch-background: linear-gradient(to right, ' | append: swatch.color | append: ', ' | append: swatch.color | append: ')'
    else
      # When color or image is not explicitly passed, we parse the config
      assign swatch_config = settings.color_swatch_config | newline_to_br | split: '<br />'

      assign value_downcase = value | downcase | strip
      assign swatch_style = '--swatch-background: linear-gradient(to right, ' | append: value_downcase | append: ', ' | append: value_downcase | append: ')'

      for swatch_item in swatch_config
        assign swatch_parts = swatch_item | split: ':'
        assign swatch_name = swatch_parts.first | downcase | strip

        if value_downcase == swatch_name
          assign swatch_value = swatch_parts.last | strip

          if swatch_value contains '#'
            assign swatch_style = '--swatch-background: linear-gradient(to right, ' | append: swatch_value | append: ', ' | append: swatch_value | append: ')'
          elsif swatch_value contains 'linear-gradient('
            assign swatch_style = '--swatch-background: ' | append: swatch_value
          elsif images[swatch_value] != blank
            assign swatch_image = images[swatch_value] | image_url: width: 72
            assign swatch_style = '--swatch-background: url(' | append: swatch_image | append: ')'
          endif

          break
        endif
      endfor
    endif
  endif
-%}

{%- unless href -%}
  <input class="sr-only" type="{% if allow_multiple %}checkbox{% else %}radio{% endif %}" name="{{ name }}" {% if form %}form="{{ form | escape }}"{% endif %} id="{{ id | escape }}" value="{{ value | escape }}" {% if disabled %}disabled{% endif %} {% if selected %}checked="checked"{% endif %}>
{%- endunless -%} 



{%- case type -%}
  {%- when 'thumbnail' -%}

    {%- if image != blank -%}
      {%- if href -%}
        <a href="{{ href }}" class="thumbnail-swatch {% if size == 'sm' %}thumbnail-swatch--sm{% endif %} {% if selected %}is-selected{% endif %} {% if bordered %}border{% endif %}">
          <span class="sr-only">{{ label | default: value }}</span>
          {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '40px', widths: '40,80', class: 'object-cover' -}}
        </a>
      {%- else -%}
        
        <div class="img-txt">
        <label class="thumbnail-swatch {% if size == 'sm' %}thumbnail-swatch--sm{% endif %} {% if bordered %}border{% endif %}" for="{{ id | escape }}" data-option-value id="{{ label | default: value }}">
          <span class="sr-only">{{ label | default: value }}</span>
          
          {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '40px', widths: '40,80', class: 'object-cover' -}}
           <p class="img-label">{{ label | default: value }}</p>
        </label>
          </div>
        
      {%- endif -%}
      
    {%- else -%}
      <label class="block-swatch" for="{{ id | escape }}" data-option-value>{{ label | default: value }}</label>
    {%- endif -%}




  

  {%- when 'block' -%}
    {%- assign white_label = 'general.label.white' | t | downcase -%}
    {%- assign value_downcase = value | downcase -%}

  
    {%- if href -%}
      <a href="{{ href }}" class="block-swatch {% if selected %}is-selected{% endif %}">
        {%- if show_swatch -%}
          <span class="block-swatch__color {% if white_label == value_downcase %}ring-inset{% endif %}" style="{{ swatch_style }}"></span>
        {%- endif -%}

        <span>{{ label | default: value }}</span>
      </a>

    {%- else -%}
      <label class="block-swatch" for="{{ id | escape }}" data-option-value>
        {%- if show_swatch -%}
          <span class="block-swatch__color {% if white_label == value_downcase %}ring-inset{% endif %}" style="{{ swatch_style }}"></span>
        {%- endif -%}
        
       {% assign label = label | default: value %}
        {% if label == 'USA'  %}
          <img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/USA_Plug.svg?v=1733464357" alt="USA Plug" width='35px'class="plug-img">
          {% elsif  label =='EU' %}
           <img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/EU_plug.svg?v=1733464365" alt="USA Plug" width='35px' class="plug-img">
          {% elsif  label =='UK' %}
           <img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/UK_Plug.svg?v=1733464374" alt="USA Plug" width='35px' class="plug-img">
          {% elsif  label =='AUS' or label == 'AUS/NZ' %}
           <img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/AU_NZ_Plug.svg?v=1733464385" alt="USA Plug" width='35px' class="plug-img">
            {% endif %}
        <span class="swatch-text">{{ label | default: value }}</span>
      </label>
      
    {%- endif -%}

  {%- when 'swatch' -%}
    {%- assign white_label = 'general.label.white' | t | downcase -%}
    {%- assign value_downcase = value | downcase -%}

    {%- if href -%}
      <a href="{{ href }}" class="color-swatch {% if settings.color_swatch_style == 'rectangle' %}color-swatch--rectangle{% endif %} {% if size == 'sm' %}color-swatch--sm{% endif %} {% if selected %}is-selected{% endif %} {% if white_label == value_downcase %}ring-inset{% endif %} {% if settings.color_swatch_style == 'round' %}rounded-full{% endif %}" {% if show_tooltip %}data-tooltip="{{ label | default: value | escape }}"{% endif %} style="{{ swatch_style }}">
        <span class="sr-only">{{ label | default: value }}</span>
      </a>
    {%- else -%}
      
      <label class="color-swatch {% if settings.color_swatch_style == 'rectangle' %}color-swatch--rectangle{% endif %} {% if size == 'sm' %}color-swatch--sm{% endif %} {% if white_label == value_downcase %}ring-inset{% endif %} {% if settings.color_swatch_style == 'round' %}rounded-full{% endif %}" for="{{ id | escape }}" data-option-value {% if show_tooltip %}data-tooltip="{{ label | default: value | escape }}"{% endif %} style="{{ swatch_style }}">
        <span class="sr-only">{{ label | default: value }}</span>
      </label>
    {%- endif -%}
{%- endcase -%}


<style>
  .img-txt {
    display: flex;
    border-width: 1px;
    align-items: center;
    padding: 5px;
    border-radius: 5px;
    width: 90px;
}

  .img-label {
    margin-left: 10px;
    font-weight: 600;
}
  label.thumbnail-swatch {
    display: flex
;
    align-items: center;
}
</style>