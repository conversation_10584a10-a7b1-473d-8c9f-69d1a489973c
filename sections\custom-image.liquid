{% style %}
  .section-{{ section.id }} .image-img-desktop img {
    width: 100%;
    max-width: {{section.settings.image_width_desktop}}%;
    margin: auto;
  }
  .section-{{ section.id }} .image-img-mobile img {
    width: 100%;
    max-width: {{section.settings.image_width_mobile}}%;
  }
  .section-{{ section.id }} img {
    margin-top: -{{ section.settings.overlay_top}}px !important;
    margin-bottom: -{{ section.settings.overlay_bottom}}px !important;
    position: relative;
    z-index: 2;
  }
  .section-template--15928311644234__custom_image_zHh7mY img {
    margin-bottom: -130px !important;
  }
  .custom-image.img-center>div {
    text-align: center;
}
  .custom-image>div {
    width: 100%;
    max-width: 1200px;
    padding: 0 10px;
    margin: auto;
}
  .image-img-mobile {
    display: none;
}
  @media (max-width: 699px) {
    .image-img-mobile {
    display: flex!important;
  }
 .image-img-desktop {
    display: none!important;
  }  
  }
  
  
{% endstyle %}
<div class="custom-image section-{{ section.id }}{% if section.settings.image_center %} img-center{% endif %}{% if section.settings.full_width %} hf-full-width{% endif %} {{ section.settings.custom_class }}">
  
    {%- if section.settings.desktop_image != blank -%}
      <div class="image-img-desktop">
        {%- if section.settings.image_link != blank -%}
          <a href="{{ section.settings.image_link }}">
        {%- endif -%}
        <img src="{{ section.settings.desktop_image | img_url: 'master' }}">
        {%- if section.settings.image_link != blank -%}
          </a>
        {%- endif -%}
      </div>
    {%- endif -%}
    
    {%- if section.settings.mobile_image != blank -%}
      <div class="image-img-mobile">
        {%- if section.settings.image_link != blank -%}
          <a href="{{ section.settings.image_link }}">
        {%- endif -%}
        <img src="{{ section.settings.mobile_image | img_url: 'master' }}">
        {%- if section.settings.image_link != blank -%}
          </a>
        {%- endif -%}
      </div>
    {%- endif -%}

  
</div>

{% schema %}
  {
    "name": "Custom: Image",
    "settings": [
      {
        "type": "image_picker",
        "id": "desktop_image",
        "label": "Desktop Image"
      },
      {
        "type": "range",
        "id": "image_width_desktop",
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%",
        "label": "Image Width Desktop",
        "default": 100
      },
      {
        "type": "image_picker",
        "id": "mobile_image",
        "label": "Mobile Image"
      },
      {
        "type": "range",
        "id": "image_width_mobile",
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "%",
        "label": "Image Width Mobile",
        "default": 100
      },
      {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full Width",
        "default": false
      },
      {
        "type": "checkbox",
        "id": "image_center",
        "label": "Image Center",
        "default": true
      },
      {
        "type": "url",
        "id": "image_link",
        "label": "Image link"
      },
      {
        "type": "range",
        "id": "overlay_top",
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Overlay Top",
        "default": 0
      },
      {
        "type": "range",
        "id": "overlay_bottom",
        "min": 0,
        "max": 100,
        "step": 1,
        "unit": "px",
        "label": "Overlay Bottom",
        "default": 0
      },
      {
        "type": "text",
        "id": "custom_class",
        "label": "Custom Class"
      }
    ],
    "presets": [
      {
        "name": "Custom: Image"
      }
    ]
  }
{% endschema %}