{%- comment -%}
----------------------------------------------------------------------------------------------------------------------
LINE ITEM
----------------------------------------------------------------------------------------------------------------------

This component renders a single line item product information, and is used on order and cart page.

********************************************
Supported variables
********************************************

* line_item: the line item to render (required)
* show_desktop_quantity: if set to true (for instance on cart drawer), the quantity is also shown on the line
{%- endcomment -%}

{%- if line_item.variant.inventory_management != blank and line_item.variant.inventory_policy == 'deny' -%}
  {%- assign line_max_quantity = line_item.variant.inventory_quantity -%}
{%- endif -%}

<line-item class="line-item">
  {%- if line_item.image != blank -%}
    <div class="line-item__media-wrapper">
      {{- line_item.image | image_url: width: line_item.image.width | image_tag: loading: 'lazy', sizes: '(max-width: 740px) 80px, 96px', widths: '80,96,160,192', class: 'line-item__media rounded-xs' -}}

      <pill-loader class="pill-loader"></pill-loader>
    </div>
  {%- endif -%}

  <div class="line-item__info">
    <div class="v-stack gap-0.5">
      {%- if line_item.url != blank -%}
        <a href="{{ line_item.url }}" class="bold">
          <span class="reversed-link hover:show">{{ line_item.product.title | default: line_item.title }}</span>
        </a>
      {%- else -%}
        <p class="bold">{{ line_item.product.title | default: line_item.title }}</p>
      {%- endif -%}

      {%- render 'price-list', line_item: line_item -%}
    </div>

    {%- unless line_item.product.has_only_default_variant or line_item.gift_card -%}
      <p class="text-sm text-subdued">{{ line_item.variant.title }}</p>
    {%- endunless -%}

    {%- if line_item.selling_plan_allocation -%}
      <p class="text-sm text-subdued">{{ line_item.selling_plan_allocation.selling_plan.name }}</p>
    {%- endif -%}

    {%- unless line_item.properties == blank -%}
      <ul class="list-disc">
        {%- for property in line_item.properties -%}
          {%- assign first_character_in_key = property.first | truncate: 1, '' -%}

          {%- if property.last == blank or first_character_in_key == '_' -%}
            {%- continue -%}
          {%- endif -%}

          <li class="text-sm text-subdued">
            {%- if property.last contains '/uploads/' -%}
              <a href="{{ property.last }}" class="link">{{ property.last | split: '/' | last }}</a>
            {%- else -%}
              {{ property.first }}: {{ property.last }}
            {%- endif -%}
          </li>
        {%- endfor -%}
      </ul>
    {%- endunless -%}

    <div class="text-sm text-subdued sm:hidden">
      {%- if line_item.url_to_remove -%}
        <line-item-quantity class="h-stack justify-center gap-3">
          <!-- Quantity buttons added here -->
          <div class="quantity-section">
          <button class="quantity-button minus" onclick="updateQuantity('{{ line_item.key }}', -1)"><span class="minus-ico">-</span></button>
          <input class="quantity-input" type="text" is="quantity-input" inputmode="numeric" {% if line_max_quantity %}max="{{ line_max_quantity }}"{% endif %} data-line-key="{{ line_item.key }}" aria-label="{{ 'cart.order.change_quantity' | t | escape }}" value="{{ line_item.quantity }}" id="quantity-{{ line_item.key | escape }}" readonly>
          <button class="quantity-button plus" onclick="updateQuantity('{{ line_item.key }}', 1)"><span class="plus-ico">+</span></button>
          </div>
          <span class="text-xs">
            <a href="{{ line_item.url_to_remove }}" class="link remove-link" id="remove-item-link">{{ 'cart.order.remove' | t }}</a>
          </span>
        </line-item-quantity>
      {%- else -%}
        {{- 'customer.order.quantity' | t }}: {{ line_item.quantity -}}
      {%- endif -%}
    </div>

    {%- if line_item.line_level_discount_allocations != blank -%}
      <ul class="contents" role="list">
        {%- for discount_allocation in line_item.line_level_discount_allocations -%}
          <li class="badge">
            {%- render 'icon' with 'discount' -%} {{ discount_allocation.discount_application.title }} (-{{ discount_allocation.amount | money }})
          </li>
        {%- endfor -%}
      </ul>
    {%- endif -%}
  </div>

  {%- if show_desktop_quantity -%}
    <div class="line-item__actions text-subdued hidden sm:block">
      <line-item-quantity class="v-stack gap-2">
        <!-- Quantity buttons added here -->
        <div class="quantity-section">
        <button class="quantity-button minus" onclick="updateQuantity('{{ line_item.key }}', -1)"><span class="minus-ico">-</span></button>
          <input class="quantity-input" type="text" is="quantity-input" inputmode="numeric" {% if line_max_quantity %}max="{{ line_max_quantity }}"{% endif %} data-line-key="{{ line_item.key }}" aria-label="{{ 'cart.order.change_quantity' | t | escape }}" value="{{ line_item.quantity }}" id="quantity-{{ line_item.key | escape }}" readonly>
          <button class="quantity-button plus" onclick="updateQuantity('{{ line_item.key }}', 1)"><span class="plus-ico">+</span></button>
        </div>
        <span class="text-xs text-center">
          <a href="{{ line_item.url_to_remove }}" class="link remove-link" id="remove-item-link">{{ 'cart.order.remove' | t }}</a>
        </span>
      </line-item-quantity>
    </div>
  {%- endif -%}
</line-item>

<script>


  
  function updateQuantity(lineKey, change) {
    // Add gray overlay and disable cart-drawer interaction
    showLoadingOverlay();

    // Find the input element using the data-line-key attribute
    const quantityInput = document.querySelector(`input[data-line-key="${lineKey}"]`);
    if (quantityInput) {
      let newQuantity = parseInt(quantityInput.value) + change;

      // Ensure quantity is within valid range (e.g., 1 to max quantity)
      newQuantity = Math.max(1, newQuantity); // You can adjust this to be more restrictive based on max inventory
      quantityInput.value = newQuantity;

      // Optionally update the cart via AJAX or your platform's cart update function
      updateCart(lineKey, newQuantity);
    } else {
      console.error(`Quantity input with data-line-key "${lineKey}" not found.`);
    }
  }

  async function updateCart(lineKey, targetQuantity) {
    if (window.themeVariables.settings.pageType === "cart") {
      // If the user is on the cart page, update the cart directly via URL
      window.location.href = `${Shopify.routes.root}cart/change?id=${lineKey}&quantity=${targetQuantity}`;
    } else {
      // If the user is not on the cart page, handle it with JavaScript
      const lineItem = document.querySelector(`.cart-item[data-line-key="${lineKey}"]`);
      lineItem?.dispatchEvent(new CustomEvent("line-item:will-change", { bubbles: true, detail: { targetQuantity } }));

      let sectionsToBundle = [];
      document.documentElement.dispatchEvent(new CustomEvent("cart:prepare-bundled-sections", { bubbles: true, detail: { sections: sectionsToBundle } }));

      // Send the AJAX request to update the cart on the server
      try {
        const cartContent = await (await fetch(`${Shopify.routes.root}cart/change.js`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            id: lineKey,
            quantity: targetQuantity,
            sections: sectionsToBundle
          })
        })).json();

        // Filter out the updated line item
        const lineItemAfterChange = cartContent["items"].filter((item) => item["key"] === lineKey);

        // Dispatch the event to notify that the line item has changed
        lineItem?.dispatchEvent(new CustomEvent("line-item:change", {
          bubbles: true,
          detail: {
            quantity: lineItemAfterChange.length === 0 ? 0 : lineItemAfterChange[0]["quantity"],
            cart: cartContent
          }
        }));

        // Dispatch the cart change event to update the entire cart
        document.documentElement.dispatchEvent(new CustomEvent("cart:change", {
          bubbles: true,
          detail: {
            baseEvent: "line-item:change",
            cart: cartContent
          }
        }));

        // Optionally, update the cart UI
        updateCartUI(cartContent);

        // Refresh the page after a successful update
        location.reload(); // This will refresh the entire page

      } catch (error) {
        console.error("Error updating cart:", error);
      }
    }
  }

  function showLoadingOverlay() {
    // Find the cart-drawer element
    const cartDrawer = document.querySelector('.cart-drawer__inner');
    if (cartDrawer) {
      // Create the overlay if it doesn't exist
      let overlay = cartDrawer.querySelector('#loading-overlay');
      if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.style.position = 'absolute';
        overlay.style.top = 0;
        overlay.style.left = 0;
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = '';
        overlay.style.zIndex = 9999;
        overlay.style.pointerEvents = 'auto'; // Ensure overlay captures clicks

        // Create a loading spinner element
        const spinner = document.createElement('div');
        spinner.classList.add('loading-spinner');
        spinner.style.position = 'absolute';
        spinner.style.top = '40%';
        spinner.style.left = '45%';
        spinner.style.transform = 'translate(-50%, -50%)';
        spinner.style.border = '7px solid #f3f3f3'; /* Light gray */
        spinner.style.borderTop = '7px solid #d6684b'; 
        spinner.style.borderRadius = '50%';
        spinner.style.width = '80px';
        spinner.style.height = '80px';
        spinner.style.animation = 'spin 3s linear infinite';
        overlay.appendChild(spinner);

        // Add the overlay to the cart-drawer
        cartDrawer.appendChild(overlay);
      }

      // Disable interactions with the cart drawer
      cartDrawer.style.pointerEvents = 'none';

      // Show the overlay
      overlay.style.display = 'block';
    }
  }

  function hideLoadingOverlay() {
    // Find the cart-drawer element and hide the overlay after a 2-second delay
    setTimeout(() => {
      const cartDrawer = document.querySelector('.cart-drawer__inner');
      if (cartDrawer) {
        const overlay = cartDrawer.querySelector('#loading-overlay');
        if (overlay) {
          overlay.style.display = 'none'; // Hide the overlay
        }

        // Re-enable interactions with the cart drawer
        cartDrawer.style.pointerEvents = 'auto';
      }
    }, 1000);
  }

  function updateCartUI(cartData) {
    // Update the total item count in the cart
    const cartQuantityElement = document.querySelector('.cart-quantity');
    if (cartQuantityElement) {
      cartQuantityElement.textContent = cartData.item_count; // Update the total item count in the cart
    }

    // Optionally, reload only the cart drawer or specific sections (like cart items)
    const cartDrawer = document.querySelector('.cart-drawer__inner');
    if (cartDrawer) {
      fetch(`${Shopify.routes.root}cart?view=drawer`)
        .then(response => response.text())
        .then(html => {
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');
          const newCartDrawerContent = doc.querySelector('.cart-drawer__inner');
          if (newCartDrawerContent) {
            cartDrawer.innerHTML = newCartDrawerContent.innerHTML; // Update only the cart drawer content
          }
        })
        .catch(error => console.error('Error updating cart drawer:', error));
    }

    // Hide the loading overlay after a 2-second delay
    hideLoadingOverlay();

    // Optionally, update the cart items dynamically
    console.log(cartData);
  }


document.addEventListener('DOMContentLoaded', function () {
    // Select all minus buttons
    const minusButtons = document.querySelectorAll('.quantity-button.minus');

    minusButtons.forEach(minusButton => {
        // Extract lineKey from the button's onclick attribute
        const lineKey = minusButton.getAttribute('onclick').match(/'([^']+)'/)[1];
        const quantityInput = document.querySelector(`input[data-line-key="${lineKey}"]`);

        // Disable the minus button if the quantity is 1
        if (parseInt(quantityInput.value) === 1) {
            minusButton.disabled = true;
        }

        // Add event listeners to update button states when quantity changes
        quantityInput.addEventListener('input', function () {
            if (parseInt(quantityInput.value) === 1) {
                minusButton.disabled = true;
            } else {
                minusButton.disabled = false;
            }
        });
    });
});


</script>











