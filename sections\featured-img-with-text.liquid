<div id="section-{{ section.id }}" 
     class="{% if section.settings.show_on_desktop == false %} hide-desktop{% endif %} 
            {% if section.settings.show_on_mobile == false %} hide-mobile{% endif %} 
            {{ section.settings.section_style }}" 
     data-section-id="{{ section.id }}" 
     data-section-type="featured-row-section">
  
  <div class="box" style="padding:0;">
    <div class="wrapper">

<div class=" grounding-section">
    <div class="grid__item content">
      {%- unless section.settings.heading == blank -%}
      <p class="info_heading" style="font-size:30px; font-weight: 600; color: black;">{{ section.settings.heading }} </p>
      {%- endunless -%}
      {%- unless section.settings.description == blank -%}
        <div class="descriptions descriptions-{{ section.settings.text_alignment }}">
        <p>{{ section.settings.description }}</p>
        
        {%- endunless -%}

<!-- Dropdown Details-->
{%- unless block.settings.heading -%}
  
<section class="info-section">
  <div class="info-container">
    <div class="info-right">
    {% for block in section.blocks %}
      <div class="info-item">
        <!-- info main button with toggle action -->
        <button class="info-main" onclick="toggleinfo(this)">
          {{ block.settings.heading }}
          <span class="toggle-icon"><img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/down-ico.webp?v=1735298077" width="10"/></span>
        </button>
        <!-- info answer, initially hidden -->
        <div class="info-answer" style="display: none;">
          <span>{{ block.settings.description }}</span>
        </div>
      </div>
    {% endfor %}
      
  </div>
</div>
</section>
    {%- endunless -%}
<script>
  function toggleinfo(button) {
    const answer = button.nextElementSibling; // Get the adjacent info answer div
    const icon = button.querySelector(".toggle-icon"); // Get the icon inside the button
    
    // Toggle visibility of the answer div and change the icon
    if (answer.style.display === "none" || !answer.style.display) {
      answer.style.display = "block"; // Show the answer
      icon.innerHTML = '<img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/up-ico.webp?v=1735298078" width="10"/>'; // Change the icon to minus
    } else {
      answer.style.display = "none"; // Hide the answer
      icon.innerHTML = '<img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/down-ico.webp?v=1735298077" width="10"/>'; // Change the icon to plus
    }
  }
</script>
          
      </div>
    </div>

{% if section.settings.features_video %}
  <div class="grid__item image-container image-container-{{ section.settings.image_alignment }}">
  <video-media autoplay loop muted class="feature-video">
      {{ section.settings.features_video | video_tag: playsinline: true, muted: true, loop: true, preload: 'metadata', class: 'rounded' }}
  </video-media>


</div>
  {% else %}
  {%- unless section.settings.image_1 == blank -%}
    <div class="grid__item image-container image-container-{{ section.settings.image_alignment }}">
       <img class="img-size" src="{{ section.settings.image_1 | img_url: '1500x1500' }}" alt="image" />
    </div>
    {%- endunless -%}

{% endif %}
  
  </div>
</div>
  </div>
</div>

{% schema %}
  {
  "name": "Image With Text",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_on_desktop",
      "label": "Show on desktop",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_on_mobile",
      "label": "Show on mobile",
      "default": true
    },
    {
      "type": "image_picker",
      "id": "image_1",
      "label": "Image 1"
    },
    {
      "type": "video",
      "id": "features_video",
      "label": "Video 1"
    },
    {
      "type": "select",
      "id": "image_alignment",
      "label": "Image Alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "right",
          "label": "Right"
        }
      ],
      "default": "left"
    },
    {
      "type": "select",
      "id": "text_alignment",
      "label": "Text Alignment",
      "options": [
        {
          "value": "left",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "right",
          "label": "Right"
        },
        {
          "value": "justify",
          "label": "Justify"
        }
      ],
      "default": "left"
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "Button Style",
      "options": [
        {
          "value": "default",
          "label": "Default"
        },
        {
          "value": "full",
          "label": "Full"
        },
        {
          "value": "outline",
          "label": "Outline"
        }
      ],
      "default": "default"
    },
    {
      "type": "url",
      "id": "button_link",
      "label": "Button Link"
    }
  ],
    "blocks": [
    {
      "name": "Dropdown Description",
      "type": "dropdown_description",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "label": "Heading"
        },
        {
          "type": "richtext",
          "id": "description",
          "label": "Description"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Image with Text"
    }
  ]
}  
{% endschema %}

{% stylesheet %}
{% endstylesheet %}

{% javascript %}
{% endjavascript %}

<style>

  .feature-video {
--default-aspect-ratio: 1/1;

  }

  /* RTE paragraph spacing and formatting for Featured Image with Text */
  .descriptions p {
    margin: 0 0 16px 0;
    line-height: 1.6;
  }
  .descriptions p:last-child {
    margin-bottom: 0;
  }
  .descriptions ul,
  .descriptions ol {
    margin: 16px 0;
    padding-left: 1.25rem;
  }
  .descriptions li {
    margin: 8px 0;
    line-height: 1.6;
  }
  .descriptions strong {
    font-weight: 700;
  }
  .descriptions em {
    font-style: italic;
  }
  .descriptions a {
    text-decoration: underline;
  }
  .descriptions a:hover {
    text-decoration: none;
  }

  /* RTE paragraph spacing for info answer content */
  .info-answer span p {
    margin: 0 0 12px 0;
    line-height: 1.5;
  }
  .info-answer span p:last-child {
    margin-bottom: 0;
  }
  .info-answer span ul,
  .info-answer span ol {
    margin: 12px 0;
    padding-left: 1rem;
  }
  .info-answer span li {
    margin: 6px 0;
    line-height: 1.5;
  }

  @media(max-width: 767px) {
    /* Mobile-specific RTE paragraph adjustments */
    .descriptions p {
      margin: 0 0 14px 0;
      line-height: 1.5;
    }
    .descriptions ul,
    .descriptions ol {
      margin: 14px 0;
    }
    .descriptions li {
      margin: 6px 0;
    }
    .info-answer span p {
      margin: 0 0 10px 0;
    }
    .info-answer span ul,
    .info-answer span ol {
      margin: 10px 0;
    }
    .info-answer span li {
      margin: 4px 0;
    }
  }

    </style>