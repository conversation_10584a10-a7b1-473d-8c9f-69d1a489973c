{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant ie09Mc0OKokt6REvX" data-instant-id="e09Mc0OKokt6REvX" data-instant-version="3.0.4" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-e09Mc0OKokt6REvX.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;700&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="iXua1fvtQIl5K0vJF">
    <div class="ibwTkpur3uYQ4Tdfu" data-instant-type="container" id="ibwTkpur3uYQ4Tdfu">
      <div class="inWpqNDE9I3jrfc18" data-instant-type="container">
        <div class="izyyQy23Z3sgleNMK" data-instant-type="container">
          <div data-instant-type="text" class="instant-rich-text iTnLKyvOaSkendneX">
            <div>{{ section.settings.text_TnLKyvOaSkendneX }}</div>
          </div>
          <div data-instant-type="text" class="instant-rich-text iJsyFxRTLNke57fi5">
            <div>{{ section.settings.text_JsyFxRTLNke57fi5 }}</div>
          </div>
        </div>
        <div class="iwIg38yOnBY0Jmhok" data-instant-type="container">
          <div class="iZgIMXytLbLf5XhNX" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
            <div data-state="closed" class="i1IouHdojIw47ukbv" data-instant-type="accordion-item">
              <button class="iWxEto7MzZn2iYntl" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text iU8rKKUZXwKH6WriW">
                  <div>{{ section.settings.text_U8rKKUZXwKH6WriW }}</div>
                </div>
                <div data-instant-type="icon" class="ilOUYo8uDIkgz2B3A">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="iCbsdkobeCpP8VAGq" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div data-instant-type="text" class="instant-rich-text iaHGyhKZzs1DK9rlQ">
                  <div>{{ section.settings.text_aHGyhKZzs1DK9rlQ }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="i7ZAoKHNNIHHYdcfC" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
            <div data-state="closed" class="iCBG4yTMGeluSGdoe" data-instant-type="accordion-item">
              <button class="iIc90w0bLxJNpBiv0" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text iEnLAO5HAc8UqaAg2">
                  <div>{{ section.settings.text_EnLAO5HAc8UqaAg2 }}</div>
                </div>
                <div data-instant-type="icon" class="iaVlK0QiW9nLu96mI">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="i3VPaGxioHqMNBbCY" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div data-instant-type="text" class="instant-rich-text iw9HdM5VVybLy8aRm">
                  <div>{{ section.settings.text_w9HdM5VVybLy8aRm }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="igoIpNZOn1uK46B6s" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
            <div data-state="closed" class="iUQhC20tKFYdgXJ9R" data-instant-type="accordion-item">
              <button class="iQ4nE52UWfXPEZzB7" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text iVPdWa2CcI4zv8Jht">
                  <div>{{ section.settings.text_VPdWa2CcI4zv8Jht }}</div>
                </div>
                <div data-instant-type="icon" class="iR4QjFVC5ECeJ5RCt">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="iVL7iieqGvlKJdw2c" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div data-instant-type="text" class="instant-rich-text i9yZMN1xHO7IBy33Z">
                  <div>{{ section.settings.text_9yZMN1xHO7IBy33Z }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="i77Xa1XwzStoBzi1H" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
            <div data-state="closed" class="ieozBmEBY4c924dz2" data-instant-type="accordion-item">
              <button class="iN0k1d6fxXiShiYFx" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text iIprZI24PRPlsAcTe">
                  <div>{{ section.settings.text_IprZI24PRPlsAcTe }}</div>
                </div>
                <div data-instant-type="icon" class="ig0JgUpt5U1eBiHb7">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="idMzjohXBxdhy02U6" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div data-instant-type="text" class="instant-rich-text ivdRt3iKB6GDKIO0I">
                  <div>{{ section.settings.text_vdRt3iKB6GDKIO0I }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="iXVbFquGU3jRM4zkz" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
            <div data-state="closed" class="i9HxYMqTjR0zVrzbL" data-instant-type="accordion-item">
              <button class="i6DNCFhZ3oCceFac7" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text iQsx8uIZegXgeIli8">
                  <div>{{ section.settings.text_Qsx8uIZegXgeIli8 }}</div>
                </div>
                <div data-instant-type="icon" class="i7FSD2DXi3CVnrsDj">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="iRh9BRsD2EXgfz2i9" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div data-instant-type="text" class="instant-rich-text ifwvqAYYBDOf4kcBI">
                  <div>{{ section.settings.text_fwvqAYYBDOf4kcBI }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="iLHI8uxQ0u62qJOyn" data-instant-type="container">
          <div class="itxODNfcW6XWAvXvh" data-instant-type="container">
            <div class="isLZKk1jDbccyJHO7" data-instant-type="container">
              <div data-instant-type="text" class="instant-rich-text iKdFfwvcjMSTDfMq1">
                <div>{{ section.settings.text_KdFfwvcjMSTDfMq1 }}</div>
              </div>
              <div data-instant-type="text" class="instant-rich-text iORbzkKWnfUR18bS9">
                <div>{{ section.settings.text_ORbzkKWnfUR18bS9 }}</div>
              </div>
            </div>
            <a class="iINJoewZsgerImHsS" data-instant-type="container" href="{{ section.settings.url_INJoewZsgerImHsS | default: 'null' }}" rel="noopener noreferrer">
              <div data-instant-type="text" class="instant-rich-text icNSpTBZ9j5JePajl">
                <div>{{ section.settings.text_cNSpTBZ9j5JePajl }}</div>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.4",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison FAQ",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "richtext",
      "id": "text_TnLKyvOaSkendneX",
      "label": "Heading",
      "default": "<p>Frequently Asked Questions</p><p>about Hike Footwear Cloud</p>"
    },
    {
      "type": "richtext",
      "id": "text_JsyFxRTLNke57fi5",
      "label": "Text",
      "default": "<p>Got questions? We&apos;ve got answers. Here are some common queries about Hike Footwear:</p>"
    },
    {
      "type": "richtext",
      "id": "text_U8rKKUZXwKH6WriW",
      "label": "Question",
      "default": "<p>How long does it take to adjust to Hike Footwear?</p>"
    },
    {
      "type": "richtext",
      "id": "text_aHGyhKZzs1DK9rlQ",
      "label": "Text",
      "default": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>"
    },
    {
      "type": "richtext",
      "id": "text_EnLAO5HAc8UqaAg2",
      "label": "Question",
      "default": "<p>How long does it take to adjust to Hike Footwear?</p>"
    },
    {
      "type": "richtext",
      "id": "text_w9HdM5VVybLy8aRm",
      "label": "Text",
      "default": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>"
    },
    {
      "type": "richtext",
      "id": "text_VPdWa2CcI4zv8Jht",
      "label": "Question",
      "default": "<p>How long does it take to adjust to Hike Footwear?</p>"
    },
    {
      "type": "richtext",
      "id": "text_9yZMN1xHO7IBy33Z",
      "label": "Text",
      "default": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>"
    },
    {
      "type": "richtext",
      "id": "text_IprZI24PRPlsAcTe",
      "label": "Question",
      "default": "<p>How long does it take to adjust to Hike Footwear?</p>"
    },
    {
      "type": "richtext",
      "id": "text_vdRt3iKB6GDKIO0I",
      "label": "Text",
      "default": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>"
    },
    {
      "type": "richtext",
      "id": "text_Qsx8uIZegXgeIli8",
      "label": "Question",
      "default": "<p>How long does it take to adjust to Hike Footwear?</p>"
    },
    {
      "type": "richtext",
      "id": "text_fwvqAYYBDOf4kcBI",
      "label": "Text",
      "default": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>"
    },
    {
      "type": "richtext",
      "id": "text_KdFfwvcjMSTDfMq1",
      "label": "Heading",
      "default": "<p>Still have questions? </p><p>Our support team is here for you 24/7</p>"
    },
    {
      "type": "richtext",
      "id": "text_ORbzkKWnfUR18bS9",
      "label": "Text",
      "default": "<p>We&apos;re here to help! Our customer service team is ready to assist you with any inquiries.</p>"
    },
    {
      "type": "url",
      "id": "url_INJoewZsgerImHsS",
      "label": "Button URL"
    },
    {
      "type": "richtext",
      "id": "text_cNSpTBZ9j5JePajl",
      "label": "Button",
      "default": "<p>Contact Us</p>"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison FAQ",
      "settings": {
        "text_TnLKyvOaSkendneX": "<p>Frequently Asked Questions</p><p>about Hike Footwear Cloud</p>",
        "text_JsyFxRTLNke57fi5": "<p>Got questions? We&apos;ve got answers. Here are some common queries about Hike Footwear:</p>",
        "text_U8rKKUZXwKH6WriW": "<p>How long does it take to adjust to Hike Footwear?</p>",
        "text_aHGyhKZzs1DK9rlQ": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>",
        "text_EnLAO5HAc8UqaAg2": "<p>How long does it take to adjust to Hike Footwear?</p>",
        "text_w9HdM5VVybLy8aRm": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>",
        "text_VPdWa2CcI4zv8Jht": "<p>How long does it take to adjust to Hike Footwear?</p>",
        "text_9yZMN1xHO7IBy33Z": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>",
        "text_IprZI24PRPlsAcTe": "<p>How long does it take to adjust to Hike Footwear?</p>",
        "text_vdRt3iKB6GDKIO0I": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>",
        "text_Qsx8uIZegXgeIli8": "<p>How long does it take to adjust to Hike Footwear?</p>",
        "text_fwvqAYYBDOf4kcBI": "<p>Most customers report feeling comfortable immediately, with full benefits realized within 1-2 weeks of regular wear.</p>",
        "text_KdFfwvcjMSTDfMq1": "<p>Still have questions? </p><p>Our support team is here for you 24/7</p>",
        "text_ORbzkKWnfUR18bS9": "<p>We&apos;re here to help! Our customer service team is ready to assist you with any inquiries.</p>",
        "text_cNSpTBZ9j5JePajl": "<p>Contact Us</p>"
      }
    }
  ]
}
{% endschema %}
