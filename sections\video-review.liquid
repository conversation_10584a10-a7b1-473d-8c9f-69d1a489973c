{%- liquid
  if section.settings.full_width
    assign full_width = true
  endif

  if section.settings.title == blank and section.settings.subtitle == blank
    assign no_header = true
  endif

  capture column_width
    render "block-width"
  endcapture
-%}

<style>
  #section-{{ section.id }} {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .section-full-width {
    width: 100%;
    max-width: 100%;
  }

  .section-no-title {
    padding-top: 10px;
  }

  .video-header__title {
    font-size: 30px;
    font-weight: bold;
    margin-bottom: 10px;
  }

  .video-header__subtitle {
    font-size: 19px;
    color: #777;
    margin-bottom: 20px;
  }

  .grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
  }

  .grid__item {
    flex: 1 1 calc(33.333% - 20px);
    box-sizing: border-box;
  }

  .main-wrapper, .wrapper-full {
    margin: 0 auto;
    max-width: 1200px;
    padding: 0 15px;
  }

  .media-wrapper {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
    height: 0;
    overflow: hidden;
    background-color: #000;
    border-radius: 8px;
  }

  .media-wrapper iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 0;
  }
.medium--hide,
.large--hide,
.small--hide{
  display: none;
}

.video-header {
    justify-items: center;
    text-align: center;
}
@media (max-width: 769px) {
  .grid {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

  .video-container {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  overflow: hidden;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

  .video-reviews {
    padding-bottom: 20px;
}
</style>

<div id="section-{{ section.id }}"
     class="{% unless section.settings.show_on_desktop %}medium--hide large--hide{% endunless %}{% unless section.settings.show_on_mobile %}small--hide{% endunless %}{% if full_width %} section-full-width{% endif %}{% if no_header %} section-no-title{% endif %} {{ section.settings.section_style }}"
     data-section-id="{{ section.id }}" data-section-type="featured-video-section"
     {% unless section.settings.show_on_desktop %}style="display:none;"{% endunless %}>
  <div class="video-reviews">
    <div class="{%- if full_width -%}wrapper-full{%- else -%}main-wrapper{%- endif -%}">

      {%- unless no_header -%}
        <div class="grid">
          <div class="grid__item large--eight-twelfths push--large--two-twelfths">
            <div class="video-header">
              {%- unless section.settings.title == blank -%}
                <h2 class="video-header__title">{{ section.settings.title | escape }}</h2>
              {%- endunless -%}

              {%- unless section.settings.subtitle == blank -%}
                <p class="video-header__subtitle">{{ section.settings.subtitle | escape }}</p>
              {%- endunless -%}
            </div>
          </div>
        </div>
      {%- endunless -%}

      {%- if section.blocks.size > 0 -%}
        <div class="grid {% if full_width %}grid-full{% else %}grid-spacer{% endif %}">
          <!-- Blocks -->
          {%- for block in section.blocks -%}
  <div class="grid__item {{ column_width }}" {{ block.shopify_attributes }}>
    {%- if block.settings.video_url != blank -%}
      <div class="media-wrapper video-container">
        <iframe 
          class="media lazyloaded" 
          width="100%" 
          height="auto" 
          src="{{ block.settings.video_url }}" 
          frameborder="0" 
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" 
          referrerpolicy="strict-origin-when-cross-origin" 
          allowfullscreen 
          loading="lazy">
        </iframe>
      </div>
    {%- endif -%}
  </div>
{%- endfor -%}
        </div>
      {%- else -%}
        {%- render "no-blocks" -%}
      {%- endif -%}
    </div>
  </div>
</div>


{% schema %}
  {
    "name": "Video reviews",
	  "class": "featured-video-section",
	  "max_blocks": 3,
    "settings": [
       {
        "type": "checkbox",
        "id": "show_on_desktop",
        "label": "Show on desktop",
        "default": true
      },
      {
        "type": "checkbox",
        "id": "show_on_mobile",
        "label": "Show on mobile",
        "default": true
      },
	    {
        "type": "checkbox",
        "id": "full_width",
        "label": "Full width",
        "default": false
      },
      {
        "type": "text",
        "id": "title",
        "label": "Video Reviews",
      },
	    {
        "type": "text",
        "id": "subtitle",
        "label": "Subheading"
      },
      {
        "type": "select",
        "id": "section_style",
        "label": "Section Style",
        "default": "section-blank",
        "options": [
          {
            "value": "section-blank",
            "label": "Section blank"
          },
		      {
            "value": "section-default",
            "label": "Section default"
          },
          {
            "value": "section-border",
            "label": "Section border"
          }
        ]
      }
    ],
	  "blocks" : [
      {
        "type": "video",
        "name": "Video",
        "settings": [
          {
            "type": "text",
            "id": "title",
           "label":"Title"
          },
          {
            "type": "url",
            "id": "video_url",
             "label":"Video Embed URL"
          }
        ]
      }
    ],
    "presets": [
      {
        "name": "Video Review",
        "blocks": [
          {
            "type": "video"
          }
        ]
      }
    ]
  }
{% endschema %}
