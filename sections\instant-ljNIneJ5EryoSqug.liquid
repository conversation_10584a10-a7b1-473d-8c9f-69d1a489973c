{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant iljNIneJ5EryoSqug" data-instant-id="ljNIneJ5EryoSqug" data-instant-version="3.0.4" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-ljNIneJ5EryoSqug.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&amp;family=Inter:wght@400&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="ifknHBN4GEYXl3xau">
    {%- liquid
      assign product_h6Z81zoUOw6rcwgN_handle = 'hf-cloud'
      assign product_h6Z81zoUOw6rcwgN = section.settings.product_h6Z81zoUOw6rcwgN | default: all_products[product_h6Z81zoUOw6rcwgN_handle]
      assign product_h6Z81zoUOw6rcwgN_variant = product_h6Z81zoUOw6rcwgN.selected_or_first_available_variant

      if product_h6Z81zoUOw6rcwgN.selected_variant
        assign product_h6Z81zoUOw6rcwgN_variant = product_h6Z81zoUOw6rcwgN.selected_variant
      endif

      assign product_h6Z81zoUOw6rcwgN_image = product_h6Z81zoUOw6rcwgN_variant.featured_image | default: product_h6Z81zoUOw6rcwgN.featured_image
    -%}
    <!--  -->
    {%- liquid
      assign loading = 'eager'
      assign fetchpriority = 'auto'
      if section.location == 'footer'
        assign loading = 'lazy'
      elsif section.location == 'header'
        assign fetchpriority = 'high'
      elsif section.location == 'template'
        if section.index == 1
          assign fetchpriority = 'high'
        elsif section.index > 2
          assign loading = 'lazy'
        endif
      endif
    -%}
    <form class="ih6Z81zoUOw6rcwgN" data-instant-type="container" id="ih6Z81zoUOw6rcwgN" data-instant-form-product-url="{{ product_h6Z81zoUOw6rcwgN.url }}" data-instant-form-variant-id="{{ product_h6Z81zoUOw6rcwgN_variant.id }}">
      <div class="iQ3mBAnIk7sW5BZsA" data-instant-type="container">
        <div class="i3VpEyEdYjSxQ5DJp" data-instant-type="container">
          <p data-instant-dynamic-content-source="TITLE" data-instant-type="text" class="iITzx5WnItC9vxU3f">{{ product_h6Z81zoUOw6rcwgN.title }}</p>
          <div data-instant-type="text" class="instant-rich-text iRDTYbz6Eg9cbH0A0">
            <div>{{ section.settings.text_RDTYbz6Eg9cbH0A0 }}</div>
          </div>
          <div class="isHMDgD9lObxNiibC" data-instant-type="container">
            <div class="iaSOfTMNjcTDL3Y9F" data-instant-type="container">
              {% if section.settings.image_aSOfTMNjcTDL3Y9F and section.settings.image_aSOfTMNjcTDL3Y9F != blank %}
                {{ section.settings.image_aSOfTMNjcTDL3Y9F | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_aSOfTMNjcTDL3Y9F.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/oSopK4DagRW18jcX/5bfbd91939f6b831a45f1431b3d502d2166cf297.svg" width="96" height="17" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
              {% endif %}
            </div>
            <a as="p" data-instant-type="text" class="iFiGqbvTJhsznicWe" href="{{ section.settings.url_FiGqbvTJhsznicWe | default: '#shopify-section-template--23938854453588__instant_rse_fowu_c4_a_cffmqw_K3Fh8e' }}" rel="noopener noreferrer">
              <div class="instant-rich-text">
                <div>{{ section.settings.text_FiGqbvTJhsznicWe }}</div>
              </div>
            </a>
          </div>
        </div>
        <div class="ikEe1M9cS1xjvBB7e" data-instant-type="container">
          <div class="instant-slider-container iNj9GYpLVIYOiYKgl" data-instant-type="slider-container">
            <div class="idXak2sME70F4zKTS" data-instant-type="container">
              <div class="iM7Tou23vsRREM7jF" data-instant-type="container">
                <div class="iI0o9KvEq6A0p3vss" data-instant-type="container">
                  {% if section.settings.image_I0o9KvEq6A0p3vss and section.settings.image_I0o9KvEq6A0p3vss != blank %}
                    {{ section.settings.image_I0o9KvEq6A0p3vss | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_I0o9KvEq6A0p3vss.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/h7PX2ZN4BIKNk7xQ/icon-1.svg" width="33" height="32" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
                <div data-instant-type="text" class="instant-rich-text izOgTgb8mIuJacIjm">
                  <div>{{ section.settings.text_zOgTgb8mIuJacIjm }}</div>
                </div>
              </div>
              <div class="ivohZTRo0vKwHfjKd" data-instant-type="container">
                <div class="iRQVStSj0ZTecingV" data-instant-type="container">
                  {% if section.settings.image_RQVStSj0ZTecingV and section.settings.image_RQVStSj0ZTecingV != blank %}
                    {{ section.settings.image_RQVStSj0ZTecingV | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_RQVStSj0ZTecingV.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/CzN223eSTSEjU6f4/icon-2.svg" width="33" height="32" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
                <div data-instant-type="text" class="instant-rich-text i8UFC00MBjjjRMnQN">
                  <div>{{ section.settings.text_8UFC00MBjjjRMnQN }}</div>
                </div>
              </div>
              <div class="im60JpJKrA44DcrDM" data-instant-type="container">
                <div class="iCxbL5uRaauOBuz2v" data-instant-type="container">
                  {% if section.settings.image_CxbL5uRaauOBuz2v and section.settings.image_CxbL5uRaauOBuz2v != blank %}
                    {{ section.settings.image_CxbL5uRaauOBuz2v | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_CxbL5uRaauOBuz2v.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/yJwlXGFVjM17Bl8p/icon-3.svg" width="33" height="32" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
                <div data-instant-type="text" class="instant-rich-text ikBlDbgQguQ2hP5AP">
                  <div>{{ section.settings.text_kBlDbgQguQ2hP5AP }}</div>
                </div>
              </div>
            </div>
            <div class="ixoRcaatGYN3EUFit" data-instant-type="container">
              <script type="application/json" id="instant-slider-rwKwjc0g6mWpvFQu-params">
                { "breakpoints": { "0": { "speed": 300, "direction": "horizontal", "slidesPerView": 1, "freeMode": false, "centeredSlides": true, "centerInsufficientSlides": true, "spaceBetween": 0 }, "1025": { "speed": 300, "direction": "horizontal", "slidesPerView": 1, "freeMode": false, "centeredSlides": true, "centerInsufficientSlides": true, "spaceBetween": 0 } }, "navigation": false, "watchOverflow": true, "thumbs": { "slideThumbActiveClass": "instant-slider-thumb-active", "thumbsContainerClass": "instant-slider-thumbs" }, "a11y": { "enabled": true, "notificationClass": "instant-slider-notification" }, "containerModifierClass": "instant-slider-", "noSwipingClass": "instant-slider-no-swiping", "slideClass": "instant-slider-slide", "slideBlankClass": "instant-slider-slide-blank", "slideActiveClass": "instant-slider-slide-active", "slideVisibleClass": "instant-slider-slide-visible", "slideFullyVisibleClass": "instant-slider-slide-fully-visible", "slideNextClass": "instant-slider-slide-next", "slidePrevClass": "instant-slider-slide-prev", "wrapperClass": "instant-slider-wrapper", "lazyPreloaderClass": "instant-slider-lazy-preloader" }
              </script>
              <div class="instant-slider irwKwjc0g6mWpvFQu" data-instant-slider-id="rwKwjc0g6mWpvFQu" id="rwKwjc0g6mWpvFQu" data-instant-type="slider">
                <div class="instant-slider-wrapper">
                  {%- liquid
                    assign images_without_variant = '' | split: ''
                    assign featured_image = product_h6Z81zoUOw6rcwgN_variant.featured_image | default: product_h6Z81zoUOw6rcwgN_image

                    for image in product_h6Z81zoUOw6rcwgN.images
                      if image.id != featured_image.id
                        assign image_element = image | sort
                        assign images_without_variant = images_without_variant | concat: image_element
                      endif
                    endfor

                    assign images = featured_image | sort | concat: images_without_variant
                  -%}
                  {%- for image in images -%}
                    {%- if forloop.length > 0 -%}
                      {%- assign repeater_index_LcM4HZ1xHZouy6QF = forloop.index0 -%}
                      <div class="instant-slider-slide iLcM4HZ1xHZouy6QF" data-instant-type="slider-slide">
                        <div data-instant-type="image" class="iAQcdN6yrlmistgjB"><img alt="{{ image.alt }}" src="{{ image | image_url: width: 800 }}" data-instant-dynamic-content-source="REPEATER" data-instant-repeater-id="LcM4HZ1xHZouy6QF" data-instant-repeater-index="{{ repeater_index_LcM4HZ1xHZouy6QF }}" width="{{ image.width }}" height="{{ image.height }}" srcSet="{{ image | image_url: width: 360 }} 360w, {{ image | image_url: width: 640 }} 640w, {{ image | image_url: width: 750 }} 750w, {{ image | image_url: width: 828 }} 828w, {{ image | image_url: width: 1080 }} 1080w, {{ image | image_url: width: 1200 }} 1200w, {{ image | image_url: width: 1920 }} 1920w, {{ image | image_url: width: 2048 }} 2048w, {{ image | image_url: width: 3840 }} 3840w" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main"></div>
                      </div>
                    {%- endif -%}
                  {%- endfor -%}
                </div>
              </div>
            </div>
            <div class="instant-slider-container i6ODG6pyG3EkjBjSE" data-instant-type="slider-container">
              <script type="application/json" id="instant-slider-SxpqPABLWTNqVEa8-params">
                { "breakpoints": { "0": { "speed": 300, "direction": "horizontal", "slidesPerView": 8, "freeMode": false, "centeredSlides": false, "centerInsufficientSlides": false, "spaceBetween": 16 }, "769": { "speed": 300, "direction": "horizontal", "slidesPerView": 6, "freeMode": false, "centeredSlides": false, "centerInsufficientSlides": false, "spaceBetween": 16 } }, "navigation": false, "watchOverflow": true, "watchSlidesProgress": true, "a11y": { "enabled": true, "notificationClass": "instant-slider-notification" }, "containerModifierClass": "instant-slider-", "noSwipingClass": "instant-slider-no-swiping", "slideClass": "instant-slider-slide", "slideBlankClass": "instant-slider-slide-blank", "slideActiveClass": "instant-slider-slide-active", "slideVisibleClass": "instant-slider-slide-visible", "slideFullyVisibleClass": "instant-slider-slide-fully-visible", "slideNextClass": "instant-slider-slide-next", "slidePrevClass": "instant-slider-slide-prev", "wrapperClass": "instant-slider-wrapper", "lazyPreloaderClass": "instant-slider-lazy-preloader" }
              </script>
              <div class="instant-slider instant-slider-thumbs iSxpqPABLWTNqVEa8" data-instant-slider-id="rwKwjc0g6mWpvFQu" id="SxpqPABLWTNqVEa8" data-instant-type="thumbnails">
                <div class="instant-slider-wrapper">
                  {%- liquid
                    assign images_without_variant = '' | split: ''
                    assign featured_image = product_h6Z81zoUOw6rcwgN_variant.featured_image | default: product_h6Z81zoUOw6rcwgN_image

                    for image in product_h6Z81zoUOw6rcwgN.images
                      if image.id != featured_image.id
                        assign image_element = image | sort
                        assign images_without_variant = images_without_variant | concat: image_element
                      endif
                    endfor

                    assign images = featured_image | sort | concat: images_without_variant
                  -%}
                  {%- for image in images -%}
                    {%- if forloop.length > 0 -%}
                      {%- assign repeater_index_VGUSDslYV70gXrzV = forloop.index0 -%}
                      <div class="instant-slider-slide iVGUSDslYV70gXrzV" data-instant-type="slider-slide">
                        <div data-instant-type="image" class="iPPEhuXp1Uu8NR9yB"><img alt="{{ image.alt }}" src="{{ image | image_url: width: 800 }}" data-instant-dynamic-content-source="REPEATER" data-instant-repeater-id="VGUSDslYV70gXrzV" data-instant-repeater-index="{{ repeater_index_VGUSDslYV70gXrzV }}" width="{{ image.width }}" height="{{ image.height }}" srcSet="{{ image | image_url: width: 360 }} 360w, {{ image | image_url: width: 640 }} 640w, {{ image | image_url: width: 750 }} 750w, {{ image | image_url: width: 828 }} 828w, {{ image | image_url: width: 1080 }} 1080w, {{ image | image_url: width: 1200 }} 1200w, {{ image | image_url: width: 1920 }} 1920w, {{ image | image_url: width: 2048 }} 2048w, {{ image | image_url: width: 3840 }} 3840w" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main"></div>
                      </div>
                    {%- endif -%}
                  {%- endfor -%}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="iAtLp7fxIo2SnkSKv" data-instant-type="container">
          <div class="iDuV9Lz1j6JencO1Z" data-instant-type="container">
            <div class="iwE4l8Wvf1mT0fHYL" data-instant-type="container">
              <div class="iRmlkWomKP9Cghrij" data-instant-type="container">
                <p data-instant-dynamic-content-source="TITLE" data-instant-type="text" class="iy7dd4YeII3IaVMOA">{{ product_h6Z81zoUOw6rcwgN.title }}</p>
                <div data-instant-type="text" class="instant-rich-text iwap7Ppej6isFxD9b">
                  <div>{{ section.settings.text_wap7Ppej6isFxD9b }}</div>
                </div>
              </div>
              <div class="ilyxufkpdJCU4ow8O" data-instant-type="container">
                <div class="iUH0sr0PEAxQ9mdAJ" data-instant-type="container">
                  <p data-instant-dynamic-content-source="PRICE" data-instant-type="text" class="iAqZZbCpYhFLNkyeT">
                    {%- liquid
                      assign variant_price = product_h6Z81zoUOw6rcwgN_variant.price

                      if product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation
                        assign variant_price = product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation.price
                      endif
                    -%}
                    {{- variant_price | money -}}
                  </p>

                  {%- liquid
                    assign style = ''
                    assign compare_at_price = product_h6Z81zoUOw6rcwgN_variant.compare_at_price
                    assign variant_price = product_h6Z81zoUOw6rcwgN_variant.price

                    if product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation
                      assign compare_at_price = product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation.compare_at_price
                      assign variant_price = product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation.price
                    endif

                    if compare_at_price <= variant_price or compare_at_price == 0 or compare_at_price == null
                      assign style = 'style="display: none;"'
                    endif
                  -%}
                  <span
                    data-instant-dynamic-content-source="COMPARE_AT"
                    class="itcyeLYD11vtEN9jr"
                    {{- style -}}
                  >
                    {{- compare_at_price | money -}}
                  </span>
                </div>
                <div class="iV1x5S9S1WaynyeO7" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text i9LfGGpIUjtw7KDDf">
                    <div>{{ section.settings.text_9LfGGpIUjtw7KDDf }}</div>
                  </div>

                  {%- liquid
                    assign style = ''

                    assign compare_at_price = product_h6Z81zoUOw6rcwgN_variant.compare_at_price
                    assign variant_price = product_h6Z81zoUOw6rcwgN_variant.price

                    if product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation
                      assign compare_at_price = product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation.compare_at_price
                      assign variant_price = product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation.price
                    endif

                    assign saved_amount = compare_at_price | minus: variant_price | times: 100 | divided_by: compare_at_price

                    if compare_at_price <= variant_price or saved_amount <= 0 or compare_at_price == null
                      assign style = 'style="display: none;"'
                    endif
                  -%}
                  <span
                    data-instant-dynamic-content-source="SAVED_PERCENTAGE"
                    class="ibYO9VjxCZZeK5S5R"
                    {{- style -}}
                  >
                    {{- saved_amount -}}
                    %
                  </span>
                </div>
              </div>
            </div>
            <div class="i46BhCUeIMLkimQTC" data-instant-type="container">
              <div class="iIdldJRJEcRvGxLtb" data-instant-type="container">
                {% if section.settings.image_IdldJRJEcRvGxLtb and section.settings.image_IdldJRJEcRvGxLtb != blank %}
                  {{ section.settings.image_IdldJRJEcRvGxLtb | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_IdldJRJEcRvGxLtb.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                {% else %}
                  <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/oSopK4DagRW18jcX/5bfbd91939f6b831a45f1431b3d502d2166cf297.svg" width="96" height="17" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                {% endif %}
              </div>
              <a as="p" data-instant-type="text" class="ifFdMhD0z9cpg9QBq" href="{{ section.settings.url_fFdMhD0z9cpg9QBq | default: 'null' }}" rel="noopener noreferrer">
                <div class="instant-rich-text">
                  <div>{{ section.settings.text_fFdMhD0z9cpg9QBq }}</div>
                </div>
              </a>
            </div>
            <div data-instant-type="text" class="instant-rich-text isVuL7ceKtKhBp20M">
              <div>{{ section.settings.text_sVuL7ceKtKhBp20M }}</div>
            </div>
            <div class="i1AGGQt6zXWFgTbk0" data-instant-type="tabs-container">
              <div class="iaQLfio7JKNsL2HIe" data-instant-type="tabs-menu">
                <button data-instant-state="inactive" data-instant-tab-id="JJlGPsB7ONy2xGVL" data-instant-tabs-id="1AGGQt6zXWFgTbk0" id="zWagCzCx2ZBkoLdu_JJlGPsB7ONy2xGVL" type="button" class="izWagCzCx2ZBkoLdu izWagCzCx2ZBkoLdu" data-instant-type="tabs-trigger" content="[object Object]">{{ section.settings.tab_title_JJlGPsB7ONy2xGVL }}</button>
                <button data-instant-state="inactive" data-instant-tab-id="hMptVU9jttzOycIH" data-instant-tabs-id="1AGGQt6zXWFgTbk0" id="zWagCzCx2ZBkoLdu_hMptVU9jttzOycIH" type="button" class="izWagCzCx2ZBkoLdu izWagCzCx2ZBkoLdu" data-instant-type="tabs-trigger" content="[object Object]">{{ section.settings.tab_title_hMptVU9jttzOycIH }}</button>
              </div>
              <div class="ioFsILXIsC4fPpxqf" data-instant-type="tabs-content">
                <div class="iJJlGPsB7ONy2xGVL" id="JJlGPsB7ONy2xGVL" data-instant-type="tabs-pane">
                  <div class="iSoS69SmmL5LZ0fiR" data-instant-type="container">
                    <div class="ig5L665RVZq90Mvai" data-instant-type="container">
                      <div class="iP9GMaZQ1DivTlRiX" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
                        <div data-state="closed" class="iyhz9mmksIQQWuCd4" data-instant-type="accordion-item">
                          <button class="ibRihjfuGOdpHrfWT" data-instant-type="accordion-header" type="button">
                            <div data-instant-type="text" class="instant-rich-text iz5UWlPOKyrYr6HdK">
                              <div>{{ section.settings.text_z5UWlPOKyrYr6HdK }}</div>
                            </div>
                            <div data-instant-type="text" class="instant-rich-text i3AtR6YdVq4PBc7sr">
                              <div>{{ section.settings.text_3AtR6YdVq4PBc7sr }}</div>
                            </div>
                          </button>
                          <div class="iSLRPx5Ze1C0aBUAt" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                            <div data-instant-type="image" class="iEEB2A4FhO5aw0N0k">
                              {% if section.settings.image_EEB2A4FhO5aw0N0k and section.settings.image_EEB2A4FhO5aw0N0k != blank %}
                                {{ section.settings.image_EEB2A4FhO5aw0N0k | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_EEB2A4FhO5aw0N0k.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                              {% else %}
                                <img alt="Placeholder" src="https://cdn.instant.so/static/templates/assets/placeholder-image.svg" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image__fallback instant-image instant-image__main">
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="irHqS1UvYgAK1EGre" data-instant-type="container"></div>
                    </div>
                  </div>
                </div>
                <div class="ihMptVU9jttzOycIH" id="hMptVU9jttzOycIH" data-instant-type="tabs-pane">
                  <div class="iC7tiignvMqoeT83s" data-instant-type="container">
                    <div class="ifJxNUzR1DGQluWNc" data-instant-type="container">
                      <div class="iiG20jCSh4kZb8BIi" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
                        <div data-state="closed" class="i4bmIWIrZKLCtZgVn" data-instant-type="accordion-item">
                          <button class="iMnnxAeaWpnC6LvST" data-instant-type="accordion-header" type="button">
                            <div data-instant-type="text" class="instant-rich-text iTfllBdbV14hd4OYY">
                              <div>{{ section.settings.text_TfllBdbV14hd4OYY }}</div>
                            </div>
                            <div data-instant-type="text" class="instant-rich-text iuGcOREhQxlfwPM6l">
                              <div>{{ section.settings.text_uGcOREhQxlfwPM6l }}</div>
                            </div>
                          </button>
                          <div class="iGeqRSgtuBBYbyWBW" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                            <div data-instant-type="image" class="iA8PW0Z8nBVvGWf3Q">
                              {% if section.settings.image_A8PW0Z8nBVvGWf3Q and section.settings.image_A8PW0Z8nBVvGWf3Q != blank %}
                                {{ section.settings.image_A8PW0Z8nBVvGWf3Q | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_A8PW0Z8nBVvGWf3Q.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                              {% else %}
                                <img alt="Placeholder" src="https://cdn.instant.so/static/templates/assets/placeholder-image.svg" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image__fallback instant-image instant-image__main">
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="iPhU2Kf4viwtbfcma" data-instant-type="container"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {%- liquid
              assign selected_options = product_h6Z81zoUOw6rcwgN_variant.options
              assign variants_option1 = product_h6Z81zoUOw6rcwgN.variants | map: 'option1'
              assign variants_option2 = product_h6Z81zoUOw6rcwgN.variants | map: 'option2'
              assign variants_option3 = product_h6Z81zoUOw6rcwgN.variants | map: 'option3'
              assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where: 'name', 'Size' | first

              if selected_option == null
                assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[1]
              endif

              assign hide_variant_picker = false

              if product_h6Z81zoUOw6rcwgN.has_only_default_variant or selected_option == null
                assign hide_variant_picker = true
              endif
            -%}
            {%- unless hide_variant_picker -%}
              <fieldset class="instant-variant-picker instant-variant-picker--select iIs4GnbkLht7wyuJ6" data-instant-type="VARIANT_PICKER">
                <legend class="instant-visually-hidden">{{ selected_option.name }}</legend>

                <select name="h6Z81zoUOw6rcwgN__{{ selected_option.name | escape }}">
                  {%- for value in selected_option.values -%}
                    {%- liquid
                      assign option_disabled = true

                      for option1_name in variants_option1
                        if selected_option.position == 1 and option1_name == value and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == selected_options[2]
                          assign option_disabled = false
                        elsif selected_option.position == 2 and option1_name == selected_options[0] and variants_option2[forloop.index0] == value and variants_option3[forloop.index0] == selected_options[2]
                          assign option_disabled = false
                        elsif selected_option.position == 3 and option1_name == selected_options[0] and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == value
                          assign option_disabled = false
                        endif
                      endfor

                      assign is_in_stock = true

                      unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify'
                        assign selected_variant = ''

                        for variant in product_h6Z81zoUOw6rcwgN.variants
                          if selected_option.position == 1 and variant.option1 == value and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                            assign selected_variant = variant
                          elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == value and variant.option3 == selected_options[2]
                            assign selected_variant = variant
                          elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == value
                            assign selected_variant = variant
                          endif
                        endfor

                        if selected_variant != '' and selected_variant.available == false
                          assign is_in_stock = false
                        endif
                      endunless
                    -%}
                    <option
                      id="Is4GnbkLht7wyuJ6-{{ selected_option.position }}-{{ forloop.index0 }}"
                      value="{{ value | escape }}"
                      {% if selected_options[1] == value %}
                        selected
                      {% endif %}
                      {% if option_disabled or is_in_stock == false %}
                        data-instant-disabled="true"
                      {% endif %}
                    >
                      {{ value }}
                      {% if option_disabled or is_in_stock == false %}- Unavailable{% endif %}
                    </option>
                  {%- endfor -%}
                </select>

                <!--  -->

                {%- for value in selected_option.values -%}
                  {% if selected_options[1] == value %}
                    <span
                      class="iWOcY9Tg1V1MT2Up8"
                      data-instant-type="selected-variant-option"
                    >
                      {{ value }}
                    </span>
                  {% endif %}
                {%- endfor -%}

                <div data-instant-type="icon" class="i3d5aDiVKKwVZx94k">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z"></path>
                  </svg>
                </div>
              </fieldset>
            {%- endunless -%}

            <div class="iQRIO4w2u9LKyJdDe" data-instant-type="container">
              <div class="ir6z0q3lmi65YS4dA" data-instant-type="container">
                <div class="iSYLJS7dVGCM5bdGE" data-instant-type="container">
                  <div class="i2lDHgh859sr8QXkp" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
                    <div data-state="closed" class="ijdfJWrPgqKG2d08r" data-instant-type="accordion-item">
                      <button class="iFGgZvO2jmlFjjFDm" data-instant-type="accordion-header" type="button">
                        <div data-instant-type="text" class="instant-rich-text iigTpZ9lKnigyJQX4">
                          <div>{{ section.settings.text_igTpZ9lKnigyJQX4 }}</div>
                        </div>
                        <div data-instant-type="text" class="instant-rich-text iEhkJ7wn1GgNg8NB5">
                          <div>{{ section.settings.text_EhkJ7wn1GgNg8NB5 }}</div>
                        </div>
                      </button>
                      <div class="ibxFtxvEWwURrv3UL" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                        <div data-instant-type="image" class="iMsxM9t9yGSAJwlWs">
                          {% if section.settings.image_MsxM9t9yGSAJwlWs and section.settings.image_MsxM9t9yGSAJwlWs != blank %}
                            {{ section.settings.image_MsxM9t9yGSAJwlWs | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_MsxM9t9yGSAJwlWs.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                          {% else %}
                            <img alt="Placeholder" src="https://cdn.instant.so/static/templates/assets/placeholder-image.svg" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image__fallback instant-image instant-image__main">
                          {% endif %}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="iNXHE4nQEACmwBJ1v" data-instant-type="container"></div>
                </div>
                {%- liquid
                  assign selected_options = product_h6Z81zoUOw6rcwgN_variant.options
                  assign variants_option1 = product_h6Z81zoUOw6rcwgN.variants | map: 'option1'
                  assign variants_option2 = product_h6Z81zoUOw6rcwgN.variants | map: 'option2'
                  assign variants_option3 = product_h6Z81zoUOw6rcwgN.variants | map: 'option3'
                  assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where: 'name', 'Size' | first

                  if selected_option == null
                    assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[1]
                  endif

                  assign hide_variant_picker = false

                  if product_h6Z81zoUOw6rcwgN.has_only_default_variant or selected_option == null
                    assign hide_variant_picker = true
                  endif
                -%}
                {%- unless hide_variant_picker -%}
                  <fieldset class="instant-variant-picker instant-variant-picker--select ia615OB3JRq6zUiHW" data-instant-type="VARIANT_PICKER">
                    <legend class="instant-visually-hidden">{{ selected_option.name }}</legend>

                    <select name="h6Z81zoUOw6rcwgN__{{ selected_option.name | escape }}">
                      {%- for value in selected_option.values -%}
                        {%- liquid
                          assign option_disabled = true

                          for option1_name in variants_option1
                            if selected_option.position == 1 and option1_name == value and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == selected_options[2]
                              assign option_disabled = false
                            elsif selected_option.position == 2 and option1_name == selected_options[0] and variants_option2[forloop.index0] == value and variants_option3[forloop.index0] == selected_options[2]
                              assign option_disabled = false
                            elsif selected_option.position == 3 and option1_name == selected_options[0] and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == value
                              assign option_disabled = false
                            endif
                          endfor

                          assign is_in_stock = true

                          unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify'
                            assign selected_variant = ''

                            for variant in product_h6Z81zoUOw6rcwgN.variants
                              if selected_option.position == 1 and variant.option1 == value and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                                assign selected_variant = variant
                              elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == value and variant.option3 == selected_options[2]
                                assign selected_variant = variant
                              elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == value
                                assign selected_variant = variant
                              endif
                            endfor

                            if selected_variant != '' and selected_variant.available == false
                              assign is_in_stock = false
                            endif
                          endunless
                        -%}
                        <option
                          id="a615OB3JRq6zUiHW-{{ selected_option.position }}-{{ forloop.index0 }}"
                          value="{{ value | escape }}"
                          {% if selected_options[1] == value %}
                            selected
                          {% endif %}
                          {% if option_disabled or is_in_stock == false %}
                            data-instant-disabled="true"
                          {% endif %}
                        >
                          {{ value }}
                          {% if option_disabled or is_in_stock == false %}- Unavailable{% endif %}
                        </option>
                      {%- endfor -%}
                    </select>

                    <!--  -->

                    {%- for value in selected_option.values -%}
                      {% if selected_options[1] == value %}
                        <span
                          class="itn2VDKNhyzbnknG6"
                          data-instant-type="selected-variant-option"
                        >
                          {{ value }}
                        </span>
                      {% endif %}
                    {%- endfor -%}

                    <div data-instant-type="icon" class="itRuFpzGWzsyUZd6G">
                      <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                        <title>caret-down</title><path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z"></path>
                      </svg>
                    </div>
                  </fieldset>
                {%- endunless -%}
              </div>
              <div class="igAMbKkemVdwMmjfl" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text iigSHUxPgYFLLR5TG">
                  <div>{{ section.settings.text_igSHUxPgYFLLR5TG }}</div>
                </div>
                <div class="idzkrrlgCSmStGLbw" data-instant-type="container">
                  <div class="i0aE9Rc0hn6p1Uecb" data-instant-type="container">
                    <div
                      class="instant-custom-variant-picker iR4kxTzDEp9x7nV2p"
                      data-instant-type="container"
                      data-instant-action-type="select-variant-option"
                      data-instant-option-value="
                        {%- liquid
                        	assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', 'Color' | first
                        	if selected_option == null
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        	endif
                        -%}{{- selected_option.values[0] | escape -}}
                      "
                      data-instant-option-name="{{ selected_option.name | escape }}"
                      data-instant-state="{%- if product_h6Z81zoUOw6rcwgN_variant.options contains selected_option.values[0] -%}active{%- else -%}inactive{%- endif -%}"
                      data-instant-disabled="
                        {%- liquid
                        	assign is_out_of_stock = false

                        	unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify'
                        		assign selected_options = product_h6Z81zoUOw6rcwgN_variant.options
                        		assign selected_variant = ''
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                        		if selected_option == null
                        			assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        		endif

                        		for variant in product_h6Z81zoUOw6rcwgN.variants
                        			if selected_option.position == 1 and variant.option1 == selected_option.values[0] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[0] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[0]
                        				assign selected_variant = variant
                        			endif
                        		endfor

                        		if selected_variant != '' and selected_variant.available == false
                        			assign is_out_of_stock = true
                        		endif
                        	endunless
                        -%}{{- is_out_of_stock -}}
                      "
                    >
                      <div class="i9bSA1cqPBRwCdPYA" data-instant-type="container"><div class="iwTvhRlSrQCWSn2Lc" data-instant-type="container"></div></div>
                      <div data-instant-type="text" class="instant-rich-text iYsKWZJwKUl7HTWVC">
                        <div>{{ section.settings.text_YsKWZJwKUl7HTWVC }}</div>
                      </div>
                    </div>
                    <div
                      class="instant-custom-variant-picker idd9nUxJgusynOoxI"
                      data-instant-type="container"
                      data-instant-action-type="select-variant-option"
                      data-instant-option-value="
                        {%- liquid
                        	assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', 'Color' | first
                        	if selected_option == null
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        	endif
                        -%}{{- selected_option.values[1] | escape -}}
                      "
                      data-instant-option-name="{{ selected_option.name | escape }}"
                      data-instant-state="{%- if product_h6Z81zoUOw6rcwgN_variant.options contains selected_option.values[1] -%}active{%- else -%}inactive{%- endif -%}"
                      data-instant-disabled="
                        {%- liquid
                        	assign is_out_of_stock = false

                        	unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify'
                        		assign selected_options = product_h6Z81zoUOw6rcwgN_variant.options
                        		assign selected_variant = ''
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                        		if selected_option == null
                        			assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        		endif

                        		for variant in product_h6Z81zoUOw6rcwgN.variants
                        			if selected_option.position == 1 and variant.option1 == selected_option.values[1] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[1]
                        				assign selected_variant = variant
                        			endif
                        		endfor

                        		if selected_variant != '' and selected_variant.available == false
                        			assign is_out_of_stock = true
                        		endif
                        	endunless
                        -%}{{- is_out_of_stock -}}
                      "
                    >
                      <div class="i9omEHM6o5wpb2iZn" data-instant-type="container"><div class="iH9kvgG2YNCU5MBEB" data-instant-type="container"></div></div>
                      <div data-instant-type="text" class="instant-rich-text inqBQHAoGP1oOId9X">
                        <div>{{ section.settings.text_nqBQHAoGP1oOId9X }}</div>
                      </div>
                    </div>
                    <div
                      class="instant-custom-variant-picker iFjVFcUBWNyaoaUQM"
                      data-instant-type="container"
                      data-instant-action-type="select-variant-option"
                      data-instant-option-value="
                        {%- liquid
                        	assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', 'Color' | first
                        	if selected_option == null
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        	endif
                        -%}{{- selected_option.values[2] | escape -}}
                      "
                      data-instant-option-name="{{ selected_option.name | escape }}"
                      data-instant-state="{%- if product_h6Z81zoUOw6rcwgN_variant.options contains selected_option.values[2] -%}active{%- else -%}inactive{%- endif -%}"
                      data-instant-disabled="
                        {%- liquid
                        	assign is_out_of_stock = false

                        	unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify'
                        		assign selected_options = product_h6Z81zoUOw6rcwgN_variant.options
                        		assign selected_variant = ''
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                        		if selected_option == null
                        			assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        		endif

                        		for variant in product_h6Z81zoUOw6rcwgN.variants
                        			if selected_option.position == 1 and variant.option1 == selected_option.values[2] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[2] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[2]
                        				assign selected_variant = variant
                        			endif
                        		endfor

                        		if selected_variant != '' and selected_variant.available == false
                        			assign is_out_of_stock = true
                        		endif
                        	endunless
                        -%}{{- is_out_of_stock -}}
                      "
                    >
                      <div class="ippr78Yhy0QbVOJXP" data-instant-type="container"><div class="ijh2zzyXXGmN3AWpJ" data-instant-type="container"></div></div>
                      <div data-instant-type="text" class="instant-rich-text i7cABbf9lRnQlHHiC">
                        <div>{{ section.settings.text_7cABbf9lRnQlHHiC }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="ihVwKLCX8l6qcKR0g" data-instant-type="container">
                    <div
                      class="instant-custom-variant-picker io0kPJzHxLDL9XRq4"
                      data-instant-type="container"
                      data-instant-action-type="select-variant-option"
                      data-instant-option-value="
                        {%- liquid
                        	assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', 'Color' | first
                        	if selected_option == null
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        	endif
                        -%}{{- selected_option.values[3] | escape -}}
                      "
                      data-instant-option-name="{{ selected_option.name | escape }}"
                      data-instant-state="{%- if product_h6Z81zoUOw6rcwgN_variant.options contains selected_option.values[3] -%}active{%- else -%}inactive{%- endif -%}"
                      data-instant-disabled="
                        {%- liquid
                        	assign is_out_of_stock = false

                        	unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify'
                        		assign selected_options = product_h6Z81zoUOw6rcwgN_variant.options
                        		assign selected_variant = ''
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                        		if selected_option == null
                        			assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        		endif

                        		for variant in product_h6Z81zoUOw6rcwgN.variants
                        			if selected_option.position == 1 and variant.option1 == selected_option.values[3] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[3] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[3]
                        				assign selected_variant = variant
                        			endif
                        		endfor

                        		if selected_variant != '' and selected_variant.available == false
                        			assign is_out_of_stock = true
                        		endif
                        	endunless
                        -%}{{- is_out_of_stock -}}
                      "
                    >
                      <div class="iYsK3KufubIln94DE" data-instant-type="container"><div class="iZulKSsQxDELo6Gh5" data-instant-type="container"></div></div>
                      <div data-instant-type="text" class="instant-rich-text ipwQk00InLlBeIX4V">
                        <div>{{ section.settings.text_pwQk00InLlBeIX4V }}</div>
                      </div>
                    </div>
                    <div
                      class="instant-custom-variant-picker i482VkDuQYWLy4FzK"
                      data-instant-type="container"
                      data-instant-action-type="select-variant-option"
                      data-instant-option-value="
                        {%- liquid
                        	assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', 'Color' | first
                        	if selected_option == null
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        	endif
                        -%}{{- selected_option.values[4] | escape -}}
                      "
                      data-instant-option-name="{{ selected_option.name | escape }}"
                      data-instant-state="{%- if product_h6Z81zoUOw6rcwgN_variant.options contains selected_option.values[4] -%}active{%- else -%}inactive{%- endif -%}"
                      data-instant-disabled="
                        {%- liquid
                        	assign is_out_of_stock = false

                        	unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify'
                        		assign selected_options = product_h6Z81zoUOw6rcwgN_variant.options
                        		assign selected_variant = ''
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                        		if selected_option == null
                        			assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        		endif

                        		for variant in product_h6Z81zoUOw6rcwgN.variants
                        			if selected_option.position == 1 and variant.option1 == selected_option.values[4] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[4] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[4]
                        				assign selected_variant = variant
                        			endif
                        		endfor

                        		if selected_variant != '' and selected_variant.available == false
                        			assign is_out_of_stock = true
                        		endif
                        	endunless
                        -%}{{- is_out_of_stock -}}
                      "
                    >
                      <div class="iuQMuOtLiABYOnKzK" data-instant-type="container"><div class="i0PashT2Zb8wrKT0J" data-instant-type="container"></div></div>
                      <div data-instant-type="text" class="instant-rich-text iX6MMK7VLxtHMU7AE">
                        <div>{{ section.settings.text_X6MMK7VLxtHMU7AE }}</div>
                      </div>
                    </div>
                    <div
                      class="instant-custom-variant-picker iivcUf2SIuLwVXlnh"
                      data-instant-type="container"
                      data-instant-action-type="select-variant-option"
                      data-instant-option-value="
                        {%- liquid
                        	assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', 'Color' | first
                        	if selected_option == null
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        	endif
                        -%}{{- selected_option.values[5] | escape -}}
                      "
                      data-instant-option-name="{{ selected_option.name | escape }}"
                      data-instant-state="{%- if product_h6Z81zoUOw6rcwgN_variant.options contains selected_option.values[5] -%}active{%- else -%}inactive{%- endif -%}"
                      data-instant-disabled="
                        {%- liquid
                        	assign is_out_of_stock = false

                        	unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify'
                        		assign selected_options = product_h6Z81zoUOw6rcwgN_variant.options
                        		assign selected_variant = ''
                        		assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                        		if selected_option == null
                        			assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                        		endif

                        		for variant in product_h6Z81zoUOw6rcwgN.variants
                        			if selected_option.position == 1 and variant.option1 == selected_option.values[5] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[5] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[5]
                        				assign selected_variant = variant
                        			endif
                        		endfor

                        		if selected_variant != '' and selected_variant.available == false
                        			assign is_out_of_stock = true
                        		endif
                        	endunless
                        -%}{{- is_out_of_stock -}}
                      "
                    >
                      <div class="iNszuYSQdwKNIg53B" data-instant-type="container"><div class="i127n78hfArOWIuZY" data-instant-type="container"></div></div>
                      <div data-instant-type="text" class="instant-rich-text iZjqAihtaX5VxqyYn">
                        <div>{{ section.settings.text_ZjqAihtaX5VxqyYn }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              {%- liquid
                assign selected_options = product_h6Z81zoUOw6rcwgN_variant.options
                assign variants_option1 = product_h6Z81zoUOw6rcwgN.variants | map: 'option1'
                assign variants_option2 = product_h6Z81zoUOw6rcwgN.variants | map: 'option2'
                assign variants_option3 = product_h6Z81zoUOw6rcwgN.variants | map: 'option3'
                assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values | where: 'name', 'Color' | first

                if selected_option == null
                  assign selected_option = product_h6Z81zoUOw6rcwgN.options_with_values[0]
                endif

                assign hide_variant_picker = false

                if product_h6Z81zoUOw6rcwgN.has_only_default_variant or selected_option == null
                  assign hide_variant_picker = true
                endif
              -%}
              {%- unless hide_variant_picker -%}
                <fieldset class="instant-variant-picker instant-variant-picker--select iUudsok4VBRIgDH6i" data-instant-type="VARIANT_PICKER">
                  <legend class="instant-visually-hidden">{{ selected_option.name }}</legend>

                  <select name="h6Z81zoUOw6rcwgN__{{ selected_option.name | escape }}">
                    {%- for value in selected_option.values -%}
                      {%- liquid
                        assign option_disabled = true

                        for option1_name in variants_option1
                          if selected_option.position == 1 and option1_name == value and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == selected_options[2]
                            assign option_disabled = false
                          elsif selected_option.position == 2 and option1_name == selected_options[0] and variants_option2[forloop.index0] == value and variants_option3[forloop.index0] == selected_options[2]
                            assign option_disabled = false
                          elsif selected_option.position == 3 and option1_name == selected_options[0] and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == value
                            assign option_disabled = false
                          endif
                        endfor

                        assign is_in_stock = true

                        unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify'
                          assign selected_variant = ''

                          for variant in product_h6Z81zoUOw6rcwgN.variants
                            if selected_option.position == 1 and variant.option1 == value and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              assign selected_variant = variant
                            elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == value and variant.option3 == selected_options[2]
                              assign selected_variant = variant
                            elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == value
                              assign selected_variant = variant
                            endif
                          endfor

                          if selected_variant != '' and selected_variant.available == false
                            assign is_in_stock = false
                          endif
                        endunless
                      -%}
                      <option
                        id="Uudsok4VBRIgDH6i-{{ selected_option.position }}-{{ forloop.index0 }}"
                        value="{{ value | escape }}"
                        {% if selected_options[0] == value %}
                          selected
                        {% endif %}
                        {% if option_disabled or is_in_stock == false %}
                          data-instant-disabled="true"
                        {% endif %}
                      >
                        {{ value }}
                        {% comment %}
                        {% if option_disabled or is_in_stock == false %}- Unavailable{% endif %}
                        {% endcomment %}
                      
                      </option>
                    {%- endfor -%}
                  </select>

                  <!--  -->

                  {%- for value in selected_option.values -%}
                    {% if selected_options[0] == value %}
                      <span
                        class="iDFdNbgU4P3bJWnFj"
                        data-instant-type="selected-variant-option"
                      >
                        {{ value }}
                      </span>
                    {% endif %}
                  {%- endfor -%}

                  <div data-instant-type="icon" class="izgSRWzA41DSStzw7">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                      <title>caret-down</title><path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z"></path>
                    </svg>
                  </div>
                </fieldset>
              {%- endunless -%}

              <div class="iRtFQEQAOhrv4aKct" data-instant-type="container">
                <div class="i0ka0HVck6VYGzyVk" data-instant-type="container">
                  {% assign app_blocks = section.blocks | where: 'type', '@app' %}
                  {% if app_blocks.size != 0 %}
                    <div schemaId="4WwrPPxhj4qkXTFd" data-instant-type="app-island" class="i4WwrPPxhj4qkXTFd">
                      {% for block in app_blocks %}
                        {% render block %}
                      {% endfor %}
                    </div>
                  {% endif %}
                  <a
                    class="iTiWq4wGXewr1QfOp maison-atc-button"
                    data-instant-type="container"
                    href="{{ routes.cart_url }}/{{ product_h6Z81zoUOw6rcwgN_variant.id }}:1?storefront=true"
                    rel="noopener noreferrer"
                    data-instant-action-type="add-to-cart"
                    data-instant-action-id="{{ product_h6Z81zoUOw6rcwgN.id }}"
                    data-instant-action-variant-id="{{ product_h6Z81zoUOw6rcwgN_variant.id }}"
                    data-instant-disabled="
                      {%- unless product_h6Z81zoUOw6rcwgN_variant.inventory_management != 'shopify' -%}
                      	{%- if product_h6Z81zoUOw6rcwgN_variant.available == false -%}
                      		true
                      	{%-	endif -%}
                      {%- endunless -%}
                    "
                  >
                    <div data-instant-type="text" class="instant-rich-text iqgkYPpFGxnZMEKW4">
                      <div>{{ section.settings.text_qgkYPpFGxnZMEKW4 }}</div>
                    </div>

                    {%- liquid
                      assign style = ''

                      assign compare_at_price = product_h6Z81zoUOw6rcwgN_variant.compare_at_price
                      assign variant_price = product_h6Z81zoUOw6rcwgN_variant.price

                      if product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation
                        assign compare_at_price = product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation.compare_at_price
                        assign variant_price = product_h6Z81zoUOw6rcwgN_variant.selected_selling_plan_allocation.price
                      endif

                      assign saved_amount = compare_at_price | minus: variant_price | times: 100 | divided_by: compare_at_price

                      if compare_at_price <= variant_price or saved_amount <= 0 or compare_at_price == null
                        assign style = 'style="display: none;"'
                      endif
                    -%}
                    <span
                      data-instant-dynamic-content-source="SAVED_PERCENTAGE"
                      class="iVkEWqXdnHcY69oE4"
                      {{- style -}}
                    >
                      {{- saved_amount -}}
                      %
                    </span>

                    <div data-instant-type="text" class="instant-rich-text iPxriSpD1KmBJwC7Y">
                      <div>{{ section.settings.text_PxriSpD1KmBJwC7Y }}</div>
                    </div>
                    <div class="ia1OKX3VA6Ho6tg5g" data-instant-type="container">
                      {% if section.settings.image_a1OKX3VA6Ho6tg5g and section.settings.image_a1OKX3VA6Ho6tg5g != blank %}
                        {{ section.settings.image_a1OKX3VA6Ho6tg5g | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_a1OKX3VA6Ho6tg5g.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                      {% else %}
                        <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/vNZD6qx56xNG4BOB/c7ce2ae2d067b27a0bc9ca1bca80c59f67500c8d.svg" width="24" height="24" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                      {% endif %}
                    </div>
                  </a>
                </div>
                <div class="ikEAm9peTmvqhDm1I" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iuGr0Fwd1uf2sW7j8">
                    <div>{{ section.settings.text_uGr0Fwd1uf2sW7j8 }}</div>
                  </div>
                  <div class="iMrfd2CfQ4Eg0H2Ez" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iotXfKtA0D2fejjCl">
                      <div>{{ section.settings.text_otXfKtA0D2fejjCl }}</div>
                    </div>
                    <div data-instant-type="text" class="instant-rich-text ix2A8WDWyDvxu1hiP maison-country-flag">
                      <div>{{ section.settings.text_x2A8WDWyDvxu1hiP }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="irycLPC3p9CJ25fuO" data-instant-type="container">
              <div class="iJW7TlZWssl4SczlC" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text iJUqf3zEjY4VuWTTg">
                  <div>{{ section.settings.text_JUqf3zEjY4VuWTTg }}</div>
                </div>
              </div>
              <div class="izZ4EPGXnAXCbPtpV" data-instant-type="container">
                <div class="if8sIVASoEQgownor" data-instant-type="container">
                  <div data-instant-type="image" class="inenm2z4kkT9WdMd2">
                    {% if section.settings.image_nenm2z4kkT9WdMd2 and section.settings.image_nenm2z4kkT9WdMd2 != blank %}
                      {{ section.settings.image_nenm2z4kkT9WdMd2 | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_nenm2z4kkT9WdMd2.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                    {% else %}
                      <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png" width="1100" height="1100" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                    {% endif %}
                  </div>
                </div>
                <div class="iZK2z9XjPeAFxBivo" data-instant-type="container">
                  <div class="iPYOLosUSnkARaLne" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iAOk2Cn9ASpepaF9q">
                      <div>{{ section.settings.text_AOk2Cn9ASpepaF9q }}</div>
                    </div>
                  </div>
                  <div class="ipiGqnUDtquUWAzDO" data-instant-type="container">
                    <div class="iskpmJ8y6NC2opVM0" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text iB2bbwSkG2gh9Awpu">
                        <div>{{ section.settings.text_B2bbwSkG2gh9Awpu }}</div>
                      </div>
                      <div data-instant-type="text" class="instant-rich-text iY6EEmQhJ5IKIw36V">
                        <div>{{ section.settings.text_Y6EEmQhJ5IKIw36V }}</div>
                      </div>
                    </div>
                    <div class="id3zXTJqWvgdokmWW" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text iiPuyunJYCvvACB3K">
                        <div>{{ section.settings.text_iPuyunJYCvvACB3K }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="iHmBHFF1bzoDNjr1j" data-instant-type="accordion-container" data-is-first-open="true" data-is-multi-open-enabled="false">
            <div data-state="open" class="iad2swYtYTHS6vri7" data-instant-type="accordion-item">
              <button class="iBQJ6T3qb5AzmPR3V" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text iVGmySesiXoLx8DFu">
                  <div>{{ section.settings.text_VGmySesiXoLx8DFu }}</div>
                </div>
                <div data-instant-type="icon" class="ij9gwg4C3oIshpEZz">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="ihBITOvSTCS0rTotc" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div class="i1sNvWX6CNIuF6x5v" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iyKK2UAPzRyWBWIqe">
                    <div>{{ section.settings.text_yKK2UAPzRyWBWIqe }}</div>
                  </div>
                  <div class="itdYAWmUnIK1nr3ss" data-instant-type="container">
                    <div class="i8aFohq5uw4RjFmV3" data-instant-type="container">
                      <div class="i7qvax1HxkG1I0wcV" data-instant-type="container">
                        {% if section.settings.image_7qvax1HxkG1I0wcV and section.settings.image_7qvax1HxkG1I0wcV != blank %}
                          {{ section.settings.image_7qvax1HxkG1I0wcV | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_7qvax1HxkG1I0wcV.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                        {% else %}
                          <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/5djohSjHkK6vVLLT/7054654596433f74c31e0487b11e2af4a8b649bd.svg" width="49" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                        {% endif %}
                      </div>
                      <div data-instant-type="text" class="instant-rich-text icDgYosZDfcPFdQSQ">
                        <div>{{ section.settings.text_cDgYosZDfcPFdQSQ }}</div>
                      </div>
                    </div>
                    <div class="i9yNUvLK7iWL0NWcD" data-instant-type="container">
                      <div class="iEXmaycVGH2ithwai" data-instant-type="container">
                        {% if section.settings.image_EXmaycVGH2ithwai and section.settings.image_EXmaycVGH2ithwai != blank %}
                          {{ section.settings.image_EXmaycVGH2ithwai | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_EXmaycVGH2ithwai.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                        {% else %}
                          <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/9v4cki5YE7UNxsZW/a1a359f8f8f14b30a77fa6e5dbdd588563901fa6.svg" width="48" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                        {% endif %}
                      </div>
                      <div data-instant-type="text" class="instant-rich-text iCbk6mNhZygABbhGS">
                        <div>{{ section.settings.text_Cbk6mNhZygABbhGS }}</div>
                      </div>
                    </div>
                    <div class="iFnTVzp7WBjGxmjsy" data-instant-type="container">
                      <div class="iCvk6kGSQorg02w6h" data-instant-type="container">
                        {% if section.settings.image_Cvk6kGSQorg02w6h and section.settings.image_Cvk6kGSQorg02w6h != blank %}
                          {{ section.settings.image_Cvk6kGSQorg02w6h | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_Cvk6kGSQorg02w6h.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                        {% else %}
                          <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/MHvi0r133dJa69XB/ea9b201c4c9f15d7613d00bc9a6778094105b8d8.svg" width="49" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                        {% endif %}
                      </div>
                      <div data-instant-type="text" class="instant-rich-text icpzpm1p9piRkzDMz">
                        <div>{{ section.settings.text_cpzpm1p9piRkzDMz }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div data-state="closed" class="iOp2bvAYjtYSc6I2E" data-instant-type="accordion-item">
              <button class="ikyUIC89oQ1dwewyj" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text ijduGwSRfZLQ3nRBd">
                  <div>{{ section.settings.text_jduGwSRfZLQ3nRBd }}</div>
                </div>
                <div data-instant-type="icon" class="iBqK1bqU8BuGzeikP">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="ibvIXdEGyATmOTxRS" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div class="irKfc9ZpVmuuNgPXC" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text i7suvqR65V07t2nZW">
                    <div>{{ section.settings.text_7suvqR65V07t2nZW }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div data-state="closed" class="iGYPoHmB5NLfaHtr5" data-instant-type="accordion-item">
              <button class="iuLJOotGJtADvnZEj" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text ilFK8TwAIqtKNiZt2">
                  <div>{{ section.settings.text_lFK8TwAIqtKNiZt2 }}</div>
                </div>
                <div data-instant-type="icon" class="i9oZI3bK4M9KbA6JU">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="iW7CgDUyfLcFTW7Ye" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div class="i8cOm2DMBm52yld0s" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iVnz9byZNeaguWeLR">
                    <div>{{ section.settings.text_Vnz9byZNeaguWeLR }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <script type="application/json" id="variants__h6Z81zoUOw6rcwgN--{{ section.id }}">
        {{ product_h6Z81zoUOw6rcwgN.variants | json }}
      </script>
      <script type="application/json" id="options__h6Z81zoUOw6rcwgN--{{ section.id }}">
        {{ product_h6Z81zoUOw6rcwgN.options_with_values | json }}
      </script>
    </form>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.4",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison Product Section (D",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "product",
      "id": "product_h6Z81zoUOw6rcwgN",
      "label": "Product Header / 5 /"
    },
    {
      "type": "richtext",
      "id": "text_RDTYbz6Eg9cbH0A0",
      "label": "Text",
      "default": "<p>Perfect for: ‘Everyday, Walking, Travel, Workouts’</p>"
    },
    {
      "type": "image_picker",
      "id": "image_aSOfTMNjcTDL3Y9F",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_FiGqbvTJhsznicWe",
      "label": "Review text mobile",
      "default": "<p>6,627 Reviews</p>"
    },
    {
      "type": "url",
      "id": "url_FiGqbvTJhsznicWe",
      "label": "Review text mobile URL"
    },
    {
      "type": "image_picker",
      "id": "image_I0o9KvEq6A0p3vss",
      "label": "Smiley-Smile--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_zOgTgb8mIuJacIjm",
      "label": "Text",
      "default": "<p>Supports your feet</p>"
    },
    {
      "type": "image_picker",
      "id": "image_RQVStSj0ZTecingV",
      "label": "Drugs-Cannabis--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_8UFC00MBjjjRMnQN",
      "label": "Text",
      "default": "<p>All day comfortable</p>"
    },
    {
      "type": "image_picker",
      "id": "image_CxbL5uRaauOBuz2v",
      "label": "Cash-Shield--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_kBlDbgQguQ2hP5AP",
      "label": "Text",
      "default": "<p>60 Day Money Back</p>"
    },
    {
      "type": "richtext",
      "id": "text_wap7Ppej6isFxD9b",
      "label": "Text",
      "default": "<p>Perfect for: ‘Everyday, Walking, Travel, Workouts’</p>"
    },
    {
      "type": "richtext",
      "id": "text_9LfGGpIUjtw7KDDf",
      "label": "YOU SAVE 70%",
      "default": "<p>YOU SAVE</p>"
    },
    {
      "type": "image_picker",
      "id": "image_IdldJRJEcRvGxLtb",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_fFdMhD0z9cpg9QBq",
      "label": "Reviews text",
      "default": "<p>6,627 Reviews</p>"
    },
    {
      "type": "url",
      "id": "url_fFdMhD0z9cpg9QBq",
      "label": "Reviews text URL"
    },
    {
      "type": "richtext",
      "id": "text_sVuL7ceKtKhBp20M",
      "label": "Text",
      "default": "<ul><li><p>Relieves pressure on feet and joints</p></li><li><p>Developed with orthopedists</p></li><li><p>Free US Shipping</p></li></ul>"
    },
    {
      "type": "text",
      "id": "tab_title_JJlGPsB7ONy2xGVL",
      "label": "Women Size",
      "default": "Women Size"
    },
    {
      "type": "richtext",
      "id": "text_z5UWlPOKyrYr6HdK",
      "label": "Variant",
      "default": "<p>Choose Your Size</p>"
    },
    {
      "type": "richtext",
      "id": "text_3AtR6YdVq4PBc7sr",
      "label": "Variant",
      "default": "<p>SIZE CHART (WOMEN)</p>"
    },
    {
      "type": "image_picker",
      "id": "image_EEB2A4FhO5aw0N0k",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "tab_title_hMptVU9jttzOycIH",
      "label": "Men Size",
      "default": "Men Size"
    },
    {
      "type": "richtext",
      "id": "text_TfllBdbV14hd4OYY",
      "label": "Variant",
      "default": "<p>Choose Your Size</p>"
    },
    {
      "type": "richtext",
      "id": "text_uGcOREhQxlfwPM6l",
      "label": "Variant",
      "default": "<p>SIZE CHART (MEN)</p>"
    },
    {
      "type": "image_picker",
      "id": "image_A8PW0Z8nBVvGWf3Q",
      "label": "Image"
    },
    {
      "type": "richtext",
      "id": "text_igTpZ9lKnigyJQX4",
      "label": "Variant",
      "default": "<p>Choose Your Size</p>"
    },
    {
      "type": "richtext",
      "id": "text_EhkJ7wn1GgNg8NB5",
      "label": "Variant",
      "default": "<p>CLICK FOR SIZE CHART</p>"
    },
    {
      "type": "image_picker",
      "id": "image_MsxM9t9yGSAJwlWs",
      "label": "Image"
    },
    {
      "type": "richtext",
      "id": "text_igSHUxPgYFLLR5TG",
      "label": "Variant",
      "default": "<p>Choose Your Color</p>"
    },
    {
      "type": "richtext",
      "id": "text_YsKWZJwKUl7HTWVC",
      "label": "Button",
      "default": "<p>BROWN</p>"
    },
    {
      "type": "richtext",
      "id": "text_nqBQHAoGP1oOId9X",
      "label": "Button",
      "default": "<p>Blue</p>"
    },
    {
      "type": "richtext",
      "id": "text_7cABbf9lRnQlHHiC",
      "label": "Button",
      "default": "<p>GREEN</p>"
    },
    {
      "type": "richtext",
      "id": "text_pwQk00InLlBeIX4V",
      "label": "Button",
      "default": "<p>BLACK</p>"
    },
    {
      "type": "richtext",
      "id": "text_X6MMK7VLxtHMU7AE",
      "label": "Button",
      "default": "<p>PURPLE</p>"
    },
    {
      "type": "richtext",
      "id": "text_ZjqAihtaX5VxqyYn",
      "label": "Button",
      "default": "<p>White</p>"
    },
    {
      "type": "richtext",
      "id": "text_qgkYPpFGxnZMEKW4",
      "label": "Button",
      "default": "<p>Add To Cart - </p>"
    },
    {
      "type": "richtext",
      "id": "text_PxriSpD1KmBJwC7Y",
      "label": "Button",
      "default": "<p>Off</p>"
    },
    {
      "type": "image_picker",
      "id": "image_a1OKX3VA6Ho6tg5g",
      "label": "Icon / cart-add"
    },
    {
      "type": "richtext",
      "id": "text_uGr0Fwd1uf2sW7j8",
      "label": "Free Shipping on Your Order!",
      "default": "<p>Free Shipping on Your Order! </p>"
    },
    {
      "type": "richtext",
      "id": "text_otXfKtA0D2fejjCl",
      "label": "Get a refund in one click.",
      "default": "<p>Shipping to</p>"
    },
    {
      "type": "richtext",
      "id": "text_x2A8WDWyDvxu1hiP",
      "label": "Get a refund in one click.",
      "default": "<p></p>"
    },
    {
      "type": "richtext",
      "id": "text_JUqf3zEjY4VuWTTg",
      "label": "FREE WITH YOUR ORDER",
      "default": "<p>FREE WITH TODAY’S ORDER:</p>"
    },
    {
      "type": "image_picker",
      "id": "image_nenm2z4kkT9WdMd2",
      "label": "Ebook Image"
    },
    {
      "type": "richtext",
      "id": "text_AOk2Cn9ASpepaF9q",
      "label": "Your order comes with 2 E-Books, Barefoot Guide, Habit Tracker & Commu",
      "default": "<p>Your order comes with 2 E-Books, Barefoot Guide, Habit Tracker &amp; Community Access</p>"
    },
    {
      "type": "richtext",
      "id": "text_B2bbwSkG2gh9Awpu",
      "label": "Text",
      "default": "<p>Today:</p>"
    },
    {
      "type": "richtext",
      "id": "text_Y6EEmQhJ5IKIw36V",
      "label": "Text",
      "default": "<p>FREE!</p>"
    },
    {
      "type": "richtext",
      "id": "text_iPuyunJYCvvACB3K",
      "label": "Text",
      "default": "<p>WORTH: $99</p>"
    },
    {
      "type": "richtext",
      "id": "text_VGmySesiXoLx8DFu",
      "label": "Heading",
      "default": "<p>Why choose HK Cloud for happier feet?</p>"
    },
    {
      "type": "richtext",
      "id": "text_yKK2UAPzRyWBWIqe",
      "label": "Text",
      "default": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_7qvax1HxkG1I0wcV",
      "label": "Stairs-Person-Ascend-1--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_cDgYosZDfcPFdQSQ",
      "label": "Text",
      "default": "<p>Cloud-Like Comfort</p>"
    },
    {
      "type": "image_picker",
      "id": "image_EXmaycVGH2ithwai",
      "label": "Smiley-Smile--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_Cbk6mNhZygABbhGS",
      "label": "Text",
      "default": "<p>All Day Comfort</p>"
    },
    {
      "type": "image_picker",
      "id": "image_Cvk6kGSQorg02w6h",
      "label": "Cash-Shield--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_cpzpm1p9piRkzDMz",
      "label": "Text",
      "default": "<p>60 Day Money Back </p>"
    },
    {
      "type": "richtext",
      "id": "text_jduGwSRfZLQ3nRBd",
      "label": "Heading",
      "default": "<p>FREE FAST WORLDWIDE Shipping</p>"
    },
    {
      "type": "richtext",
      "id": "text_7suvqR65V07t2nZW",
      "label": "Text",
      "default": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>"
    },
    {
      "type": "richtext",
      "id": "text_lFK8TwAIqtKNiZt2",
      "label": "Heading",
      "default": "<p>EASY 30 DAY Returns</p>"
    },
    {
      "type": "richtext",
      "id": "text_Vnz9byZNeaguWeLR",
      "label": "Text",
      "default": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison Product Section (D",
      "settings": {
        "product_h6Z81zoUOw6rcwgN": "hf-cloud",
        "text_RDTYbz6Eg9cbH0A0": "<p>Perfect for: ‘Everyday, Walking, Travel, Workouts’</p>",
        "text_FiGqbvTJhsznicWe": "<p>6,627 Reviews</p>",
        "url_FiGqbvTJhsznicWe": "#shopify-section-template--23938854453588__instant_rse_fowu_c4_a_cffmqw_K3Fh8e",
        "text_zOgTgb8mIuJacIjm": "<p>Supports your feet</p>",
        "text_8UFC00MBjjjRMnQN": "<p>All day comfortable</p>",
        "text_kBlDbgQguQ2hP5AP": "<p>60 Day Money Back</p>",
        "text_wap7Ppej6isFxD9b": "<p>Perfect for: ‘Everyday, Walking, Travel, Workouts’</p>",
        "text_9LfGGpIUjtw7KDDf": "<p>YOU SAVE</p>",
        "text_fFdMhD0z9cpg9QBq": "<p>6,627 Reviews</p>",
        "text_sVuL7ceKtKhBp20M": "<ul><li><p>Relieves pressure on feet and joints</p></li><li><p>Developed with orthopedists</p></li><li><p>Free US Shipping</p></li></ul>",
        "text_z5UWlPOKyrYr6HdK": "<p>Choose Your Size</p>",
        "text_3AtR6YdVq4PBc7sr": "<p>SIZE CHART (WOMEN)</p>",
        "text_TfllBdbV14hd4OYY": "<p>Choose Your Size</p>",
        "text_uGcOREhQxlfwPM6l": "<p>SIZE CHART (MEN)</p>",
        "text_igTpZ9lKnigyJQX4": "<p>Choose Your Size</p>",
        "text_EhkJ7wn1GgNg8NB5": "<p>CLICK FOR SIZE CHART</p>",
        "text_igSHUxPgYFLLR5TG": "<p>Choose Your Color</p>",
        "text_YsKWZJwKUl7HTWVC": "<p>BROWN</p>",
        "text_nqBQHAoGP1oOId9X": "<p>Blue</p>",
        "text_7cABbf9lRnQlHHiC": "<p>GREEN</p>",
        "text_pwQk00InLlBeIX4V": "<p>BLACK</p>",
        "text_X6MMK7VLxtHMU7AE": "<p>PURPLE</p>",
        "text_ZjqAihtaX5VxqyYn": "<p>White</p>",
        "text_qgkYPpFGxnZMEKW4": "<p>Add To Cart - </p>",
        "text_PxriSpD1KmBJwC7Y": "<p>Off</p>",
        "text_uGr0Fwd1uf2sW7j8": "<p>Free Shipping on Your Order! </p>",
        "text_otXfKtA0D2fejjCl": "<p>Shipping to</p>",
        "text_x2A8WDWyDvxu1hiP": "<p></p>",
        "text_JUqf3zEjY4VuWTTg": "<p>FREE WITH TODAY’S ORDER:</p>",
        "text_AOk2Cn9ASpepaF9q": "<p>Your order comes with 2 E-Books, Barefoot Guide, Habit Tracker &amp; Community Access</p>",
        "text_B2bbwSkG2gh9Awpu": "<p>Today:</p>",
        "text_Y6EEmQhJ5IKIw36V": "<p>FREE!</p>",
        "text_iPuyunJYCvvACB3K": "<p>WORTH: $99</p>",
        "text_VGmySesiXoLx8DFu": "<p>Why choose HK Cloud for happier feet?</p>",
        "text_yKK2UAPzRyWBWIqe": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>",
        "text_cDgYosZDfcPFdQSQ": "<p>Cloud-Like Comfort</p>",
        "text_Cbk6mNhZygABbhGS": "<p>All Day Comfort</p>",
        "text_cpzpm1p9piRkzDMz": "<p>60 Day Money Back </p>",
        "text_jduGwSRfZLQ3nRBd": "<p>FREE FAST WORLDWIDE Shipping</p>",
        "text_7suvqR65V07t2nZW": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>",
        "text_lFK8TwAIqtKNiZt2": "<p>EASY 30 DAY Returns</p>",
        "text_Vnz9byZNeaguWeLR": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>"
      }
    }
  ],
  "blocks": [{ "type": "@app" }]
}
{% endschema %}
