{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant ihpsWtyt2aYIFeimx" data-instant-id="hpsWtyt2aYIFeimx" data-instant-version="3.0.3" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-hpsWtyt2aYIFeimx.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="iycjOwlGaJYvC9Nrx">
    <div class="ijoPggRzU5Wjo9p7E" data-instant-type="container" id="ijoPggRzU5Wjo9p7E">
      <div class="iknzM4HHejyiipFac" data-instant-type="container">
        <div class="iO4kMaIoapZefJfkH" data-instant-type="container">
          <div class="iBQSXnku1Qd9mFgqh" data-instant-type="container">
            <div class="ilK2sBcACIlcgnKPG" data-instant-type="container">
              <div data-instant-type="text" class="instant-rich-text iWXlXXV3Wo7MK5fUe">
                <div>{{ section.settings.text_WXlXXV3Wo7MK5fUe }}</div>
              </div>
            </div>
            <div class="iAtuSjd3GL5rmtNwa" data-instant-type="container">
              <div class="iI8sxcfyUBNItFxGz" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text iRbGagytHnfvCrdfx">
                  <div>{{ section.settings.text_RbGagytHnfvCrdfx }}</div>
                </div>
                <div data-instant-type="text" class="instant-rich-text ibJhuxNWcDbDmcM80">
                  <div>{{ section.settings.text_bJhuxNWcDbDmcM80 }}</div>
                </div>
              </div>
<!-- text image -->

{% if section.settings.feature_img %}              
<div class="feature-img" style="max-width:800px">
    <img class="" src="{{ section.settings.feature_img | img_url: '1500x1500' }}" alt="image" />
  
</div>
  {% endif %}


              
              <div data-instant-type="text" class="instant-rich-text iEI09hnCchRsmwF83">
                <div>{{ section.settings.text_EI09hnCchRsmwF83 }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="ig4z90RCpzeEbwxzp" data-instant-type="container">


<div class="iZIs8JlC6gafGEe0k" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text i3oCCviHXrACf7Dps">
              <div>{{ section.settings.text_1 }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text i6KLj4wGFuJqUAYy0">
              <div>{{ section.settings.text_2 }}</div>
            </div>
          </div>
          
          
          <div class="iZIs8JlC6gafGEe0k" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text i3oCCviHXrACf7Dps">
              <div>{{ section.settings.text_3oCCviHXrACf7Dps }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text i6KLj4wGFuJqUAYy0">
              <div>{{ section.settings.text_6KLj4wGFuJqUAYy0 }}</div>
            </div>
          </div>
          <div class="iYlll34zEVpRcfGzj" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text iUXRGwAn0e56Wa66z">
              <div>{{ section.settings.text_UXRGwAn0e56Wa66z }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text iROHgPeeOZSDunn1Y">
              <div>{{ section.settings.text_ROHgPeeOZSDunn1Y }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text iWVPDfqrOP7A8esGt">
              <div>{{ section.settings.text_WVPDfqrOP7A8esGt }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- prettier-ignore -->
 <!-- <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.3",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script> -->
</div>
{% schema %}
{
  "name": "Maison Text With Benefits",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "richtext",
      "id": "text_WXlXXV3Wo7MK5fUe",
      "label": "Tagline",
      "default": "<p>WE LISTEN TO YOU</p>"
    },
    {
      "type": "richtext",
      "id": "text_RbGagytHnfvCrdfx",
      "label": "Heading",
      "default": "<p>We make our shoes better</p>"
    },
    {
      "type": "image_picker",
      "id": "feature_img",
      "label": "Feature Img"
    },
    {
      "type": "richtext",
      "id": "text_bJhuxNWcDbDmcM80",
      "label": "Heading",
      "default": "<p>based on your feedback.</p>"
    },
    {
      "type": "richtext",
      "id": "text_EI09hnCchRsmwF83",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat.</p>"
    },
    {
      "type": "richtext",
      "id": "text_1",
      "label": "Text",
    },
    {
      "type": "richtext",
      "id": "text_2",
      "label": "Text",
    },
    {
      "type": "richtext",
      "id": "text_3oCCviHXrACf7Dps",
      "label": "Text",
      "default": "<p>67%</p>"
    },
    {
      "type": "richtext",
      "id": "text_6KLj4wGFuJqUAYy0",
      "label": "Text",
      "default": "<p>Short heading goes here</p>"
    },
    {
      "type": "richtext",
      "id": "text_UXRGwAn0e56Wa66z",
      "label": "Text",
      "default": "<p>88%</p>"
    },
    {
      "type": "richtext",
      "id": "text_ROHgPeeOZSDunn1Y",
      "label": "Text",
      "default": "<p>Rated us 4 or 5 stars for comfort &amp; customer support</p>"
    },
    {
      "type": "richtext",
      "id": "text_WVPDfqrOP7A8esGt",
      "label": "Text",
      "default": "<p>Based on 1082+ reviews in a 14 day survey with our users</p>"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison Text With Benefits",
      "settings": {
        "text_WXlXXV3Wo7MK5fUe": "<p>WE LISTEN TO YOU</p>",
        "text_RbGagytHnfvCrdfx": "<p>We make our shoes better</p>",
        "text_bJhuxNWcDbDmcM80": "<p>based on your feedback.</p>",
        "text_EI09hnCchRsmwF83": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique. Duis cursus, mi quis viverra ornare, eros dolor interdum nulla, ut commodo diam libero vitae erat.</p>",
        "text_3oCCviHXrACf7Dps": "<p>67%</p>",
        "text_6KLj4wGFuJqUAYy0": "<p>Short heading goes here</p>",
        "text_UXRGwAn0e56Wa66z": "<p>88%</p>",
        "text_ROHgPeeOZSDunn1Y": "<p>Rated us 4 or 5 stars for comfort &amp; customer support</p>",
        "text_WVPDfqrOP7A8esGt": "<p>Based on 1082+ reviews in a 14 day survey with our users</p>"
      }
    }
  ]
}
{% endschema %}


<style>
  .instant-rich-text.iRbGagytHnfvCrdfx {
  color: black !important;
}
</style>