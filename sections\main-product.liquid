{%- render 'section-spacing-collapsing' -%}

<style>
  #shopify-section-{{ section.id }} {
    --product-grid: auto / minmax(0, 1fr);
    --product-gallery-media-list-grid: auto / auto-flow {% if section.settings.mobile_carousel_control == 'free_scroll' %}{% if section.settings.mobile_media_size == 'expanded' %}84vw{% else %}73vw{% endif %}{% else %}100%{% endif %};
    --product-gallery-media-list-gap: {% if section.settings.mobile_media_size == 'expanded' %}var(--spacing-0-5){% else %}var(--grid-gutter){% endif %};
  }

  @media screen and (max-width: 999px) {
    #shopify-section-{{ section.id }} {
      --section-spacing-block-start: {% if section.settings.mobile_media_size == 'expanded' %}0px{% else %}var(--container-gutter){% endif %};
    }
  }

  @media screen and (min-width: 1000px) {
    #shopify-section-{{ section.id }} {
      {%- assign media_ratio = section.settings.desktop_media_width | divided_by: 50.0 -%}
      --product-grid: auto / minmax(0, {{ media_ratio }}fr) minmax(0, {{ 2.0 | minus: media_ratio }}fr);
      --product-gallery-media-list-grid: {% if section.settings.desktop_media_layout contains 'grid' %}auto-flow dense / repeat(2, minmax(0, 1fr)){% else %}auto / auto-flow 100%{% endif %};
      --product-gallery-media-list-gap: calc(var(--grid-gutter) / 2);
    }

    {%- if section.settings.desktop_media_layout == 'grid_highlight' -%}
      #shopify-section-{{ section.id }} .product-gallery__media-list > :not([hidden]) {
        grid-column: span 2;
      }

      #shopify-section-{{ section.id }} .product-gallery__media-list > :not([hidden]) ~ *:not(.product-gallery__media--expand) {
        grid-column: span 1;
      }
    {%- endif -%}
  }

  @media screen and (min-width: 1400px) {
    #shopify-section-{{ section.id }} {
      --product-gallery-media-list-gap: var(--grid-gutter);
    }
  }
</style>

<div {% render 'section-properties', tight: true %}>
  <div class="product">
    {%- if product.media.size > 0 -%}
      {%- render 'product-gallery', product: product -%}
    {%- endif -%}

    {%- render 'product-info', product: product, update_url: true -%}
  </div>
</div>

{%- assign buy_buttons_block = section.blocks | where: 'type', 'buy_buttons' | first -%}

{%- if section.settings.show_fixed_add_to_cart and product.available and buy_buttons_block != blank -%}
  {%- capture product_form_id -%}product-form-{{ product.id }}-{{ section.id }}{%- endcapture -%}

  <product-quick-add form="{{ product_form_id }}" class="product-quick-add">
    {%- assign button_label = 'product.general.add_to_cart_button' | t -%}
    {%- assign image = product.selected_or_first_available_variant.featured_media | default: product.featured_media -%}

    {%- comment -%}Mobile version with image and variant info{%- endcomment -%}
    <div class="product-quick-add__variant-mobile {% if image == blank %}no-image{% endif %} sm:hidden">
      {%- comment -%}Left div: Image + Product Info{%- endcomment -%}
      <div class="product-quick-add__variant-mobile-left">
        {%- if image != blank -%}
          <div class="product-quick-add__variant-mobile-image">
            <variant-media widths="50,100" form="{{ product_form_id }}">
              {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '50px', widths: '50,100', class: 'rounded-xs' -}}
            </variant-media>
          </div>
        {%- endif -%}

        <div class="product-quick-add__variant-mobile-info">
          <div class="v-stack gap-0.5">
            {%- assign vendor_block = section.blocks | where: 'type', 'vendor' -%}

            {%- if vendor_block != blank -%}
              {%- render 'vendor' with product.vendor, class: 'text-xs' -%}
            {%- endif -%}

            <a href="{{ product.url }}" class="bold truncate-text">{{ product.title }}</a>
            {%- render 'price-list', product: product, variant: product.selected_or_first_available_variant, form_id: product_form_id -%}
          </div>
        </div>
      </div>

      {%- comment -%}Right div: Add to Cart Button{%- endcomment -%}
      <div class="product-quick-add__variant-mobile-right">
        <buy-buttons template="{{ product.template_suffix }}" form="{{ product_form_id }}" force-secondary-button class="product-quick-add__variant-mobile-button">
          {%- render 'button', type: 'submit', content: button_label, form: product_form_id, background: buy_buttons_block.settings.atc_button_background, text_color: buy_buttons_block.settings.atc_button_text_color, secondary: true, size: 'sm' -%}
        </buy-buttons>
      </div>
    </div>

    {%- comment -%}Desktop version (existing){%- endcomment -%}
    <div class="product-quick-add__variant {% if image == blank %}no-image{% endif %} hidden sm:grid">
      {%- if image != blank -%}
        <variant-media widths="80,160" form="{{ product_form_id }}">
          {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '80px', widths: '80,160', class: 'rounded-xs' -}}
        </variant-media>
      {%- endif -%}

      <div class="v-stack gap-0.5">
        {%- assign vendor_block = section.blocks | where: 'type', 'vendor' -%}

        {%- if vendor_block != blank -%}
          {%- render 'vendor' with product.vendor, class: 'text-xs' -%}
        {%- endif -%}

        <a href="{{ product.url }}" class="bold truncate-text">{{ product.title }}</a>
        {%- render 'price-list', product: product, variant: product.selected_or_first_available_variant, form_id: product_form_id -%}
      </div>

      <buy-buttons template="{{ product.template_suffix }}" form="{{ product_form_id }}" force-secondary-button>
        {%- render 'button', type: 'submit', content: button_label, form: product_form_id, background: buy_buttons_block.settings.atc_button_background, text_color: buy_buttons_block.settings.atc_button_text_color, secondary: true -%}
      </buy-buttons>
    </div>
  </product-quick-add>
{%- endif -%}

{%- comment -%}
IMPLEMENTATION NOTE: Shopify does not currently allows to render a given section within the context of a given product. However,
when rendering the quick buy popovers, we want to be able to re-use the merchant's choices (such as the selector type). This
is however only possible by rendering the whole product page, and extracting the relevant part. Here, we therefore render the
quick buy information in a template, that will be extracted in JS, but ensure it is not visible in the main product page
{%- endcomment -%}
<template id="quick-buy-content">
  {%- capture product_form_id -%}quick-buy-form-{{ product.id }}-{{ section.id }}{%- endcapture -%}

  <div class="quick-buy-drawer__variant text-start h-stack gap-6" slot="header">
    {%- assign image = product.selected_or_first_available_variant.featured_media | default: product.featured_media -%}

    {%- if image != blank -%}
      <variant-media widths="80,160" form="{{ product_form_id }}">
        {{- image | image_url: width: image.width | image_tag: loading: 'lazy', sizes: '80px', widths: '80,160', class: 'quick-buy-drawer__media rounded-xs' -}}
      </variant-media>
    {%- endif -%}

    <div class="v-stack gap-0.5">
      <a href="{{ product.url }}" class="bold justify-self-start">{{ product.title }}</a>
      {%- render 'price-list', product: product, variant: product.selected_or_first_available_variant, form_id: product_form_id -%}
    </div>
  </div>

  <div class="quick-buy-drawer__info">
    {%- comment -%}We only show a limited set of information in the quick buy{%- endcomment -%}

    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when '@app' -%}
          {%- render block -%}

        {%- when 'variant_picker' -%}
          {%- render 'variant-picker', product: product, form_id: product_form_id, update_url: false, hide_sold_out_variants: block.settings.hide_sold_out_variants, hide_size_chart: true, force_dropdown_as_block: true, block: block -%}

        {%- when 'buy_buttons' -%}
          {%- render 'buy-buttons', product: product, form_id: product_form_id, show_payment_button: block.settings.show_payment_button, button_size: 'lg' -%}
      {%- endcase -%}
    {%- endfor -%}
  </div>
</template>
<script>
document.addEventListener('DOMContentLoaded', function () {
  // Function to update the URL based on selected variant
  const updateVariant = () => {
    const countryFlag = document.querySelector('.country-selector');
    let variantId = '';
    let baseUrl = window.location.href.split('?')[0]; // Get the base URL (excluding query parameters)

    // Check if the current URL already has a 'variant' query parameter
    const urlParams = new URLSearchParams(window.location.search);
    const currentVariantId = urlParams.get('variant');

    if (currentVariantId) {
      console.log("Variant ID already in URL:", currentVariantId); // Log if the variant is already in the URL
      return; // Exit if variant is already set in the URL to prevent the loop
    }

    console.log("Base URL:", baseUrl); // Log the base URL for debugging

    // Ensure the country flag and ShopifyAnalytics meta.product exist
    if (countryFlag && window.ShopifyAnalytics && window.ShopifyAnalytics.meta && window.ShopifyAnalytics.meta.product) {
      const meta = window.ShopifyAnalytics.meta.product;
      console.log("Product data:", meta); // Log the product data for debugging

      // Dynamically find the variant ID based on the country flag
      meta.variants.forEach(variant => {
        if (
          (countryFlag.classList.contains('country-flags--ES') && variant.public_title.includes('EU')) ||
          (countryFlag.classList.contains('country-flags--FR') && variant.public_title.includes('EU')) ||
          (countryFlag.classList.contains('country-flags--DE') && variant.public_title.includes('EU')) ||
          (countryFlag.classList.contains('country-flags--AU') && variant.public_title.includes('AUS')) ||
          (countryFlag.classList.contains('country-flags--GB') && variant.public_title.includes('UK'))
        ) {
          variantId = variant.id; // Match the variant ID dynamically
          console.log("Selected Variant ID:", variantId); // Log the selected variant ID
        }
      });
    } else {
      console.log("Required elements or product data not found.");
    }

    // If a valid variant is selected, update the URL and redirect
    if (variantId) {
      const updatedUrl = `${baseUrl}?variant=${variantId}`;
      console.log("Updated URL:", updatedUrl); // Log the updated URL
      window.location.href = updatedUrl; // Redirect the user to the updated URL
    }
  };

  // Initially check and update variant when the page loads
  updateVariant();
});

</script>
{% schema %}
{
  "name": "Product page",
  "class": "shopify-section--main-product",
  "tag": "section",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "vendor",
      "name": "Vendor",
      "limit": 1
    },
    {
      "type": "title",
      "name": "Title",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Style",
          "options": [
            {
              "value": "h1",
              "label": "X-Large"
            },
            {
              "value": "h2",
              "label": "Large"
            },
            {
              "value": "h3",
              "label": "Medium"
            },
            {
              "value": "h4",
              "label": "Small"
            },
            {
              "value": "h5",
              "label": "X-Small"
            },
            {
              "value": "h6",
              "label": "XX-Small"
            }
          ],
          "default": "h2"
        }
      ]
    },
    {
      "type": "title_custom",
      "name": "Product Title",
      "limit": 1,
      "settings": [
        {
          "type": "color",
          "id": "primary_color",
          "label": "Primary color",
          "default": "#111111"
        },
        {
          "type": "color",
          "id": "secondary_color",
          "label": "Secondary color",
          "default": "#00a341"
        },
        {
          "type": "checkbox",
          "id": "swap_colors",
          "label": "Swap colors (use secondary for main)",
          "default": false
        },
        {
          "type": "text",
          "id": "accent_text",
          "label": "Accent text (optional)"
        }
      ]
    },
    {
      "type": "sku",
      "name": "SKU",
      "limit": 1
    },
    {
      "type": "badges",
      "name": "Badges",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Use metafields to add custom badges. [Learn more](https://support.maestrooo.com/article/75-collection-displaying-custom-label)"
        }
      ]
    },
    {
      "type": "price",
      "name": "Price",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_taxes_notice",
          "label": "Show taxes notice",
          "default": false
        },
        {
          "type": "color",
          "id": "price_color",
          "label": "Price color",
          "default": "#111111"
        },
        {
          "type": "color",
          "id": "compare_color",
          "label": "Compare-at color",
          "default": "#6b7280"
        },
        {
          "type": "color",
          "id": "sale_badge_bg",
          "label": "Sale badge background",
          "default": "#e7f29b"
        },
        {
          "type": "color",
          "id": "sale_badge_text",
          "label": "Sale badge text color",
          "default": "#2e6a3d"
        },
        {
          "type": "select",
          "id": "sale_weight",
          "label": "Sale/current price weight",
          "options": [
            { "value": "normal", "label": "Normal" },
            { "value": "bold", "label": "Bold" }
          ],
          "default": "bold"
        },
        {
          "type": "select",
          "id": "compare_weight",
          "label": "Compare-at price weight",
          "options": [
            { "value": "normal", "label": "Normal" },
            { "value": "bold", "label": "Bold" }
          ],
          "default": "normal"
        }
      ]
    },
    {
      "type": "payment_terms",
      "name": "Payment installments",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "To display payment installments, your store needs to support Shop Pay Installments. [Learn more](https://help.shopify.com/en/manual/payments/shop-pay-installments)"
        }
      ]
    },
    {
      "type": "rating",
      "name": "Rating",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "To display a rating, add a product rating app. [Learn more](https://apps.shopify.com/categories/store-design-social-proof-product-reviews)"
        },
        {
          "type": "checkbox",
          "id": "show_empty",
          "label": "Show if no reviews",
          "default": false
        }
      ]
    },
    {
      "type": "separator",
      "name": "Separator"
    },
    {
      "type": "description",
      "name": "Description",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "collapse_content",
          "label": "Collapse content",
          "default": false
        }
      ]
    },
    {
      "type": "variant_picker",
      "name": "Variant picker",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "hide_sold_out_variants",
          "label": "Hide sold out variants",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "stack_blocks",
          "label": "Stack options on mobile",
          "default": true
        },
        {
          "type": "select",
          "id": "selector_style",
          "label": "Selector style",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "dropdown",
              "label": "Dropdown"
            }
          ],
          "default": "block"
        },
        {
          "type": "select",
          "id": "swatch_selector_style",
          "label": "Swatch selector style",
          "info": "Enable [swatches](https://help.shopify.com/en/manual/online-store/themes/theme-structure/theme-settings#options-with-swatches) on product options.",
          "options": [
            {
              "value": "swatch",
              "label": "Swatch"
            },
            {
              "value": "block_swatch",
              "label": "Block with swatch"
            },
            {
              "value": "none",
              "label": "None"
            }
          ],
          "default": "swatch"
        },
        {
          "type": "text",
          "id": "variant_image_options",
          "label": "Show variant image for options",
          "info": "List of comma separated option names where option values show the attached variant image. [Learn more](https://impact-theme.helpscoutdocs.com/article/555-variant-images-for-color-option)"
        },
        {
          "type": "page",
          "id": "size_chart_page",
          "label": "Size chart page",
          "info": "Feature a page for size option"
        }
      ]
    },
    {
      "type": "custom_variant_picker",
      "name": "Custom Variant Picker",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "target_option_name",
          "label": "Target option name",
          "info": "Case-insensitive. Leave blank to auto-detect (Size/Jars/Pack/Quantity)."
        },
        {
          "type": "color",
          "id": "tile_bg",
          "label": "Tile background"
        },
        {
          "type": "color",
          "id": "tile_text",
          "label": "Tile text"
        },
        {
          "type": "color",
          "id": "tile_border",
          "label": "Tile border"
        },
        {
          "type": "color",
          "id": "tile_bg_selected",
          "label": "Selected tile background"
        },
        {
          "type": "color",
          "id": "tile_border_selected",
          "label": "Selected tile border"
        },
        {
          "type": "color",
          "id": "price_color",
          "label": "Price text color"
        },
        {
          "type": "color",
          "id": "selected_text",
          "label": "Selected text color"
        },
        {
          "type": "color",
          "id": "selected_compare",
          "label": "Selected compare-at color"
        }
      ]
    },
    {
      "type": "product_variations",
      "name": "Product variations",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Link products of different colors using swatches. [Learn more](https://support.maestrooo.com/article/307-product-creating-product-variations-that-link-to-different-pages)"
        },
        {
          "type": "text",
          "id": "option_name",
          "label": "Option name",
          "info": "Example: Color, Style..."
        },
        {
          "type": "text",
          "id": "option_value_metafield",
          "label": "Option value metafield",
          "info": "Enter namespace and key of the metafield holding the value. Eg.: custom.color"
        },
        {
          "type": "product_list",
          "id": "products",
          "label": "Products",
          "info": "Select all the variations (including the product itself)."
        },
        {
          "type": "select",
          "id": "selector_style",
          "label": "Selector style",
          "info": "Product image mode requires that all variant have an associated image. [Learn more](https://help.shopify.com/en/manual/products/product-media/add-images-variants#add-images-to-existing-variants)",
          "options": [
            {
              "value": "block",
              "label": "Block"
            },
            {
              "value": "block_swatch",
              "label": "Block with swatch"
            },
            {
              "value": "swatch",
              "label": "Swatch"
            },
            {
              "value": "variant_image",
              "label": "Variant image"
            }
          ],
          "default": "swatch"
        }
      ]
    },
    {
      "type": "line_item_property",
      "name": "Line item property",
      "settings": [
        {
          "type": "paragraph",
          "content": "Line item properties are used to collect customization information for an item added to the cart."
        },
        {
          "type": "text",
          "id": "label",
          "label": "Label",
          "default": "Your label"
        },
        {
          "type": "select",
          "id": "type",
          "label": "Type",
          "options": [
            {
              "value": "text",
              "label": "Text"
            },
            {
              "value": "checkbox",
              "label": "Checkbox"
            }
          ],
          "default": "text"
        },
        {
          "type": "checkbox",
          "id": "required",
          "label": "Required",
          "info": "For text property, the customer must write a text ; for checkbox, the customer must check it to add to cart.",
          "default": false
        },
        {
          "type": "header",
          "content": "Text",
          "info": "Only applicable for line item property of type Text."
        },
        {
          "type": "checkbox",
          "id": "allow_long_text",
          "label": "Allow long text",
          "default": false
        },
        {
          "type": "number",
          "id": "max_length",
          "label": "Maximum number of characters"
        }
      ]
    },
    {
      "type": "quantity_selector",
      "name": "Quantity selector",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "The selector is automatically hidden if all variants are sold out. When at least one variant is available, the selector is always visible to prevent the page from moving when switching variants."
        }
      ]
    },
    {
      "type": "inventory",
      "name": "Inventory",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "low_inventory_threshold",
          "label": "Low inventory threshold",
          "info": "Use low stock color when quantity is below the threshold. Choose 0 to always show in stock.",
          "min": 0,
          "max": 100,
          "step": 1,
          "default": 0
        }
      ]
    },
    {
      "type": "buy_buttons",
      "name": "Buy buttons",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_payment_button",
          "label": "Show dynamic checkout button",
          "info": "Each customer will see their preferred payment method from those available on your store, such as PayPal or Apple Pay. [Learn more](https://help.shopify.com/manual/using-themes/change-the-layout/dynamic-checkout)",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_gift_card_recipient",
          "label": "Show recipient information form for gift cards",
          "info": "Allow buyers to send gift cards along with a personal message. [Learn more](https://help.shopify.com/manual/online-store/themes/customizing-themes/add-gift-card-recipient-fields)",
          "default": true
        },
        {
          "type": "color",
          "id": "atc_button_background",
          "label": "Add to cart background"
        },
        {
          "type": "color",
          "id": "atc_button_text_color",
          "label": "Add to cart color"
        },
        {
          "type": "color",
          "id": "payment_button_background",
          "label": "Buy now button background"
        },
        {
          "type": "color",
          "id": "payment_button_text_color",
          "label": "Buy now button color"
        },
        {
          "type": "text",
          "id": "atc_label",
          "label": "Add to cart text",
          "default": "Add to Cart"
        },
        {
          "type": "checkbox",
          "id": "show_price_in_atc",
          "label": "Show price inside ATC button",
          "default": true
        },
        {
          "type": "image_picker",
          "id": "atc_icon",
          "label": "ATC icon (optional)"
        },
        {
          "type": "color",
          "id": "atc_price_color",
          "label": "ATC price color"
        },
        {
          "type": "color",
          "id": "atc_compare_color",
          "label": "ATC compare-at color"
        }
,
        {
          "type": "select",
          "id": "atc_corner_style",
          "label": "Button corner style",
          "options": [
            { "value": "square", "label": "Plain rectangle" },
            { "value": "semi", "label": "Semi-rounded" },
            { "value": "pill", "label": "Rounded (pill)" }
          ],
          "default": "semi"
        }
      ]
    },
    {
      "type": "guarantee_box",
      "name": "Guarantee box",
      "settings": [
        { "type": "image_picker", "id": "icon", "label": "Icon" },
        { "type": "text", "id": "title", "label": "Title", "default": "30-Day Return Guarantee" },
        { "type": "textarea", "id": "text", "label": "Text", "default": "If it's not your thing, return your unused product within 30 days and we’ll fully refund you." },
        { "type": "color", "id": "bg", "label": "Background color", "default": "#ffffff" },
        { "type": "color", "id": "border", "label": "Border color", "default": "#e5e7eb" },
        { "type": "color", "id": "color", "label": "Text color", "default": "#111111" }
,
        { "type": "range", "id": "icon_width", "label": "Icon width", "min": 16, "max": 80, "step": 2, "unit": "px", "default": 28 }

      ]
    },
    {
      "type": "stock_left",
      "name": "Stock left notice",
      "settings": [
        { "type": "image_picker", "id": "icon", "label": "Icon" },
        { "type": "text", "id": "prefix", "label": "Prefix text", "default": "We only have" },
        { "type": "text", "id": "suffix", "label": "Suffix text", "default": "left in stock" },
        { "type": "range", "id": "min_random", "label": "Minimum random", "min": 1, "max": 30, "step": 1, "default": 4 },
        { "type": "range", "id": "max_random", "label": "Maximum random", "min": 1, "max": 50, "step": 1, "default": 8 }
      ]
    },
    {
      "type": "pdp_accordions",
      "name": "Accordions (up to 5)",
      "settings": [
        { "type": "color", "id": "icon_color", "label": "Icon color" },
        { "type": "color", "id": "icon_active_color", "label": "Icon color (active)" },
        { "type": "color", "id": "border_color", "label": "Border color" },
        { "type": "color", "id": "border_active_color", "label": "Border color (active)" },
        { "type": "color", "id": "background_color", "label": "Background color" },
        { "type": "color", "id": "background_active_color", "label": "Background color (active)" },
        { "type": "color", "id": "heading_color", "label": "Heading color" },
        { "type": "color", "id": "heading_active_color", "label": "Heading color (active)" },
        { "type": "color", "id": "content_color", "label": "Content text color" },
        { "type": "color", "id": "content_active_color", "label": "Content text color (active)" },
        { "type": "text", "id": "title1", "label": "Title 1" },
        { "type": "richtext", "id": "content1", "label": "Content 1" },
        { "type": "text", "id": "title2", "label": "Title 2" },
        { "type": "richtext", "id": "content2", "label": "Content 2" },
        { "type": "text", "id": "title3", "label": "Title 3" },
        { "type": "richtext", "id": "content3", "label": "Content 3" },
        { "type": "text", "id": "title4", "label": "Title 4" },
        { "type": "richtext", "id": "content4", "label": "Content 4" },
        { "type": "text", "id": "title5", "label": "Title 5" },
        { "type": "richtext", "id": "content5", "label": "Content 5" }
      ]
    },
    {
      "type": "pickup_availability",
      "name": "Pickup availability",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Allow your customers to see availability in retail stores by [setting up local pickup](https://help.shopify.com/en/manual/sell-in-person/shopify-pos/order-management/local-pickup-for-online-orders)."
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "Text",
          "default": "<p>Share some content to your customers about your products.</p>"
        }
      ]
    },
    {
      "type": "collapsible_text",
      "name": "Collapsible text",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Title"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Share some content to your customers about your products.</p>"
        },
        {
          "type": "page",
          "id": "page",
          "label": "Page",
          "info": "Replaces inline content if specified."
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ],
          "default": "start"
        },
        {
          "type": "range",
          "id": "max_width",
          "min": 50,
          "max": 500,
          "step": 10,
          "unit": "px",
          "label": "Width",
          "default": 150
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "settings": [
        {
          "type": "paragraph",
          "content": "Create link to your contact page, external marketplace..."
        },
        {
          "type": "url",
          "id": "link",
          "label": "Link"
        },
        {
          "type": "text",
          "id": "text",
          "label": "Text",
          "default": "Button"
        },
        {
          "type": "select",
          "id": "size",
          "label": "Size",
          "options": [
            {
              "value": "sm",
              "label": "Small"
            },
            {
              "value": "base",
              "label": "Medium"
            },
            {
              "value": "lg",
              "label": "Large"
            },
            {
              "value": "xl",
              "label": "X-Large"
            }
          ],
          "default": "xl"
        },
        {
          "type": "select",
          "id": "style",
          "label": "Style",
          "options": [
            {
              "value": "outline",
              "label": "Outline"
            },
            {
              "value": "fill",
              "label": "Fill"
            }
          ],
          "default": "fill"
        },
        {
          "type": "checkbox",
          "id": "stretch",
          "label": "Stretch",
          "default": true
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text"
        }
      ]
    },
    {
      "type": "liquid",
      "name": "Custom Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      ]
    },
    {
      "type": "associated_products",
      "name": "Complementary products",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "To select complementary products, use the Search & Discovery app. [Learn more](https://help.shopify.com/en/manual/online-store/search-and-discovery/product-recommendations#complementary-products)"
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Buy it with"
        },
        {
          "type": "checkbox",
          "id": "stack_products",
          "label": "Stack products",
          "default": true
        },
        {
          "type": "range",
          "id": "products_count",
          "min": 1,
          "max": 10,
          "label": "Products to show",
          "default": 5
        },
        {
          "type": "header",
          "content": "Colors",
          "info": "Cards are bordered when background matches section background."
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text"
        }
      ]
    },
    {
      "type": "offer",
      "name": "Offer",
      "settings": [
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            }
          ],
          "default": "start"
        },
        {
          "type": "select",
          "id": "icon_position",
          "label": "Icon position",
          "options": [
            {
              "value": "aligned",
              "label": "Aligned horizontally"
            },
            {
              "value": "stacked",
              "label": "Stacked"
            }
          ],
          "default": "aligned"
        },
        {
          "type": "select",
          "id": "icon",
          "label": "Icon",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-percent",
              "label": "Percent",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-star",
              "label": "Star",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-building",
              "label": "Building",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-award-gift",
              "label": "Award gift",
              "group": "Shop"
            },
            {
              "value": "picto-happy",
              "label": "Happy",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-pin",
              "label": "Pin",
              "group": "Shipping"
            },
            {
              "value": "picto-timer",
              "label": "Timer",
              "group": "Shipping"
            },
            {
              "value": "picto-validation",
              "label": "Validation",
              "group": "Shipping"
            },
            {
              "value": "picto-truck",
              "label": "Truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return",
              "label": "Return",
              "group": "Shipping"
            },
            {
              "value": "picto-earth",
              "label": "Earth",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-profile",
              "label": "Secure profile",
              "group": "Payment & Security"
            },
            {
              "value": "picto-money",
              "label": "Money",
              "group": "Payment & Security"
            },
            {
              "value": "picto-recycle",
              "label": "Recycle",
              "group": "Ecology"
            },
            {
              "value": "picto-leaf",
              "label": "Leaf",
              "group": "Ecology"
            },
            {
              "value": "picto-tree",
              "label": "Tree",
              "group": "Ecology"
            },
            {
              "value": "picto-mobile-phone",
              "label": "Mobile phone",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Operator",
              "group": "Communication"
            },
            {
              "value": "picto-mailbox",
              "label": "Mailbox",
              "group": "Communication"
            },
            {
              "value": "picto-envelope",
              "label": "Envelope",
              "group": "Communication"
            },
            {
              "value": "picto-comment",
              "label": "Comment",
              "group": "Communication"
            },
            {
              "value": "picto-question",
              "label": "Question",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-at-sign",
              "label": "At sign",
              "group": "Tech"
            },
            {
              "value": "picto-camera",
              "label": "Camera",
              "group": "Tech"
            },
            {
              "value": "picto-wifi",
              "label": "WiFi",
              "group": "Tech"
            },
            {
              "value": "picto-bluetooth",
              "label": "Bluetooth",
              "group": "Tech"
            },
            {
              "value": "picto-printer",
              "label": "Printer",
              "group": "Tech"
            },
            {
              "value": "picto-smart-watch",
              "label": "Smart watch",
              "group": "Tech"
            },
            {
              "value": "picto-coffee",
              "label": "Coffee",
              "group": "Food & Drink"
            },
            {
              "value": "picto-burger",
              "label": "Burger",
              "group": "Food & Drink"
            },
            {
              "value": "picto-beer",
              "label": "Beer",
              "group": "Food & Drink"
            },
            {
              "value": "picto-target",
              "label": "Target",
              "group": "Other"
            },
            {
              "value": "picto-document",
              "label": "Document",
              "group": "Other"
            },
            {
              "value": "picto-jewelry",
              "label": "Jewelry",
              "group": "Other"
            },
            {
              "value": "picto-music",
              "label": "Music",
              "group": "Other"
            },
            {
              "value": "picto-file",
              "label": "File",
              "group": "Other"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Other"
            },
            {
              "value": "picto-stop",
              "label": "Stop",
              "group": "Other"
            }
          ],
          "default": "picto-coupon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon",
          "info": "240 x 240px .png recommended"
        },
        {
          "type": "range",
          "id": "icon_width",
          "min": 20,
          "max": 100,
          "step": 4,
          "unit": "px",
          "label": "Icon width",
          "default": 24
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Shipping"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Short content about your shipping rates or discounts.</p>"
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background",
          "default": "#eaf2ed"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text",
          "default": "#00a341"
        }
      ]
    },
    {
      "type": "benefit_checklist",
      "name": "Benefit checklist",
      "settings": [
        {
          "type": "text",
          "id": "item1",
          "label": "Item 1",
          "default": "Enhances Focus & Mood"
        },
        {
          "type": "text",
          "id": "item2",
          "label": "Item 2",
          "default": "Reduces Bloat & Inflammation"

        },
        {
          "type": "text",
          "id": "item3",
          "label": "Item 3",
          "default": "Supports Gut Health"
        },
        {
          "type": "text",
          "id": "item4",
          "label": "Item 4"
        },
        {
          "type": "text",
          "id": "item5",
          "label": "Item 5"
        },
        {
          "type": "color",
          "id": "icon_color",
          "label": "Icon (check) color",
          "default": "#ffffff"
        },
        {
          "type": "color",
          "id": "icon_bg",
          "label": "Icon background",
          "default": "#2e6a3d"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text color",
          "default": "#2e6a3d"
        },
        {
          "type": "color",
          "id": "row_bg",
          "label": "Row background",
          "default": "#f2f6f2"
        }
      ]
    },
    {
      "type": "share_buttons",
      "name": "Share buttons",
      "settings": [
        {
          "type": "paragraph",
          "content": "To improve user experience and performance, native share buttons are used when supported."
        },
        {
          "type": "select",
          "id": "alignment",
          "label": "Alignment",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            },
            {
              "value": "end",
              "label": "Right"
            }
          ],
          "default": "start"
        }
      ]
    },
    {
      "type": "feature_badge_grid",
      "name": "Feature badges",
      "settings": [
        {
          "type": "image_picker",
          "id": "icon1",
          "label": "Icon 1"
        },
        {
          "type": "text",
          "id": "text1",
          "label": "Text 1"
        },
        {
          "type": "image_picker",
          "id": "icon2",
          "label": "Icon 2"
        },
        {
          "type": "text",
          "id": "text2",
          "label": "Text 2",
          "default": "2 Jahre Garantie"
        },
        {
          "type": "image_picker",
          "id": "icon3",
          "label": "Icon 3"
        },
        {
          "type": "text",
          "id": "text3",
          "label": "Text 3"
        },
        {
          "type": "image_picker",
          "id": "icon4",
          "label": "Icon 4"
        },
        {
          "type": "text",
          "id": "text4",
          "label": "Text 4"
        },
        {
          "type": "color",
          "id": "badge_bg",
          "label": "Badge background",
          "default": "#eef3fb"
        },
        {
          "type": "color",
          "id": "badge_text",
          "label": "Badge text color",
          "default": "#2a3a57"
        },
        {
          "type": "range",
          "id": "margin_top",
          "label": "Top margin",
          "min": 0,
          "max": 80,
          "step": 2,
          "default": 0
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "label": "Bottom margin",
          "min": 0,
          "max": 80,
          "step": 2,
          "default": 0
        }
      ]
    },
    {
      "type": "rating_banner",
      "name": "Rating strip",
      "settings": [
        {
          "type": "text",
          "id": "rating_value",
          "label": "Rating value (e.g., 4.8)",
          "default": "4.8"
        },
        {
          "type": "color",
          "id": "star_color",
          "label": "Star color",
          "default": "#2e6a3d"
        },
        {
          "type": "color",
          "id": "label_color",
          "label": "Label color",
          "default": "#2e6a3d"
        },
        {
          "type": "text",
          "id": "badge_text",
          "label": "Badge text",
          "default": "Over 27,000 Happy Customers"
        },
        {
          "type": "color",
          "id": "badge_bg",
          "label": "Badge background",
          "default": "#e7f29b"
        },
        {
          "type": "color",
          "id": "badge_text_color",
          "label": "Badge text color",
          "default": "#2e6a3d"
        },
        {
          "type": "range",
          "id": "margin_top",
          "label": "Top margin",
          "min": 0,
          "max": 80,
          "step": 2,
          "default": 0
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "label": "Bottom margin",
          "min": 0,
          "max": 80,
          "step": 2,
          "default": 0
        }
      ]
    },

    {
      "type": "review_block",
      "name": "Review block",
      "settings": [
        {
          "type": "select",
          "id": "border_style",
          "label": "Border style",
          "options": [
            { "value": "solid", "label": "Straight" },
            { "value": "dotted", "label": "Dotted" }
          ],
          "default": "dotted"
        },
        {
          "type": "color",
          "id": "border_color",
          "label": "Border color",
          "default": "#c6d4bd"
        },
        {
          "type": "color",
          "id": "background_color",
          "label": "Background color",
          "default": "#ffffff"
        },
        {
          "type": "text",
          "id": "reviewer_name",
          "label": "Reviewer name",
          "default": "John D."
        },
        {
          "type": "color",
          "id": "name_color",
          "label": "Name color",
          "default": "#111111"
        },
        {
          "type": "checkbox",
          "id": "show_verified",
          "label": "Show verified check",
          "default": true
        },
        {
          "type": "color",
          "id": "check_color",
          "label": "Verified icon color",
          "default": "#2e6a3d"
        },
        {
          "type": "range",
          "id": "stars",
          "label": "Star rating",
          "min": 1,
          "max": 5,
          "step": 1,
          "default": 5
        },
        {
          "type": "color",
          "id": "star_color",
          "label": "Star color",
          "default": "#2e6a3d"
        },
        {
          "type": "richtext",
          "id": "review_text",
          "label": "Review text",
          "default": "<p>I love this product. Great quality and fast shipping.</p>"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Review text color",
          "default": "#333333"
        },
        {
          "type": "image_picker",
          "id": "icon_image",
          "label": "Reviewer image/icon"
        },
        {
          "type": "range",
          "id": "image_width",
          "label": "Image width",
          "min": 20,
          "max": 80,
          "step": 2,
          "unit": "px",
          "default": 28
        }
,
        {
          "type": "select",
          "id": "corner_style",
          "label": "Corner style",
          "options": [
            { "value": "square", "label": "Plain rectangle" },
            { "value": "semi", "label": "Semi-rounded" },
            { "value": "pill", "label": "Rounded (pill)" }
          ],
          "default": "semi"
        },
        {
          "type": "text",
          "id": "verified_text",
          "label": "Verified label",
          "default": "Verified"
        },
        {
          "type": "range",
          "id": "margin_top",
          "label": "Top margin",
          "min": 0,
          "max": 80,
          "step": 2,
          "default": 0
        },
        {
          "type": "range",
          "id": "margin_bottom",
          "label": "Bottom margin",
          "min": 0,
          "max": 80,
          "step": 2,
          "default": 0
        }
,
        {
          "type": "range",
          "id": "check_size",
          "label": "Verified icon size",
          "min": 10,
          "max": 28,
          "step": 1,
          "unit": "px",
          "default": 14
        },
        {
          "type": "range",
          "id": "star_size",
          "label": "Star icon size",
          "min": 10,
          "max": 24,
          "step": 1,
          "unit": "px",
          "default": 14
        },
        {
          "type": "select",
          "id": "image_position",
          "label": "Image position",
          "options": [
            { "value": "left", "label": "Left" },
            { "value": "right", "label": "Right" }
          ],
          "default": "left"
        },
        {
          "type": "checkbox",
          "id": "stack_on_mobile",
          "label": "Stack image above text on mobile",
          "default": true
        },
        {
          "type": "range",
          "id": "dotted_gap",
          "label": "Dotted border gap (padding)",
          "min": 0,
          "max": 24,
          "step": 2,
          "unit": "px",
          "default": 10
        }


      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_fixed_add_to_cart",
      "label": "Show add to cart on scroll",
      "default": true
    },
    {
      "type": "header",
      "content": "Media"
    },
    {
      "type": "paragraph",
      "content": "Learn more about [media types](https://help.shopify.com/en/manual/products/product-media)"
    },
    {
      "type": "range",
      "id": "desktop_media_width",
      "label": "Desktop media size",
      "min": 35,
      "max": 65,
      "step": 5,
      "unit": "%",
      "default": 55
    },
    {
      "type": "select",
      "id": "desktop_media_layout",
      "label": "Desktop media layout",
      "options": [
        {
          "value": "grid",
          "label": "Grid"
        },
        {
          "value": "grid_highlight",
          "label": "Grid with main media"
        },
        {
          "value": "carousel_thumbnails_left",
          "label": "Thumbnails left (carousel)"
        },
        {
          "value": "carousel_thumbnails_bottom",
          "label": "Thumbnails bottom (carousel)"
        }
      ],
      "default": "carousel_thumbnails_left"
    },
    {
      "type":"select",
      "id": "mobile_media_size",
      "label": "Mobile media size",
      "options": [
        {
          "value": "expanded",
          "label": "Expanded"
        },
        {
          "value": "contained",
          "label": "Contained"
        }
      ],
      "default": "expanded"
    },
    {
      "type": "select",
      "id": "mobile_carousel_control",
      "label": "Mobile carousel control",
      "options": [
        {
          "value": "dots",
          "label": "Dots"
        },
        {
          "value": "floating_dots",
          "label": "Floating dots"
        },
        {
          "value": "thumbnails",
          "label": "Thumbnails"
        },
        {
          "value": "free_scroll",
          "label": "Free scroll"
        }
      ],
      "default": "floating_dots"
    },
    {
      "type": "checkbox",
      "id": "enable_video_autoplay",
      "label": "Enable video autoplay",
      "info": "Video are muted automatically to allow autoplay. Grid mode on desktop turn off autoplay.",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "enable_video_looping",
      "label": "Enable video looping",
      "default": true
    },
    {
      "type": "header",
      "content": "Image zoom"
    },
    {
      "type": "checkbox",
      "id": "enable_image_zoom",
      "label": "Enable",
      "default": true
    },
    {
      "type": "range",
      "id": "max_image_zoom_level",
      "min": 1,
      "max": 4,
      "step": 0.5,
      "label": "Max zoom level",
      "default": 3
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    },
    {
      "type": "header",
      "content": "Input colors",
      "info": "Applies to elements like quantity selector and variant selectors."
    },
    {
      "type": "color",
      "id": "input_background",
      "label": "Background",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "color",
      "id": "input_text_color",
      "label": "Text"
    },
    {
      "type": "richtext",
      "id": "free_shipping_text",
      "label": "Free shipping text"
    },
    {
      "type": "richtext",
      "id": "shipping_to_text",
      "label": "Shipping to:"
    },
{
      "type": "color",
      "id": "shipping_text_background",
      "label": "Shipping Text Background"
    },
    {
      "type": "color",
      "id": "shipping_text_border",
      "label": "Shipping Text Border"
    },
    {
      "type": "color",
      "id": "shipping_text_color",
      "label": "Shipping Text Color"
    },
    {
      "type": "richtext",
      "id": "custom_order_header",
      "label": "Free order header"
    },
    {
      "type": "color",
      "id": "custom_header_color",
      "label": "Header background color"
    },
    {
      "type": "color",
      "id": "header_text_color",
      "label": "Header text color"
    },

    {
      "type": "image_picker",
      "id": "custom_order_image",
      "label": "Free order image"
    },
    {
      "type": "richtext",
      "id": "custom_order_description",
      "label": "Free order description"
    },
    {
      "type": "color",
      "id": "custom_order_background",
      "label": "Free background color"
    },
    {
      "type": "richtext",
      "id": "custom_order_text1",
      "label": "Free order text 1"
    },
    {
      "type": "color",
      "id": "custom_text2_color",
      "label": "Text 2 color"
    },
    {
      "type": "richtext",
      "id": "custom_order_text2",
      "label": "Free order text 2"
    },
    {
      "type": "color",
      "id": "custom_worth_color",
      "label": "Worth Text color"
    },
    {
      "type": "color",
      "id": "custom_badge_color",
      "label": "Badge Color"
    },
    {
      "type": "richtext",
      "id": "custom_worth_text",
      "label": "Custom worth text"
    }
  ]
}
{% endschema %}
