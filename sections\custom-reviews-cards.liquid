{%- comment -%}
Review Cards (Grid)
- Displays customer reviews as individual cards/blocks in a responsive grid
- Desktop/Laptop: 2–3 columns depending on width; Mobile: single-column stack
- Each block includes: customer name, star rating, review text, date (optional), and other metadata
- Designed to follow the theme system (colors, typography) and integrate with existing patterns
{%- endcomment -%}

{{ 'custom-typography.css' | asset_url | stylesheet_tag }}

{%- if section.blocks.size > 0 -%}
  <style>
    #shopify-section-{{ section.id }} {
      /* Section-scoped design tokens */
      --reviews-grid-columns: 1; /* default (mobile) */
      --reviews-gap: 24px; /* grid gap per reference */
      --review-card-radius: 12px; /* exact geometry */
      --review-card-bg: rgb(var(--background-primary));
      --review-card-text: rgb(var(--text-primary));
      --review-card-border: rgba(0,0,0,.06);
      --review-star: #FABE1E; /* exact star color */
    }

    @media screen and (min-width: 700px) {
      #shopify-section-{{ section.id }} { --reviews-grid-columns: 2; }
    }
    @media screen and (min-width: 1150px) {
      #shopify-section-{{ section.id }} { --reviews-grid-columns: 3; }
    }


    /* Flickity enhancement: when slider is active, cells are sized for one-at-a-time */
    #shopify-section-{{ section.id }} .reviews__grid.flickity-enabled {
      display: block !important;
      position: relative;
      overflow: hidden; /* Prevent horizontal overflow */
    }
    #shopify-section-{{ section.id }} .flickity-enabled .review {
      width: 75%; /* Show more of adjacent cards */
      margin-right: var(--reviews-gap);
      box-sizing: border-box;
    }
    @media (min-width: 480px){
      #shopify-section-{{ section.id }} .flickity-enabled .review {
        width: 70%; /* Even more preview on larger phones */
      }
    }
    @media (min-width: 768px){
      #shopify-section-{{ section.id }} .flickity-enabled .review {
        width: 85%;
      }
    }
    @media (min-width: 1000px){
      #shopify-section-{{ section.id }} .flickity-enabled .review {
        width: calc(33.333% - 16px); /* 3 cards per slide with gaps */
        margin-right: 24px;
      }
      #shopify-section-{{ section.id }} .flickity-enabled .review:nth-child(3n) {
        margin-right: 0; /* Remove margin from every 3rd card */
      }
    }

    /* Fix Flickity height constraints to show full cards */
    #shopify-section-{{ section.id }} .flickity-viewport {
      height: auto !important;
      min-height: 380px;
      overflow-x: hidden; /* Prevent horizontal overflow */
      overflow-y: visible;
      max-width: 100vw;
      box-sizing: border-box;
    }
    #shopify-section-{{ section.id }} .flickity-slider {
      height: auto !important;
    }
    #shopify-section-{{ section.id }} .flickity-enabled .review {
      height: auto !important;
      min-height: auto !important;
      display: flex !important;
      flex-direction: column !important;
    }
    #shopify-section-{{ section.id }} .flickity-enabled .review-card {
      height: 100% !important;
      min-height: auto !important;
      display: flex !important;
      flex-direction: column !important;
      flex: 1 !important;
    }
    #shopify-section-{{ section.id }} .flickity-enabled .review-card__tail {
      position: relative !important;
      margin-top: -18px !important;
    }

    /* Force equal heights in Flickity slider */
    #shopify-section-{{ section.id }} .flickity-enabled .review-card__text {
      flex: 1 !important;
    }
    #shopify-section-{{ section.id }} .flickity-enabled .review-footer {
      margin-top: auto !important;
    }

    /* Desktop specific equal heights */
    @media (min-width: 1000px) {
      #shopify-section-{{ section.id }} .flickity-slider {
        display: flex !important;
        align-items: stretch !important;
      }
      #shopify-section-{{ section.id }} .flickity-enabled .review {
        height: 100% !important;
        min-height: 300px !important;
      }
      #shopify-section-{{ section.id }} .flickity-enabled .review-card {
        height: 100% !important;
        min-height: 300px !important;
      }
    }

    #shopify-section-{{ section.id }} .reviews__header h2 {
      text-transform: uppercase;
      letter-spacing: .06em;
      text-align: center;
      margin: 0 0 var(--spacing-8);
    }

    #shopify-section-{{ section.id }} .reviews__grid {
      display: grid;
      grid-template-columns: repeat(var(--reviews-grid-columns), minmax(0, 1fr));
      gap: var(--reviews-gap);
      align-items: stretch; /* ensure items fill row height */
    }

    /* Force equal heights for review cards */
    #shopify-section-{{ section.id }} .review {
      display: flex;
      flex-direction: column;
      height: 100%;
    }



    #shopify-section-{{ section.id }} .review-card {
      background: var(--review-card-bg);
      color: var(--review-card-text);
      border: 1px solid var(--review-card-border);
      border-radius: var(--review-card-radius);
      padding: 24px;
      display: flex;
      flex-direction: column;
      gap: 6px; /* tighter rhythm between title and body */
      box-shadow: 0 8px 22px rgba(0,0,0,.06);
      margin-block-end: 16px; /* room for speech tail */
      flex: 1; /* stretch to fill available height */
    }

    /* Push footer to bottom of card */
    #shopify-section-{{ section.id }} .review-card__text {
      flex: 1;
    }

    /* Ensure footer stays at bottom */
    #shopify-section-{{ section.id }} .review-footer {
      margin-top: auto;
    }
    /* Centered speech tail beneath each card */
    #shopify-section-{{ section.id }} .review-card__tail{
      display:block;
      margin:-18px auto 0; /* snug under the card and centered */
      width:0; height:0;
      border-left:16px solid transparent; border-right:16px solid transparent;
      border-top:16px solid var(--review-card-bg); /* downward pointer */
      filter: drop-shadow(0 8px 22px rgba(0,0,0,.06));
    }



    #shopify-section-{{ section.id }} .review-card__rating {
      display: flex; align-items: center; gap: 6px; color: var(--review-star);
    }
    #shopify-section-{{ section.id }} .review-card__rating .rating__stars svg { color: var(--review-star); }

    #shopify-section-{{ section.id }} .review-card__title { margin: 0; font-weight: 800; }
    #shopify-section-{{ section.id }} .review-card__text {
      color: rgb(var(--text-primary) / .9);
      /* Ensure clean text rendering on all devices */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
    }


    #shopify-section-{{ section.id }} .review-footer { display: flex; align-items: center; gap: 12px; margin-top: 20px; }
    #shopify-section-{{ section.id }} .review-footer__avatar { width: 56px; height: 56px; border-radius: 999px; object-fit: cover; flex-shrink: 0; }
    #shopify-section-{{ section.id }} .review-footer__meta { flex: 1; min-width: 0; }
    #shopify-section-{{ section.id }} .review-footer__meta > * { flex: 0 0 auto; }
    #shopify-section-{{ section.id }} .review-footer__name { font-weight: 700; }
    #shopify-section-{{ section.id }} .review-footer__verified { display: inline-flex; align-items: center; gap: 6px; color: #22C55E; font-weight: 600; font-size: var(--text-sm, 0.875rem); white-space: nowrap; }
    #shopify-section-{{ section.id }} .review-footer__verified svg,
    #shopify-section-{{ section.id }} .review-footer__verified img { width: 16px; height: 16px; }
    #shopify-section-{{ section.id }} .review-footer__date { color: rgb(var(--text-primary) / .55); font-size: var(--text-sm, 0.875rem); }
    #shopify-section-{{ section.id }} .review-footer__meta > div { display: flex; align-items: center; gap: 10px; }

    /* Mobile-specific styling to ensure two-column layout is maintained */
    @media (max-width: 768px) {
      #shopify-section-{{ section.id }} .review-footer {
        display: flex !important;
        align-items: flex-start !important;
        gap: 10px !important;
        flex-wrap: nowrap !important;
      }
      #shopify-section-{{ section.id }} .review-footer__avatar {
        width: 48px !important;
        height: 48px !important;
        flex-shrink: 0 !important;
      }
      #shopify-section-{{ section.id }} .review-footer__meta {
        flex: 1 !important;
        min-width: 0 !important;
        display: flex !important;
        flex-direction: column !important;
        gap: 4px !important;
      }
      #shopify-section-{{ section.id }} .review-footer__name {
        font-size: 14px !important;
        line-height: 1.2 !important;
      }
      #shopify-section-{{ section.id }} .review-footer__verified {
        font-size: 12px !important;
        gap: 4px !important;
      }
      #shopify-section-{{ section.id }} .review-footer__verified svg,
      #shopify-section-{{ section.id }} .review-footer__verified img {
        width: 14px !important;
        height: 14px !important;
      }
      #shopify-section-{{ section.id }} .review-footer__date {
        font-size: 12px !important;
      }
      #shopify-section-{{ section.id }} .review-footer__meta > div {
        flex-wrap: wrap !important;
        gap: 8px !important;
      }
    }

    /* Typography handled by .custom-heading-secondary and .custom-reviews classes */

    /* Mobile text styling fixes */
    @media (max-width: 768px) {
      #shopify-section-{{ section.id }} .review-card__text {
        color: rgb(var(--text-primary)) !important;
        opacity: 1 !important;
        text-shadow: none !important;
        border: none !important;
        outline: none !important;
        -webkit-text-stroke: none !important;
        text-stroke: none !important;
      }

      #shopify-section-{{ section.id }} .review-card__text p {
        color: rgb(var(--text-primary)) !important;
        opacity: 1 !important;
        text-shadow: none !important;
        border: none !important;
        outline: none !important;
        -webkit-text-stroke: none !important;
        text-stroke: none !important;
      }

      /* Ensure proper spacing between elements on mobile */
      #shopify-section-{{ section.id }} .section-stack {
        margin-top: 32px !important;
        padding-top: 24px !important;
      }

      #shopify-section-{{ section.id }} .reviews__header {
        margin-bottom: 24px !important;
      }

      #shopify-section-{{ section.id }} .review-card {
        margin-bottom: 20px !important;
      }

      /* Additional mobile text fixes */
      #shopify-section-{{ section.id }} .review-card__title {
        color: rgb(var(--text-primary)) !important;
        opacity: 1 !important;
      }

      #shopify-section-{{ section.id }} .rte,
      #shopify-section-{{ section.id }} .rte * {
        color: rgb(var(--text-primary)) !important;
        opacity: 0.9 !important;
        text-shadow: none !important;
        -webkit-text-stroke: none !important;
      }
    }

    /* Stars color to match mock */
    #shopify-section-{{ section.id }} .rating__star{ color:#FABE1E; }

    /* Title/body rhythm - remove duplicates, already defined above */

    /* Product line and link accent */
    #shopify-section-{{ section.id }} .review-card .text-subdued strong{ font-weight:800; }
    #shopify-section-{{ section.id }} .review-card .text-subdued a{ color:#C7794E; font-weight:700; text-decoration:none; }

    /* Footer: larger avatar + verified green - remove duplicates, already defined above */

    /* Prevent section-wide overflow */
    #shopify-section-{{ section.id }} {
      overflow-x: hidden;
      max-width: 100vw;
      box-sizing: border-box;
    }
    #shopify-section-{{ section.id }} .reviews__grid {
      max-width: 100%;
      overflow-x: hidden;
    }

    /* Mobile: Remove left/right padding for full width */
    @media (max-width: 999px) {
      #shopify-section-{{ section.id }} .section-stack {
        padding-left: 10px !important;
        padding-right: 0 !important;
        margin-top: 40px !important; /* Add more space from previous section */
        padding-top: 32px !important; /* Add internal top padding */
      }
      #shopify-section-{{ section.id }} .reviews__header {
        padding-left: 16px;
        padding-right: 16px;
        margin-bottom: 32px !important; /* More space before cards */
      }
      #shopify-section-{{ section.id }} .reviews__grid {
        padding-left: 0;
        padding-right: 0;
      }
      /* Ensure Flickity viewport is full width */
      #shopify-section-{{ section.id }} .flickity-viewport {
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
      }

      /* Apply .bg-custom styles specifically to review cards section on mobile */
      #shopify-section-{{ section.id }} .bg-custom {
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
      }
    }

    /* Flickity dots styling for review cards */
    #shopify-section-{{ section.id }} .flickity-page-dots {
      position: relative;
      bottom: 0;
      margin-top: 12px;
      text-align: center;
      line-height: 1;
    }
    #shopify-section-{{ section.id }} .flickity-page-dots .dot {
      display: inline-block;
      width: 10px;
      height: 10px;
      margin: 0 8px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      opacity: 0.25;
      cursor: pointer;
      transition: opacity 0.2s ease, background-color 0.2s ease;
    }
    #shopify-section-{{ section.id }} .flickity-page-dots .dot.is-selected {
      opacity: 1;
      background: #000;
    }
    #shopify-section-{{ section.id }} .flickity-page-dots .dot:hover {
      opacity: 0.7;

    /* Flickity arrows styling (reviews) */
    #shopify-section-{{ section.id }} .flickity-prev-next-button {
      width: 36px; height: 36px; border-radius: 9999px;
      background: rgba(0,0,0,0.55);
      top: 50%; transform: translateY(-50%);
      z-index: 2;
    }
    #shopify-section-{{ section.id }} .flickity-prev-next-button.previous { left: 8px; }
    #shopify-section-{{ section.id }} .flickity-prev-next-button.next { right: 8px; }
    #shopify-section-{{ section.id }} .flickity-prev-next-button .flickity-button-icon { fill: #fff; }



  </style>


  <div {% render 'section-properties' %}>
    <div class="section-stack">
      <div class="reviews__header">
        {%- if section.settings.heading != blank -%}
          <h2 class="h2 custom-heading-primary custom-reviews">{{ section.settings.heading | escape }}</h2>
        {%- endif -%}
        {%- if section.settings.subheading != blank -%}
          <p class="text-subdued custom-desc-text" style="text-align:center;max-width:780px;margin:0 auto">{{ section.settings.subheading }}</p>
        {%- endif -%}
      </div>

      {% assign review_count = 0 %}
      {% for b in section.blocks %}{% if b.type == 'review' %}{% assign review_count = review_count | plus: 1 %}{% endif %}{% endfor %}

      <div class="reviews__grid" id="reviews-{{ section.id }}-track" data-count="{{ review_count }}">
        {%- for block in section.blocks -%}
          {%- if block.type == 'review' -%}
            <div class="review">
              <article class="review-card" {{ block.shopify_attributes }}>
                {%- if block.settings.show_rating -%}
                  <div class="review-card__rating rating" aria-label="{{ block.settings.rating }} out of 5 stars">
                    <div class="rating__stars">
                      {%- for i in (1..5) -%}
                        {%- if i <= block.settings.rating -%}
                          {%- render 'icon' with 'rating-star', class: 'rating__star rating__star--full' -%}
                        {%- else -%}
                          {%- render 'icon' with 'rating-star', class: 'rating__star rating__star--empty' -%}
                        {%- endif -%}
                      {%- endfor -%}
                    </div>
                  </div>
                {%- endif -%}

                {%- if block.settings.title != blank -%}
                  <h3 class="review-card__title">{{ block.settings.title | escape }}</h3>
                {%- endif -%}
                {%- if block.settings.content != blank -%}
                  <div class="review-card__text rte custom-card-text">{{ block.settings.content }}</div>
                {%- endif -%}
                {%- if block.settings.product_name != blank -%}
                  <p class="text-subdued"><strong>Product:</strong> {%- if block.settings.product_url != blank -%}<a href="{{ block.settings.product_url }}">{{ block.settings.product_name }}</a>{%- else -%}{{ block.settings.product_name }}{%- endif -%}</p>
                {%- endif -%}
              </article>
              <div class="review-card__tail" aria-hidden="true"></div>

              <div class="review-footer">
                {%- if block.settings.avatar != blank -%}
                  {{ block.settings.avatar | image_url: width: 120 | image_tag: loading: 'lazy', class: 'review-footer__avatar', widths: '60,90,120' }}
                {%- endif -%}
                <div class="review-footer__meta">
                  {%- if block.settings.author != blank -%}
                    <div class="review-footer__name">{{ block.settings.author }}</div>
                  {%- endif -%}
                  <div>
                    {%- if block.settings.verified -%}
                      <span class="review-footer__verified">
                        {%- render 'icon', icon: 'success', width: 16, height: 16 -%}
                        {{ block.settings.verified_label | default: 'Verified Buyer' }}
                      </span>
                    {%- endif -%}
                    {%- if block.settings.date != blank -%}
                      <span class="review-footer__date">{{ block.settings.date }}</span>
                    {%- endif -%}
                  </div>
                </div>
              </div>
            </div>
          {%- endif -%}
        {%- endfor -%}
      </div>
    </div>


      <link rel="stylesheet" href="https://unpkg.com/flickity@2/dist/flickity.min.css">
      <script src="https://unpkg.com/flickity@2/dist/flickity.pkgd.min.js" defer></script>
      <script src="{{ 'flickity-utils.js' | asset_url }}" defer></script>

      <script>
        function initReviewsSlider{{ section.id | replace: '-', '' }}(){
          if(typeof FlickityUtils === 'undefined'){
            setTimeout(initReviewsSlider{{ section.id | replace: '-', '' }}, 100);
            return;
          }

          // Initialize using FlickityUtils
          const sliderControl = FlickityUtils.initReviews('reviews-{{ section.id }}-track', {
            // Enable slider on all screen sizes, show 3 per slide on desktop
            maxDesktopItems: 3,
            customConfig: {
              pageDots: {{ section.settings.show_dots | default: true | json }},
              prevNextButtons: {{ section.settings.show_arrows | default: true | json }},
              groupCells: true, // Group cells together for multi-cell slides
              cellAlign: 'left',
              adaptiveHeight: false, // Disable adaptive height for equal heights
              setGallerySize: false // Let CSS control the height
            },
            onInit: function(flkty) {
              console.log('Reviews slider initialized successfully');

              // Force equal heights after initialization
              setTimeout(() => {
                const slides = flkty.element.querySelectorAll('.review-card');
                if (slides.length > 0) {
                  let maxHeight = 0;
                  slides.forEach(slide => {
                    slide.style.height = 'auto';
                    maxHeight = Math.max(maxHeight, slide.offsetHeight);
                  });
                  slides.forEach(slide => {
                    slide.style.height = maxHeight + 'px';
                  });
                }
              }, 100);
            }
          });

          // Store reference for potential cleanup
          window['reviewsSlider{{ section.id | replace: "-", "" }}'] = sliderControl;
        }

        if(document.readyState === 'loading'){
          document.addEventListener('DOMContentLoaded', initReviewsSlider{{ section.id | replace: '-', '' }}, {once:true});
        } else {
          initReviewsSlider{{ section.id | replace: '-', '' }}();
        }
      </script>

  </div>


{%- endif -%}


{% schema %}
{
  "name": "Custom Review Cards",
  "class": "shopify-section--custom-review-cards",
  "tag": "section",
  "disabled_on": { "groups": ["header", "custom.overlay"] },
  "settings": [
    {"type": "checkbox", "id": "full_width", "label": "Full width", "default": true},
    {"type": "text", "id": "heading", "label": "Heading", "default": "EXTRAORDINARY RESULTS"},
    {"type": "richtext", "id": "subheading", "label": "Subheading"},
    {"type": "color", "id": "background", "label": "Background"},
    {"type": "color_background", "id": "background_gradient", "label": "Background gradient"},
    {"type": "color", "id": "text_color", "label": "Text"},
    {"type": "color", "id": "heading_color", "label": "Heading color"},
    {"type": "header", "content": "Slider controls"},
    {"type": "checkbox", "id": "show_dots", "label": "Show dots", "default": true},
    {"type": "checkbox", "id": "show_arrows", "label": "Show arrows", "default": true}
  ],
  "blocks": [
    {
      "type": "review",
      "name": "Review",
      "settings": [
        {"type": "checkbox", "id": "show_rating", "label": "Show rating", "default": true},
        {"type": "range", "id": "rating", "label": "Rating", "min": 0, "max": 5, "step": 1, "default": 5},
        {"type": "text", "id": "title", "label": "Title", "default": "At 68 I wasn’t…"},
        {"type": "richtext", "id": "content", "label": "Content", "default": "<p>Share what your customers are saying about your products and results.<\/p>"},
        {"type": "text", "id": "product_name", "label": "Product name"},
        {"type": "url", "id": "product_url", "label": "Product link"},
        {"type": "image_picker", "id": "avatar", "label": "Customer avatar"},
        {"type": "text", "id": "author", "label": "Customer name", "default": "Pat T"},
        {"type": "checkbox", "id": "verified", "label": "Show verified badge", "default": true},
        {"type": "text", "id": "verified_label", "label": "Verified label", "default": "Verified Buyer"},
        {"type": "text", "id": "date", "label": "Date (optional)"}
      ]
    }
  ],
  "presets": [
    {
      "name": "Custom Review Cards",
      "blocks": [{"type":"review"},{"type":"review"},{"type":"review"}]
    }
  ]
}
{% endschema %}

