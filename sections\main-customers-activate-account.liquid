<div class="sm:container">
  <div class="customer-form">
    <div {% render 'surface', class: 'customer-form__box', background_fallback: 'bg-secondary' %}>
      <div class="customer-form__box-inner text-center">
        <div class="v-stack gap-12">
          <h1 class="h2">
            {%- assign content = 'customer.activate_account.title' | t -%}
            {%- render 'styled-text', content: content, gradient: section.settings.heading_gradient -%}
          </h1>

          {%- form 'activate_customer_password', class: 'form' -%}
            <div class="fieldset">
              {%- if form.errors -%}
                {%- assign form_errors = form.errors | default_errors -%}
                {%- render 'banner', status: 'error', content: form_errors -%}
              {%- endif -%}

              {%- assign password_label = 'customer.activate_account.password' | t -%}
              {%- render 'input', type: 'password', name: 'customer[password]', label: password_label, autocomplete: 'new-password' -%}

              {%- assign password_label_confirmation = 'customer.activate_account.password_confirmation' | t -%}
              {%- render 'input', type: 'password', name: 'customer[password_confirmation]', label: password_label_confirmation, autocomplete: 'new-password' -%}
            </div>

            <div class="fieldset">
              {%- assign submit_label = 'customer.activate_account.submit' | t -%}
              {%- render 'button', content: submit_label, type: 'submit', size: 'xl', stretch: true, secondary: true -%}

              {%- assign cancel_label = 'customer.activate_account.cancel' | t -%}
              {%- render 'button', content: cancel_label, name: 'decline', type: 'submit', size: 'xl', stretch: true, secondary: true, style: 'outline' -%}
            </div>
          {%- endform -%}
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Customer activate account",
  "class": "shopify-section--main-customers-activate-account",
  "tag": "section"
}
{% endschema %}