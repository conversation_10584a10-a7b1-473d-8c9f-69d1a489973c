<style>
  .image-review {
    padding: 15px;
  }
  .review-wrapper {
    max-width: 1300px;
    margin: auto;
  }
  .slider-title {
    color: #000;
    font-size: 28px;
    font-weight: 600;
    text-align: center;
  }
  .slider-content {
    color: #000;
    font-size: 18px;
    font-weight: 400;
    text-align: center;
    margin-bottom: 20px;
  }
  .swiper {
    width: 100%;
    overflow: hidden; /* Hide overflow slides */
    position: relative;
  }
  .swiper-wrapper {
    display: flex;
    gap: 10px;
    transform: translateX(0); /* Initial position for animation */
    justify-content: center; /* Center the slides when no animation */
    transition: transform 0.5s ease; /* Smooth transition */
  }
  .swiper-slide {
    flex: 0 0 auto;
    width: 240px;
    height: 100%;
  }
  .slider-image img {
    width: 100%;
    height: 350px;
    border-radius: 10px;
    object-fit: cover;
  }
  @media screen and (max-width: 768px) {
    .slider-image img {
      height: 300px;
    }
    .slider-title {
      font-size: 23px;
    }
    .swiper-slide {
      width: 33.33%; /* 3 images visible on smaller screens */
    }
  }
  @media screen and (max-width: 480px) {
    .swiper-slide {
      width: 50%; /* 2 images visible on mobile */
    }
  }

  /* Keyframes for continuous scrolling */
  @keyframes scroll-infinite {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-100%);
    }
  }
</style>





<section class="asyncLoad Slider-section">
  <div class="image-review">
    <div class="review-wrapper">
      <h2 class="slider-title">{{ section.settings.title }}</h2>
      <p class="slider-content">{{ section.settings.content }}</p>
      <div class="swiper">
        <div class="swiper-wrapper">
          {% for block in section.blocks %}
          <div class="swiper-slide">
            <div class="slider-image">
              <img
                loading="lazy"
                src="{{ block.settings.image | img_url:'250x456' }}"
                alt="Slider Image"
              />
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>
  </div>
</section>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    const swiperWrapper = document.querySelector(".swiper-wrapper");
    const slides = document.querySelectorAll(".swiper-slide");

    if (!swiperWrapper || slides.length === 0) return;

    // Calculate the total width of all slides and the container's width
    const totalSlidesWidth = Array.from(slides).reduce(
      (acc, slide) => acc + slide.offsetWidth + 10, // Adding gap width (10px)
      0
    );
    const containerWidth = swiperWrapper.offsetWidth;

    // Only enable scrolling if the slides overflow the container (total width > container width)
    if (totalSlidesWidth > containerWidth) {
      // Duplicate the slides to ensure a seamless infinite scroll
      const slidesArray = Array.from(slides);
      slidesArray.forEach(slide => {
        const clone = slide.cloneNode(true); // Clone each slide
        swiperWrapper.appendChild(clone);  // Append the clone to the end of the wrapper
      });

      // Adjust animation duration dynamically based on the total width of slides
      const animationDuration = totalSlidesWidth / 50; // Adjust speed factor here
      swiperWrapper.style.animationDuration = `${animationDuration}s`;

      // Apply the infinite scroll animation
      swiperWrapper.style.animation = `scroll-infinite ${animationDuration}s linear infinite`;

      // Disable centering if scrolling is enabled
      swiperWrapper.style.justifyContent = "flex-start";
    } else {
      // Disable scrolling and animation if there is no overflow
      swiperWrapper.style.animation = "none";
      
      // Center the images when no scrolling
      swiperWrapper.style.justifyContent = "center";
    }
  });
</script>




{% schema %}
{
  "name": "Custom Image Slider",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "text",
      "id": "content",
      "label": "Content"
    }
  ],
  "blocks": [
    {
      "type": "Image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Slider Image"
        }
      ]
    }
  ],
  "max_blocks": 10,
  "presets": [
    {
      "category": "Image",
      "name": "Custom Image Slider"
    }
  ]
}
{% endschema %}