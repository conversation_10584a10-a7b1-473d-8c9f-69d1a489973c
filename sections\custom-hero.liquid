{%- comment -%}
Custom Hero section
- Fully customizable via blocks
- Separate desktop and mobile background images
- Separate, unique CSS file scoped to this section
{%- endcomment -%}

<section class="custom-hero {% if section.settings.vertical_align == 'flex-start' %}is-top{% elsif section.settings.vertical_align == 'flex-end' %}is-bottom{% else %}is-center{% endif %}" aria-label="Hero">
  {%- comment -%} Section-scoped CSS variables and background images {%- endcomment -%}
  <style>
    #shopify-section-{{ section.id }} {
      --ch-text: {{ section.settings.text_color.rgb }};
      --ch-accent: {{ section.settings.accent_color.rgb }};
      --ch-button-bg: {{ section.settings.button_bg.rgb }};
      --ch-button-text: {{ section.settings.button_text_color.rgb }};
      --ch-star: {{ section.settings.star_color.rgb }};
      --ch-badge-bg: {{ section.settings.badge_bg.rgb }};
      --ch-badge-text: {{ section.settings.badge_text_color.rgb }};
      --ch-overlay: {{ section.settings.overlay_opacity | default: 0 | divided_by: 100.0 }};
      --ch-bg: {{ section.settings.background_color.rgb }};
      --ch-align: {{ section.settings.vertical_align }};
    }

    /* Background images */
    @media screen and (min-width: 1000px) {
      #shopify-section-{{ section.id }} .custom-hero {
        background-image: url('{{ section.settings.bg_desktop | image_url: width: 2880 }}');
        background-position: {{ section.settings.bg_position_desktop }};
        background-size: {{ section.settings.bg_size }};
        min-height: {% if section.settings.fit_viewport %}calc({{ section.settings.viewport_height_desktop | default: 100 }}dvh - var(--header-height, 0px) - var(--announcement-bar-height, 0px)){% else %}{{ section.settings.height_desktop }}px{% endif %};
      }
    }
    @media screen and (max-width: 999px) {
      #shopify-section-{{ section.id }} .custom-hero {
        background-image: url('{{ section.settings.bg_mobile | image_url: width: 1400 }}');
        background-position: {{ section.settings.bg_position_mobile }};
        background-size: {{ section.settings.bg_size }};
        min-height: {% if section.settings.fit_viewport %}calc({{ section.settings.viewport_height_mobile | default: 100 }}dvh - var(--header-height, 0px) - var(--announcement-bar-height, 0px)){% else %}{{ section.settings.height_mobile }}px{% endif %};
      }
    }
  </style>

  {{ 'custom-typography.css' | asset_url | stylesheet_tag }}
  {{ 'custom-hero.css' | asset_url | stylesheet_tag }}

  <div class="ch-overlay" aria-hidden="true"></div>

  <div class="ch-container">
    <div class="ch-content">
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when 'eyebrow' -%}
            <div class="ch-eyebrow" {{ block.shopify_attributes }}>
              {%- if block.settings.stars != blank -%}
                <span class="ch-eyebrow__stars" style="color: {{ block.settings.color }}">{{ block.settings.stars }}</span>
              {%- endif -%}
              {%- if block.settings.text != blank -%}
                <span class="ch-eyebrow__text">{{ block.settings.text }}</span>
              {%- endif -%}
            </div>

          {%- when 'heading' -%}
            <h1 class="ch-heading custom-heading-primary" {{ block.shopify_attributes }}>
              <span class="ch-heading__line1">{{ block.settings.line1 }}</span>
              <span class="ch-heading__line2" style="color: rgb(var(--ch-accent));">{{ block.settings.line2 }}</span>
            </h1>

          {%- when 'text' -%}
            <div class="ch-text rte custom-body-text" {{ block.shopify_attributes }}>{{ block.settings.body }}</div>

          {%- when 'rating' -%}
            <div class="ch-rating" {{ block.shopify_attributes }}>
              <span class="ch-stars" aria-hidden="true">
                {%- for i in (1..5) -%}
                  <svg viewBox="0 0 24 24" class="ch-star" focusable="false" aria-hidden="true"><path d="M12 17.27 18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/></svg>
                {%- endfor -%}
              </span>
              {%- if block.settings.text != blank -%}
                <span class="ch-rating__text">{{ block.settings.text }}</span>
              {%- endif -%}
            </div>

          {%- when 'button' -%}
            <div class="ch-cta" {{ block.shopify_attributes }}>
              <a class="ch-button" href="{{ block.settings.link }}">
                <span>{{ block.settings.label }}</span>
                <svg class="ch-button__icon" viewBox="0 0 24 24" aria-hidden="true"><path d="M13.172 12l-4.95-4.95 1.414-1.414L16 12l-6.364 6.364-1.414-1.414z"/></svg>
              </a>
            </div>

          {%- when 'guarantee' -%}
            <div class="ch-guarantee" {{ block.shopify_attributes }}>
              <svg viewBox="0 0 24 24" class="ch-guarantee__icon" aria-hidden="true"><path d="M12 2l7 4v6c0 5-3.5 9.5-7 10-3.5-.5-7-5-7-10V6l7-4zm-1 13l6-6-1.41-1.41L11 12.17l-2.59-2.58L7 11l4 4z"/></svg>
              <span>{{ block.settings.text }}</span>
            </div>

          {%- when 'image' -%}
            <div class="ch-side-image" {{ block.shopify_attributes }}>
              {%- if block.settings.image != blank -%}
                <img src="{{ block.settings.image | image_url: width: 1600 }}" alt="{{ block.settings.image.alt | escape }}" loading="lazy" width="{{ block.settings.image.width }}" height="{{ block.settings.image.height }}">
              {%- endif -%}
            </div>

          {%- when 'badge' -%}
            <div class="ch-badge" {{ block.shopify_attributes }}>{{ block.settings.text }}</div>

          {%- when 'spacer' -%}
            <div class="ch-spacer" style="height: {{ block.settings.size }}px" aria-hidden="true" {{ block.shopify_attributes }}></div>
        {%- endcase -%}
      {%- endfor -%}
    </div>
  </div>
</section>

{% schema %}
{
  "name": "Custom Hero",
  "settings": [
    {"type": "image_picker", "id": "bg_desktop", "label": "Background image (desktop/tablet)"},
    {"type": "image_picker", "id": "bg_mobile", "label": "Background image (mobile)"},
    {"type": "range", "id": "height_desktop", "label": "Min height (desktop)", "min": 400, "max": 900, "step": 10, "unit": "px", "default": 560},
    {"type": "range", "id": "height_mobile", "label": "Min height (mobile)", "min": 400, "max": 900, "step": 10, "unit": "px", "default": 700},
    {"type": "checkbox", "id": "fit_viewport", "label": "Fit to viewport height (avoid cropping)", "default": false},
    {"type": "range", "id": "viewport_height_desktop", "label": "Viewport height (desktop)", "min": 60, "max": 100, "step": 1, "unit": "vh", "default": 100},
    {"type": "range", "id": "viewport_height_mobile", "label": "Viewport height (mobile)", "min": 60, "max": 100, "step": 1, "unit": "vh", "default": 100},
    {"type": "select", "id": "bg_size", "label": "Background scale", "default": "cover", "options": [
      {"value":"cover","label":"Cover (fill; may crop)"},
      {"value":"contain","label":"Contain (fit; may letterbox)"}
    ]},
    {"type": "select", "id": "bg_position_desktop", "label": "Background position (desktop)", "default": "center right", "options": [
      {"value":"top left","label":"Top left"},
      {"value":"top center","label":"Top center"},
      {"value":"top right","label":"Top right"},
      {"value":"center left","label":"Center left"},
      {"value":"center center","label":"Center"},
      {"value":"center right","label":"Center right"},
      {"value":"bottom left","label":"Bottom left"},
      {"value":"bottom center","label":"Bottom center"},
      {"value":"bottom right","label":"Bottom right"}
    ]},
    {"type": "select", "id": "bg_position_mobile", "label": "Background position (mobile)", "default": "center center", "options": [
      {"value":"top left","label":"Top left"},
      {"value":"top center","label":"Top center"},
      {"value":"top right","label":"Top right"},
      {"value":"center left","label":"Center left"},
      {"value":"center center","label":"Center"},
      {"value":"center right","label":"Center right"},
      {"value":"bottom left","label":"Bottom left"},
      {"value":"bottom center","label":"Bottom center"},
      {"value":"bottom right","label":"Bottom right"}
    ]},
    {"type": "select", "id": "vertical_align", "label": "Content vertical alignment", "default": "center", "options": [
      {"value":"flex-start","label":"Top"},
      {"value":"center","label":"Center"},
      {"value":"flex-end","label":"Bottom"}
    ]},
    {"type": "color", "id": "background_color", "label": "Background color", "default": "#FFF3EC"},
    {"type": "color", "id": "text_color", "label": "Text color", "default": "#2D2A26"},
    {"type": "color", "id": "accent_color", "label": "Accent color (Heading part 2)", "default": "#D96443"},
    {"type": "color", "id": "button_bg", "label": "Button background", "default": "#C3583E"},
    {"type": "color", "id": "button_text_color", "label": "Button text color", "default": "#FFFFFF"},
    {"type": "color", "id": "star_color", "label": "Stars color", "default": "#D36445"},
    {"type": "color", "id": "badge_bg", "label": "Badge background", "default": "#E08566"},
    {"type": "color", "id": "badge_text_color", "label": "Badge text", "default": "#FFFFFF"},
    {"type": "range", "id": "overlay_opacity", "label": "Overlay darken (0–80)", "min": 0, "max": 80, "step": 1, "default": 0}
  ],
  "blocks": [
    {"type": "eyebrow", "name": "Eyebrow", "limit": 1, "settings": [
      {"type":"text","id":"stars","label":"Stars (use ★ characters)","default":"★★★★★"},
      {"type":"text","id":"text","label":"Text","default":"Over 700k+ Customers Worldwide!"},
      {"type":"color","id":"color","label":"Stars color","default":"#D36445"}
    ]},
    {"type": "heading", "name": "Heading", "limit": 1, "settings": [
      {"type":"text","id":"line1","label":"Line 1","default":"Heal Your"},
      {"type":"text","id":"line2","label":"Line 2 (accent)","default":"Body & Mind"}
    ]},
    {"type": "text", "name": "Body text", "limit": 1, "settings": [
      {"type":"richtext","id":"body","label":"Text","default":"<p>RYZE is a mushroom-powered coffee that helps boost energy, improve mental clarity, & supports digestion. Don't miss out on our <strong>SALE</strong> & enjoy free gifts + <strong>25% OFF</strong> today!</p>"}
    ]},
    {"type": "rating", "name": "Rating", "limit": 1, "settings": [
      {"type":"text","id":"text","label":"Text","default":"266,222 Reviews"}
    ]},
    {"type": "button", "name": "Primary button", "limit": 2, "settings": [
      {"type":"text","id":"label","label":"Label","default":"TRY RYZE TODAY"},
      {"type":"url","id":"link","label":"Link"}
    ]},
    {"type": "guarantee", "name": "Guarantee", "limit": 1, "settings": [
      {"type":"text","id":"text","label":"Text","default":"30-Day Money Back Guarantee"}
    ]},
    {"type": "image", "name": "Side image", "limit": 1, "settings": [
      {"type":"image_picker","id":"image","label":"Image"}
    ]},
    {"type": "badge", "name": "Badge", "limit": 1, "settings": [
      {"type":"text","id":"text","label":"Badge text","default":"EARLY\nLABOR DAY\nSALE"}
    ]},
    {"type": "spacer", "name": "Spacer", "settings": [
      {"type":"range","id":"size","label":"Height","min":8,"max":48,"step":4,"unit":"px","default":16}
    ]}
  ],
  "presets": [
    {"name": "Custom Hero", "blocks": [
      {"type":"eyebrow"},
      {"type":"heading"},
      {"type":"text"},
      {"type":"rating"},
      {"type":"button"},
      {"type":"guarantee"},
      {"type":"badge"}
    ]}
  ]
}
{% endschema %}

