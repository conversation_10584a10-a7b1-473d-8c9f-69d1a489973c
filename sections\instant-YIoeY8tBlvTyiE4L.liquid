{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant iYIoeY8tBlvTyiE4L" data-instant-id="YIoeY8tBlvTyiE4L" data-instant-version="3.0.3" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-YIoeY8tBlvTyiE4L.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="iRL7VRyLAqGVZmpUL">
    {%- liquid
      assign loading = 'eager'
      assign fetchpriority = 'auto'
      if section.location == 'footer'
        assign loading = 'lazy'
      elsif section.location == 'header'
        assign fetchpriority = 'high'
      elsif section.location == 'template'
        if section.index == 1
          assign fetchpriority = 'high'
        elsif section.index > 2
          assign loading = 'lazy'
        endif
      endif
    -%}
    <div class="isR3CIqMAbcyyjzmr" data-instant-type="container" id="isR3CIqMAbcyyjzmr">
      <div class="iipgbIt9A8tPGrKjH" data-instant-type="container">
        <div class="igQRKRiuEUJXBPEuu" data-instant-type="container">
          <div class="i7QPyQjsnLHdMjtjJ" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text iU1bpoxShn4YOT9Rw">
              <div>{{ section.settings.text_U1bpoxShn4YOT9Rw }}</div>
            </div>
          </div>
          <div class="idD9GrpJiNG8pVtcc" data-instant-type="container">
            <div class="iMgKutLNVCQPBhOvi" data-instant-type="container">
              <div data-instant-type="text" class="instant-rich-text iRXXysjzRI1Cs8xZh">
                <div>{{ section.settings.text_RXXysjzRI1Cs8xZh }}</div>
              </div>
              <div data-instant-type="text" class="instant-rich-text iLpKGkRk6JTVbHXiI">
                <div>{{ section.settings.text_LpKGkRk6JTVbHXiI }}</div>
              </div>
            </div>
            <div data-instant-type="text" class="instant-rich-text id44xXPI9hpbVY8Wa">
              <div>{{ section.settings.text_d44xXPI9hpbVY8Wa }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="i1b2DuHW0pFIYOcb0" data-instant-type="container">
        <div class="i4eRWg3te31OGXiD6" data-instant-type="container">
          <div class="ika9Ys1LbTrK6SheJ" data-instant-type="container">
            <div data-instant-type="image" class="iGYAr0KlvsYWE7l6v">
              {% if section.settings.image_GYAr0KlvsYWE7l6v and section.settings.image_GYAr0KlvsYWE7l6v != blank %}
                {{ section.settings.image_GYAr0KlvsYWE7l6v | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_GYAr0KlvsYWE7l6v.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/7dARCQGCiJdNQkA9/3e9acb51e06fe80bd393020e2aaace0c0ec19573.svg" width="16" height="15" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
              {% endif %}
            </div>
            <div class="icZbUMwCHFssvs50m" data-instant-type="container"></div>
          </div>
          <div class="ijpK3egdecoJgO1p6" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text iN1bSLrfa1nQQDv6V">
              <div>{{ section.settings.text_N1bSLrfa1nQQDv6V }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text ib28DkTiH1TvmCQ9E">
              <div>{{ section.settings.text_b28DkTiH1TvmCQ9E }}</div>
            </div>
          </div>
        </div>
        <div class="iIxsjSb9QdlFZ9ir3" data-instant-type="container">
          <div class="iihoXvNEBphEGlUa4" data-instant-type="container">
            <div data-instant-type="image" class="i44ZMIWMh8Q5fg2wb">
              {% if section.settings.image_44ZMIWMh8Q5fg2wb and section.settings.image_44ZMIWMh8Q5fg2wb != blank %}
                {{ section.settings.image_44ZMIWMh8Q5fg2wb | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_44ZMIWMh8Q5fg2wb.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/7dARCQGCiJdNQkA9/3e9acb51e06fe80bd393020e2aaace0c0ec19573.svg" width="16" height="15" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
              {% endif %}
            </div>
            <div class="iV7dotjb4OfOLqvDm" data-instant-type="container"></div>
          </div>
          <div class="isVnpDJYXR6kZyAsU" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text ih2MeqDODilkmCmr8">
              <div>{{ section.settings.text_h2MeqDODilkmCmr8 }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text iqKEBLUaeoUnOaae1">
              <div>{{ section.settings.text_qKEBLUaeoUnOaae1 }}</div>
            </div>
          </div>
        </div>
        <div class="i5jkmsFItw1dvYmF7" data-instant-type="container">
          <div class="iKoU2lCvK3RNV5t8S" data-instant-type="container">
            <div data-instant-type="image" class="iNUrnz3uV3ZjjZRmI">
              {% if section.settings.image_NUrnz3uV3ZjjZRmI and section.settings.image_NUrnz3uV3ZjjZRmI != blank %}
                {{ section.settings.image_NUrnz3uV3ZjjZRmI | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_NUrnz3uV3ZjjZRmI.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/CKIQ0SqMezPZxVyf/c4d7316e775d4257d381801303c6d1e1ab3c6b97.svg" width="15" height="15" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
              {% endif %}
            </div>
            <div class="iSOB2IPieoPn6NBTy" data-instant-type="container"></div>
          </div>
          <div class="it3ExjHd6wSOiN8Kb" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text itKYQLQN5YlqWG43D">
              <div>{{ section.settings.text_tKYQLQN5YlqWG43D }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text iUCUV3pRIw8m1hG5W">
              <div>{{ section.settings.text_UCUV3pRIw8m1hG5W }}</div>
            </div>
          </div>
        </div>
        <div class="ikSVZTwcO6lRsHZJ6" data-instant-type="container">
          <div class="iwq21rWk61nRwUiOq" data-instant-type="container">
            <div data-instant-type="image" class="iXABnE29v3t5dqGqZ">
              {% if section.settings.image_XABnE29v3t5dqGqZ and section.settings.image_XABnE29v3t5dqGqZ != blank %}
                {{ section.settings.image_XABnE29v3t5dqGqZ | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_XABnE29v3t5dqGqZ.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/CKIQ0SqMezPZxVyf/c4d7316e775d4257d381801303c6d1e1ab3c6b97.svg" width="15" height="15" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
              {% endif %}
            </div>
            <div class="iCM9ku53CdLf30kAp" data-instant-type="container"></div>
          </div>
          <div class="idZHBIilXEnxpwm17" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text izI3wgLmqrtlB9M8B">
              <div>{{ section.settings.text_zI3wgLmqrtlB9M8B }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text i9xxagRUsjpalsZB3">
              <div>{{ section.settings.text_9xxagRUsjpalsZB3 }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="iA41er49Jf5fXUQCD" data-instant-type="container">
        <div class="iArWmt1lk6Z0b5Byi" data-instant-type="container">
          <div class="i3AqjVXQvP3xyQTSf" data-instant-type="container"></div>
          <div class="ingEWg3yxfRm44aTf" data-instant-type="container"><div class="iXhzV0kuGSl4NYCTn" data-instant-type="container"></div></div>
          <div class="i75DTs1ttWPtvLNzM" data-instant-type="container"></div>
        </div>
        <div class="iSO4wG7eSxbrBsHtD" data-instant-type="container">
          <div class="iEK4PiVF6YM5j8Rzp" data-instant-type="container"></div>
          <div class="ikiKdrcwgFwjhHCN0" data-instant-type="container">
            <div data-instant-type="image" class="iOSdCKiIgmJBORjdy">
              {% if section.settings.image_OSdCKiIgmJBORjdy and section.settings.image_OSdCKiIgmJBORjdy != blank %}
                {{ section.settings.image_OSdCKiIgmJBORjdy | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_OSdCKiIgmJBORjdy.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/CKIQ0SqMezPZxVyf/c4d7316e775d4257d381801303c6d1e1ab3c6b97.svg" width="15" height="15" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
              {% endif %}
            </div>
            <div class="igYv7F06ptHMwNUfE" data-instant-type="container"></div>
          </div>
          <div class="inYWFbZnSbqJO7lmk" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text iSRtBbxBHS7LwSdlQ">
              <div>{{ section.settings.text_SRtBbxBHS7LwSdlQ }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text iYTiaXSAFXm3fCMGo">
              <div>{{ section.settings.text_YTiaXSAFXm3fCMGo }}</div>
            </div>
          </div>
        </div>
        <div class="i0c1BVb5kQSqzt4sq" data-instant-type="container">
          <div class="il8t4LuSROJZqVtMP" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text iR073L2Bdp5eRVifu">
              <div>{{ section.settings.text_R073L2Bdp5eRVifu }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text i4EzpirL54mZnwb3q">
              <div>{{ section.settings.text_4EzpirL54mZnwb3q }}</div>
            </div>
          </div>
          <div class="iG3EhJ1O2pQmFYB2R" data-instant-type="container">
            <div data-instant-type="image" class="icmVsTxnRvQGTSZZ5">
              {% if section.settings.image_cmVsTxnRvQGTSZZ5 and section.settings.image_cmVsTxnRvQGTSZZ5 != blank %}
                {{ section.settings.image_cmVsTxnRvQGTSZZ5 | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_cmVsTxnRvQGTSZZ5.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/CKIQ0SqMezPZxVyf/c4d7316e775d4257d381801303c6d1e1ab3c6b97.svg" width="15" height="15" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
              {% endif %}
            </div>
            <div class="i76ZwAbMElulNVukD" data-instant-type="container"></div>
          </div>
          <div class="i34pr6hLYLnQx5B5L" data-instant-type="container"></div>
        </div>
        <div class="ioLrfkFdf54iVIcZY" data-instant-type="container">
          <div class="i5fHMFMCPcKPlUkT2" data-instant-type="container"></div>
          <div class="ikWxfnpJFosYEJ3jQ" data-instant-type="container">
            <div data-instant-type="image" class="ig0wIKKVDAEfDh5TU">
              {% if section.settings.image_g0wIKKVDAEfDh5TU and section.settings.image_g0wIKKVDAEfDh5TU != blank %}
                {{ section.settings.image_g0wIKKVDAEfDh5TU | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_g0wIKKVDAEfDh5TU.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/CKIQ0SqMezPZxVyf/c4d7316e775d4257d381801303c6d1e1ab3c6b97.svg" width="15" height="15" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
              {% endif %}
            </div>
            <div class="iTG8RgA91tbMAeg9J" data-instant-type="container"></div>
          </div>
          <div class="i81Za8Y6YV9Ri2dXr" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text i3NA8ffa5KmrmIAU2">
              <div>{{ section.settings.text_3NA8ffa5KmrmIAU2 }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text ippuFijyGKLl6Wwtl">
              <div>{{ section.settings.text_ppuFijyGKLl6Wwtl }}</div>
            </div>
          </div>
        </div>
        <div class="iVf6ldswPVZunAcEu" data-instant-type="container">
          <div class="iXQLL6I05009bTRCn" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text iX9oFzeO4p3kg5Mkv">
              <div>{{ section.settings.text_X9oFzeO4p3kg5Mkv }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text iwbhXBEzWAT9CCDCI">
              <div>{{ section.settings.text_wbhXBEzWAT9CCDCI }}</div>
            </div>
          </div>
          <div class="iQl3tZaTChlyxuCmL" data-instant-type="container">
            <div data-instant-type="image" class="imXflgXkxwzVRvpbI">
              {% if section.settings.image_mXflgXkxwzVRvpbI and section.settings.image_mXflgXkxwzVRvpbI != blank %}
                {{ section.settings.image_mXflgXkxwzVRvpbI | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_mXflgXkxwzVRvpbI.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/CKIQ0SqMezPZxVyf/c4d7316e775d4257d381801303c6d1e1ab3c6b97.svg" width="15" height="15" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
              {% endif %}
            </div>
            <div class="iCcYeu9KefeuNDmgS" data-instant-type="container"></div>
          </div>
          <div class="i6jMFEtkdZOm5QGuX" data-instant-type="container"></div>
        </div>
      </div>
    </div>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.3",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison TImeline",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "richtext",
      "id": "text_U1bpoxShn4YOT9Rw",
      "label": "Tagline",
      "default": "<p>HOW IT WILL FEEL</p>"
    },
    {
      "type": "richtext",
      "id": "text_RXXysjzRI1Cs8xZh",
      "label": "Heading",
      "default": "<p>Feel instant relief &amp; improve your feet</p>"
    },
    {
      "type": "richtext",
      "id": "text_LpKGkRk6JTVbHXiI",
      "label": "Heading",
      "default": "<p>alignment over time</p>"
    },
    {
      "type": "richtext",
      "id": "text_d44xXPI9hpbVY8Wa",
      "label": "Text",
      "default": "<p>Here’s how your feet will feel over time when wearing the HK Cloud on a daily basis.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_GYAr0KlvsYWE7l6v",
      "label": "Circle"
    },
    {
      "type": "richtext",
      "id": "text_N1bSLrfa1nQQDv6V",
      "label": "Date",
      "default": "<p>1st Days:</p><p>Instant Relief</p>"
    },
    {
      "type": "richtext",
      "id": "text_b28DkTiH1TvmCQ9E",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_44ZMIWMh8Q5fg2wb",
      "label": "Circle"
    },
    {
      "type": "richtext",
      "id": "text_h2MeqDODilkmCmr8",
      "label": "Date",
      "default": "<p>After a week: </p><p>Less pain after a long day</p>"
    },
    {
      "type": "richtext",
      "id": "text_qKEBLUaeoUnOaae1",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_NUrnz3uV3ZjjZRmI",
      "label": "Circle"
    },
    {
      "type": "richtext",
      "id": "text_tKYQLQN5YlqWG43D",
      "label": "Date",
      "default": "<p>After 2 weeks: </p><p>Feet get used + better aligned</p>"
    },
    {
      "type": "richtext",
      "id": "text_UCUV3pRIw8m1hG5W",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_XABnE29v3t5dqGqZ",
      "label": "Circle"
    },
    {
      "type": "richtext",
      "id": "text_zI3wgLmqrtlB9M8B",
      "label": "Date",
      "default": "<p>After 1 month: </p><p>Lasting comfort. </p>"
    },
    {
      "type": "richtext",
      "id": "text_9xxagRUsjpalsZB3",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_OSdCKiIgmJBORjdy",
      "label": "Circle"
    },
    {
      "type": "richtext",
      "id": "text_SRtBbxBHS7LwSdlQ",
      "label": "Date",
      "default": "<p>First hours:</p><p>Instant Relief</p>"
    },
    {
      "type": "richtext",
      "id": "text_YTiaXSAFXm3fCMGo",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristi que.</p>"
    },
    {
      "type": "richtext",
      "id": "text_R073L2Bdp5eRVifu",
      "label": "Date",
      "default": "<p>After 2 days: </p><p>Less pain after a long day</p>"
    },
    {
      "type": "richtext",
      "id": "text_4EzpirL54mZnwb3q",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_cmVsTxnRvQGTSZZ5",
      "label": "Circle"
    },
    {
      "type": "image_picker",
      "id": "image_g0wIKKVDAEfDh5TU",
      "label": "Circle"
    },
    {
      "type": "richtext",
      "id": "text_3NA8ffa5KmrmIAU2",
      "label": "Date",
      "default": "<p>After the first week: </p><p>Feet get used + better aligned</p>"
    },
    {
      "type": "richtext",
      "id": "text_ppuFijyGKLl6Wwtl",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>"
    },
    {
      "type": "richtext",
      "id": "text_X9oFzeO4p3kg5Mkv",
      "label": "Date",
      "default": "<p>After 1 month: </p><p>Lasting comfort. </p>"
    },
    {
      "type": "richtext",
      "id": "text_wbhXBEzWAT9CCDCI",
      "label": "Text",
      "default": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_mXflgXkxwzVRvpbI",
      "label": "Circle"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison TImeline",
      "settings": {
        "text_U1bpoxShn4YOT9Rw": "<p>HOW IT WILL FEEL</p>",
        "text_RXXysjzRI1Cs8xZh": "<p>Feel instant relief &amp; improve your feet</p>",
        "text_LpKGkRk6JTVbHXiI": "<p>alignment over time</p>",
        "text_d44xXPI9hpbVY8Wa": "<p>Here’s how your feet will feel over time when wearing the HK Cloud on a daily basis.</p>",
        "text_N1bSLrfa1nQQDv6V": "<p>1st Days:</p><p>Instant Relief</p>",
        "text_b28DkTiH1TvmCQ9E": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>",
        "text_h2MeqDODilkmCmr8": "<p>After a week: </p><p>Less pain after a long day</p>",
        "text_qKEBLUaeoUnOaae1": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>",
        "text_tKYQLQN5YlqWG43D": "<p>After 2 weeks: </p><p>Feet get used + better aligned</p>",
        "text_UCUV3pRIw8m1hG5W": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>",
        "text_zI3wgLmqrtlB9M8B": "<p>After 1 month: </p><p>Lasting comfort. </p>",
        "text_9xxagRUsjpalsZB3": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>",
        "text_SRtBbxBHS7LwSdlQ": "<p>First hours:</p><p>Instant Relief</p>",
        "text_YTiaXSAFXm3fCMGo": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristi que.</p>",
        "text_R073L2Bdp5eRVifu": "<p>After 2 days: </p><p>Less pain after a long day</p>",
        "text_4EzpirL54mZnwb3q": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>",
        "text_3NA8ffa5KmrmIAU2": "<p>After the first week: </p><p>Feet get used + better aligned</p>",
        "text_ppuFijyGKLl6Wwtl": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>",
        "text_X9oFzeO4p3kg5Mkv": "<p>After 1 month: </p><p>Lasting comfort. </p>",
        "text_wbhXBEzWAT9CCDCI": "<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse varius enim in eros elementum tristique.</p>"
      }
    }
  ]
}
{% endschema %}
