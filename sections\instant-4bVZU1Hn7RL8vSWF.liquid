{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant i4bVZU1Hn7RL8vSWF" data-instant-id="4bVZU1Hn7RL8vSWF" data-instant-version="3.0.3" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-4bVZU1Hn7RL8vSWF.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="i4TxQet0dCgRecC80">
    {%- liquid
      assign loading = 'eager'
      assign fetchpriority = 'auto'
      if section.location == 'footer'
        assign loading = 'lazy'
      elsif section.location == 'header'
        assign fetchpriority = 'high'
      elsif section.location == 'template'
        if section.index == 1
          assign fetchpriority = 'high'
        elsif section.index > 2
          assign loading = 'lazy'
        endif
      endif
    -%}
    <div class="idSu26YNIuD0w57L2" data-instant-type="container" id="idSu26YNIuD0w57L2">
      <div class="iRtcBo0VEEZzHipzz" data-instant-type="container">
        <div class="iXczRlOpBFcFsXTuI" data-instant-type="container">
          <div data-instant-type="text" class="instant-rich-text iGqIKptfEeQVQNfJl">
            <div>{{ section.settings.text_GqIKptfEeQVQNfJl }}</div>
          </div>
        </div>
        <div class="ifhaIEnFP6weNxWHa" data-instant-type="container">
          <div class="iShGcJu2BJr6nMeeo" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text iWmzOiZba7bhQGrqC">
              <div>{{ section.settings.text_WmzOiZba7bhQGrqC }}</div>
            </div>
            <div data-instant-type="text" class="instant-rich-text iUFIUWhHDNJFIS9au">
              <div>{{ section.settings.text_UFIUWhHDNJFIS9au }}</div>
            </div>
          </div>
          <div data-instant-type="text" class="instant-rich-text ifr3TC6XS0jjgqfXH">
            <div>{{ section.settings.text_fr3TC6XS0jjgqfXH }}</div>
          </div>
        </div>
      </div>
      <div class="iXZD6N3zFIZkcxdDr" data-instant-type="container">
        <div class="ivP6LyaAEMD5iyHW9" data-instant-type="container">
          <div class="iJTZJtbeua5nGiyi8" data-instant-type="container">
            <div class="ixtUGb1QduUvxrt33" data-instant-type="container">
              <div class="ieHbs80KtP1TBFPvX" data-instant-type="container">
                <div class="iMVKaclcV0Ne3oCVy" data-instant-type="container"></div>
                <div class="ijAxg9HwCvnIII0uO" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iwqGRRTwrR6oqbF38">
                    <div>{{ section.settings.text_wqGRRTwrR6oqbF38 }}</div>
                  </div>
                </div>
                <div class="iDlR3mDBj27INb2e8" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iPZU8Nnq2WLmMzxrE">
                    <div>{{ section.settings.text_PZU8Nnq2WLmMzxrE }}</div>
                  </div>
                </div>
                <div class="imRdo84eYYbBDxFm2" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text ipIAjkkCpmYNC25h5">
                    <div>{{ section.settings.text_pIAjkkCpmYNC25h5 }}</div>
                  </div>
                </div>
                <div class="ibjfoXp44AOE8z3wI" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text i3CaZmqZ6km7GCIny">
                    <div>{{ section.settings.text_3CaZmqZ6km7GCIny }}</div>
                  </div>
                </div>
                <div class="i3ZlhiAW1cWIp8i7Q" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iXbRsH0NPVxeuNIJP">
                    <div>{{ section.settings.text_XbRsH0NPVxeuNIJP }}</div>
                  </div>
                </div>
                <div class="iG2k7lgO1BAHvhYxl" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iNumkH5BTsYHo6nTa">
                    <div>{{ section.settings.text_NumkH5BTsYHo6nTa }}</div>
                  </div>
                </div>
                <div class="irTYrlM7BgTaDoPMN" data-instant-type="container"></div>
              </div>
              <div class="it5ObrRLzprcCQ9ri" data-instant-type="container">
                <div class="iOsP9gwnS1I47Yyc2" data-instant-type="container">
                  <form class="iMyEg9OiARNT0MphS" data-instant-type="container" data-instant-form-product-url="/products/plain-vinyl">
                    <div data-instant-type="image" conditionals="" class="iVzFQjk0i9ir3Pofn">
                      {% if section.settings.image_VzFQjk0i9ir3Pofn and section.settings.image_VzFQjk0i9ir3Pofn != blank %}
                        {{ section.settings.image_VzFQjk0i9ir3Pofn | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_VzFQjk0i9ir3Pofn.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                      {% else %}
                        <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/kNRF4ddiLCsBD5qb/group-14.png" width="330" height="252" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                      {% endif %}
                    </div>
                  </form>
                  <div class="ie8cSIsTKwVXULSFD" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iVPcuFBXk3NkNNRKZ">
                      <div>{{ section.settings.text_VPcuFBXk3NkNNRKZ }}</div>
                    </div>
                  </div>
                  <div class="i503qHRmEVXm0jeP5" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iMlzjoF06EemrHvf0">
                      <div>{{ section.settings.text_MlzjoF06EemrHvf0 }}</div>
                    </div>
                  </div>
                  <div class="iSXEAsqC1wt05pCTX" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iZGvVmOBATGj5VCeT">
                      <div>{{ section.settings.text_ZGvVmOBATGj5VCeT }}</div>
                    </div>
                  </div>
                  <div class="iNGdZboNHrrx3CPD5" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text i85nekQgnWIS9fSPn">
                      <div>{{ section.settings.text_85nekQgnWIS9fSPn }}</div>
                    </div>
                  </div>
                  <div class="iHJmgixJhxp3Q75zG" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iUi76cjVPHgQAWOw8">
                      <div>{{ section.settings.text_Ui76cjVPHgQAWOw8 }}</div>
                    </div>
                  </div>
                  <div class="i6tXXJxLvXL9cB9gT" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iXpjtZPmHC4SkHrKD">
                      <div>{{ section.settings.text_XpjtZPmHC4SkHrKD }}</div>
                    </div>
                  </div>
                  <div class="ixtukEFjGJXoLWs2E" data-instant-type="container"></div>
                </div>
                <div class="iUx1nSUPjv7bkD0Oc" data-instant-type="container">
                  <div class="iOvTUa4YQdHih7HBt" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text itUn1wmagb5D2Uf7l">
                      <div>{{ section.settings.text_tUn1wmagb5D2Uf7l }}</div>
                    </div>
                  </div>
                  <div class="izmlGmDXCF2zyLm7z" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text irRlQivwq1SZ4fvxU">
                      <div>{{ section.settings.text_rRlQivwq1SZ4fvxU }}</div>
                    </div>
                  </div>
                  <div class="iy8HLtnnqxS0oBivP" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iMBrATEF3MMXHUPdr">
                      <div>{{ section.settings.text_MBrATEF3MMXHUPdr }}</div>
                    </div>
                  </div>
                  <div class="ich5tU5FczzhDzPg3" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iNLtU0ZiHSTz23FpM">
                      <div>{{ section.settings.text_NLtU0ZiHSTz23FpM }}</div>
                    </div>
                  </div>
                  <div class="iYpmLftESeHx539wn" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text ixuXjBgc0XAWSJNxA">
                      <div>{{ section.settings.text_xuXjBgc0XAWSJNxA }}</div>
                    </div>
                  </div>
                  <div class="ilF3G0OM0cyjX4YC9" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iELdOLPHM9iPoVU2t">
                      <div>{{ section.settings.text_ELdOLPHM9iPoVU2t }}</div>
                    </div>
                  </div>
                  <div class="iRgyjJfcUaA9N2ICk" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text izVQHOEWKvJhJCeuB">
                      <div>{{ section.settings.text_zVQHOEWKvJhJCeuB }}</div>
                    </div>
                  </div>
                  <div class="ispkrOeDLDGBhJg4D" data-instant-type="container"></div>
                </div>
                <div class="iDIfXCkeYob3Xkujv" data-instant-type="container">
                  <div class="ihTPNLnUzooejWNmP" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text i9V6jL0jaURLDaxOf">
                      <div>{{ section.settings.text_9V6jL0jaURLDaxOf }}</div>
                    </div>
                  </div>
                  <div class="iFHsETdWwLENwJWvh" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iFNMjnzc0oKpHdQD9">
                      <div>{{ section.settings.text_FNMjnzc0oKpHdQD9 }}</div>
                    </div>
                  </div>
                  <div class="iZKcssF06CfnrbeWr" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text ilnKkgpdloNmh3wpG">
                      <div>{{ section.settings.text_lnKkgpdloNmh3wpG }}</div>
                    </div>
                  </div>
                  <div class="iZDdaYYotH2WpWj6M" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iQ5ECcLrQ9WSCCzE9">
                      <div>{{ section.settings.text_Q5ECcLrQ9WSCCzE9 }}</div>
                    </div>
                  </div>
                  <div class="ibyt5SxzG33uyKFzZ" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text i6nf4OfkTRwCtJ3zu">
                      <div>{{ section.settings.text_6nf4OfkTRwCtJ3zu }}</div>
                    </div>
                  </div>
                  <div class="iRAHJsVRmhX4IO0jX" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iNpCjnjlnF82hCGAg">
                      <div>{{ section.settings.text_NpCjnjlnF82hCGAg }}</div>
                    </div>
                  </div>
                  <div class="iTVY8gRr8bTd8QyaS" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iBOJXlNtBCDEQ1NiC">
                      <div>{{ section.settings.text_BOJXlNtBCDEQ1NiC }}</div>
                    </div>
                  </div>
                  <div class="i2PCdZb9fF6IKcKLD" data-instant-type="container"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.3",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison Comparison Table",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "richtext",
      "id": "text_GqIKptfEeQVQNfJl",
      "label": "Tagline",
      "default": "<p>COMPARISON</p>"
    },
    {
      "type": "richtext",
      "id": "text_WmzOiZba7bhQGrqC",
      "label": "Heading",
      "default": "<p>Normal shoes are designed for style,</p>"
    },
    {
      "type": "richtext",
      "id": "text_UFIUWhHDNJFIS9au",
      "label": "Heading",
      "default": "<p>not for comfort &amp; ultimate support</p>"
    },
    {
      "type": "richtext",
      "id": "text_fr3TC6XS0jjgqfXH",
      "label": "Text",
      "default": "<p>See how we compare to normal shoes</p>"
    },
    {
      "type": "richtext",
      "id": "text_wqGRRTwrR6oqbF38",
      "label": "Logo",
      "default": "<p> Comfort</p>"
    },
    {
      "type": "richtext",
      "id": "text_PZU8Nnq2WLmMzxrE",
      "label": "Logo",
      "default": "<p>Alignment</p>"
    },
    {
      "type": "richtext",
      "id": "text_pIAjkkCpmYNC25h5",
      "label": "Logo",
      "default": "<p>Toe Space</p>"
    },
    {
      "type": "richtext",
      "id": "text_3CaZmqZ6km7GCIny",
      "label": "Logo",
      "default": "<p>Style</p>"
    },
    {
      "type": "richtext",
      "id": "text_XbRsH0NPVxeuNIJP",
      "label": "Logo",
      "default": "<p>Weight</p>"
    },
    {
      "type": "richtext",
      "id": "text_NumkH5BTsYHo6nTa",
      "label": "Logo",
      "default": "<p>Breathability</p>"
    },
    {
      "type": "image_picker",
      "id": "image_VzFQjk0i9ir3Pofn",
      "label": "Image"
    },
    {
      "type": "richtext",
      "id": "text_VPcuFBXk3NkNNRKZ",
      "label": "Logo",
      "default": "<p>Cloud-like cushioning</p>"
    },
    {
      "type": "richtext",
      "id": "text_MlzjoF06EemrHvf0",
      "label": "Logo",
      "default": "<p>Promotes natural posture</p>"
    },
    {
      "type": "richtext",
      "id": "text_ZGvVmOBATGj5VCeT",
      "label": "Logo",
      "default": "<p>Wide, natural toe box</p>"
    },
    {
      "type": "richtext",
      "id": "text_85nekQgnWIS9fSPn",
      "label": "Logo",
      "default": "<p>Modern, versatile designs</p>"
    },
    {
      "type": "richtext",
      "id": "text_Ui76cjVPHgQAWOw8",
      "label": "Logo",
      "default": "<p>Ultra-lightweight</p>"
    },
    {
      "type": "richtext",
      "id": "text_XpjtZPmHC4SkHrKD",
      "label": "Logo",
      "default": "<p>High, moisture-wicking</p>"
    },
    {
      "type": "richtext",
      "id": "text_tUn1wmagb5D2Uf7l",
      "label": "Logo",
      "default": "<p>Traditional Shoes</p>"
    },
    {
      "type": "richtext",
      "id": "text_rRlQivwq1SZ4fvxU",
      "label": "Logo",
      "default": "<p>Varies, often minimal</p>"
    },
    {
      "type": "richtext",
      "id": "text_MBrATEF3MMXHUPdr",
      "label": "Logo",
      "default": "<p>Little alignment support</p>"
    },
    {
      "type": "richtext",
      "id": "text_NLtU0ZiHSTz23FpM",
      "label": "Logo",
      "default": "<p>Often narrow, constraining</p>"
    },
    {
      "type": "richtext",
      "id": "text_xuXjBgc0XAWSJNxA",
      "label": "Logo",
      "default": "<p>Wide variety</p>"
    },
    {
      "type": "richtext",
      "id": "text_ELdOLPHM9iPoVU2t",
      "label": "Logo",
      "default": "<p>Varies</p>"
    },
    {
      "type": "richtext",
      "id": "text_zVQHOEWKvJhJCeuB",
      "label": "Logo",
      "default": "<p>Varies</p>"
    },
    {
      "type": "richtext",
      "id": "text_9V6jL0jaURLDaxOf",
      "label": "Logo",
      "default": "<p>Orthopedic Shoes</p>"
    },
    {
      "type": "richtext",
      "id": "text_FNMjnzc0oKpHdQD9",
      "label": "Logo",
      "default": "<p>Rigid support</p>"
    },
    {
      "type": "richtext",
      "id": "text_lnKkgpdloNmh3wpG",
      "label": "Logo",
      "default": "<p>Corrective, can be restrictive</p>"
    },
    {
      "type": "richtext",
      "id": "text_Q5ECcLrQ9WSCCzE9",
      "label": "Logo",
      "default": "<p>Variable, often wide but boxy</p>"
    },
    {
      "type": "richtext",
      "id": "text_6nf4OfkTRwCtJ3zu",
      "label": "Logo",
      "default": "<p>Often bulky, medical-looking</p>"
    },
    {
      "type": "richtext",
      "id": "text_NpCjnjlnF82hCGAg",
      "label": "Logo",
      "default": "<p>Usually heavy</p>"
    },
    {
      "type": "richtext",
      "id": "text_BOJXlNtBCDEQ1NiC",
      "label": "Logo",
      "default": "<p>Often limited</p>"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison Comparison Table",
      "settings": {
        "text_GqIKptfEeQVQNfJl": "<p>COMPARISON</p>",
        "text_WmzOiZba7bhQGrqC": "<p>Normal shoes are designed for style,</p>",
        "text_UFIUWhHDNJFIS9au": "<p>not for comfort &amp; ultimate support</p>",
        "text_fr3TC6XS0jjgqfXH": "<p>See how we compare to normal shoes</p>",
        "text_wqGRRTwrR6oqbF38": "<p> Comfort</p>",
        "text_PZU8Nnq2WLmMzxrE": "<p>Alignment</p>",
        "text_pIAjkkCpmYNC25h5": "<p>Toe Space</p>",
        "text_3CaZmqZ6km7GCIny": "<p>Style</p>",
        "text_XbRsH0NPVxeuNIJP": "<p>Weight</p>",
        "text_NumkH5BTsYHo6nTa": "<p>Breathability</p>",
        "text_VPcuFBXk3NkNNRKZ": "<p>Cloud-like cushioning</p>",
        "text_MlzjoF06EemrHvf0": "<p>Promotes natural posture</p>",
        "text_ZGvVmOBATGj5VCeT": "<p>Wide, natural toe box</p>",
        "text_85nekQgnWIS9fSPn": "<p>Modern, versatile designs</p>",
        "text_Ui76cjVPHgQAWOw8": "<p>Ultra-lightweight</p>",
        "text_XpjtZPmHC4SkHrKD": "<p>High, moisture-wicking</p>",
        "text_tUn1wmagb5D2Uf7l": "<p>Traditional Shoes</p>",
        "text_rRlQivwq1SZ4fvxU": "<p>Varies, often minimal</p>",
        "text_MBrATEF3MMXHUPdr": "<p>Little alignment support</p>",
        "text_NLtU0ZiHSTz23FpM": "<p>Often narrow, constraining</p>",
        "text_xuXjBgc0XAWSJNxA": "<p>Wide variety</p>",
        "text_ELdOLPHM9iPoVU2t": "<p>Varies</p>",
        "text_zVQHOEWKvJhJCeuB": "<p>Varies</p>",
        "text_9V6jL0jaURLDaxOf": "<p>Orthopedic Shoes</p>",
        "text_FNMjnzc0oKpHdQD9": "<p>Rigid support</p>",
        "text_lnKkgpdloNmh3wpG": "<p>Corrective, can be restrictive</p>",
        "text_Q5ECcLrQ9WSCCzE9": "<p>Variable, often wide but boxy</p>",
        "text_6nf4OfkTRwCtJ3zu": "<p>Often bulky, medical-looking</p>",
        "text_NpCjnjlnF82hCGAg": "<p>Usually heavy</p>",
        "text_BOJXlNtBCDEQ1NiC": "<p>Often limited</p>"
      }
    }
  ]
}
{% endschema %}
