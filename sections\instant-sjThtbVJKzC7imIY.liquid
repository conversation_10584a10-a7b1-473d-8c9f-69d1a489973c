{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant isjThtbVJKzC7imIY" data-instant-id="sjThtbVJKzC7imIY" data-instant-version="3.0.4" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-sjThtbVJKzC7imIY.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="ijrQJf4Jp1DuPlqEm">
    <div class="iqMcdP1E0EYsHhaUl" data-instant-type="container" id="iqMcdP1E0EYsHhaUl">
      <div class="i5mxpCD8AI0oRptJ4" data-instant-type="container">
        <div data-instant-type="text" class="instant-rich-text iyhKWRBcBFYbAHeQH">
          <div>{{ section.settings['text_yhKWRBcBFYbAHeQH-1'] }}</div>
        </div>
      </div>
      <div class="iDSUQmCf9Yx6LDAr4" data-instant-type="container">
        <div class="ipK5dJodQqj4d24uM" data-instant-type="container">
          <div data-instant-type="text" class="instant-rich-text imgwn1B5QXONmg1Ao">
            <div>{{ section.settings['text_mgwn1B5QXONmg1Ao-1'] }}</div>
          </div>
          <div data-instant-type="text" class="instant-rich-text igaAT0QeTHEtmZA7I">
            <div>{{ section.settings['text_gaAT0QeTHEtmZA7I-1'] }}</div>
          </div>
        </div>
        <div data-instant-type="text" class="instant-rich-text iYuweWWSPuzTZKC1a">
          <div>{{ section.settings['text_YuweWWSPuzTZKC1a-1'] }}</div>
        </div>
      </div>
    </div>
    {%- liquid
      assign loading = 'eager'
      assign fetchpriority = 'auto'
      if section.location == 'footer'
        assign loading = 'lazy'
      elsif section.location == 'header'
        assign fetchpriority = 'high'
      elsif section.location == 'template'
        if section.index == 1
          assign fetchpriority = 'high'
        elsif section.index > 2
          assign loading = 'lazy'
        endif
      endif
    -%}
    <div class="iFTXGzTMevZrrC0x5" data-instant-type="container" id="iFTXGzTMevZrrC0x5">
      <div class="iHeDViOK1xG6XuHzu" data-instant-type="container">
        <div class="iutovM9oT6fT3SRVI" data-instant-type="container">
          <div class="iaRj1tuJbeVMD3mCh" data-instant-type="container">
            <div class="imAUW2umxrvdUEwvb instant-scroll-trigger instant-scroll-trigger--hidden instant-scroll--transform-fade" data-instant-scroll-into-view-replay="false" data-instant-scroll-into-view-offset="10" data-instant-type="container">
              <div class="iLSWdqWNwC5Ll4HBP" data-instant-type="container">
                <div class="iZ5VYVEmAVuDnxLex" data-instant-type="container">
                  {% if section.settings['image_Z5VYVEmAVuDnxLex-1'] and section.settings['image_Z5VYVEmAVuDnxLex-1'] != blank %}
                    {{ section.settings['image_Z5VYVEmAVuDnxLex-1'] | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings['image_Z5VYVEmAVuDnxLex-1'].alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/XmBDM7b6q7fl8wG2/9ea6162b8eeeca40a99bdabee33fe87bce88c8b1.svg" width="48" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
              </div>
              <div data-instant-type="text" class="instant-rich-text iUCXwBVOYJu4mRxXg">
                <div>{{ section.settings['text_UCXwBVOYJu4mRxXg-1'] }}</div>
              </div>
              <div data-instant-type="text" class="instant-rich-text iBxt3CCbpHxh1jLhK">
                <div>{{ section.settings['text_Bxt3CCbpHxh1jLhK-1'] }}</div>
              </div>
            </div>
            <div class="i1GXrtv5TWM5ZhXJG instant-scroll-trigger instant-scroll-trigger--hidden instant-scroll--transform-fade" data-instant-scroll-into-view-replay="false" data-instant-scroll-into-view-offset="10" data-instant-type="container">
              <div class="iYuU6liPj5cyQMm9w" data-instant-type="container">
                <div class="iIgdOtO7LgT2Amq2d" data-instant-type="container">
                  {% if section.settings['image_IgdOtO7LgT2Amq2d-1'] and section.settings['image_IgdOtO7LgT2Amq2d-1'] != blank %}
                    {{ section.settings['image_IgdOtO7LgT2Amq2d-1'] | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings['image_IgdOtO7LgT2Amq2d-1'].alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/xKPRji1zJITLVUiy/f0a240a4202affa37b37c707f04d0b81a4d7c8d3.svg" width="48" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
              </div>
              <div data-instant-type="text" class="instant-rich-text icYxK6O4ztIYmsnYl">
                <div>{{ section.settings['text_cYxK6O4ztIYmsnYl-1'] }}</div>
              </div>
              <div data-instant-type="text" class="instant-rich-text iFbh7kdkT1y5RXxYE">
                <div>{{ section.settings['text_Fbh7kdkT1y5RXxYE-1'] }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="iDeMlBcDjOaLHQ4bs" data-instant-type="container">
          <div data-instant-type="image" class="iBRE7xKuTSREbNeRn">
            {% if section.settings['image_BRE7xKuTSREbNeRn-1'] and section.settings['image_BRE7xKuTSREbNeRn-1'] != blank %}
              {{ section.settings['image_BRE7xKuTSREbNeRn-1'] | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings['image_BRE7xKuTSREbNeRn-1'].alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
            {% else %}
              <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/6GXamVYZ1x983Vfq/pp-hf-cloud-11.png" width="2000" height="2000" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
            {% endif %}
          </div>
          <div class="ie1T9OvU6qbWWp5ei" data-instant-type="container">
            <div class="i31Ih4wZIa6niNfyu" data-instant-type="container">
              <div class="ij4r1nfgJztl1LayD" data-instant-type="container">
                <div class="iFEtuzu4xOrvmyMLY" data-instant-type="container">
                  {% if section.settings['image_FEtuzu4xOrvmyMLY-1'] and section.settings['image_FEtuzu4xOrvmyMLY-1'] != blank %}
                    {{ section.settings['image_FEtuzu4xOrvmyMLY-1'] | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings['image_FEtuzu4xOrvmyMLY-1'].alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/XmBDM7b6q7fl8wG2/9ea6162b8eeeca40a99bdabee33fe87bce88c8b1.svg" width="48" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
              </div>
              <div class="iRhUity9zwDii0BN5" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text iEpEulnjZsNV0aG2U">
                  <div>{{ section.settings['text_EpEulnjZsNV0aG2U-1'] }}</div>
                </div>
                <div data-instant-type="text" class="instant-rich-text iSbuWZC7ULMS2c62X">
                  <div>{{ section.settings['text_SbuWZC7ULMS2c62X-1'] }}</div>
                </div>
              </div>
            </div>
            <div class="iVOIN9WxTmO7GSclw" data-instant-type="container">
              <div class="iQ7dsVsWm4LA9dtHM" data-instant-type="container">
                <div class="iMn1qofUAGNYeqXDl" data-instant-type="container">
                  {% if section.settings['image_Mn1qofUAGNYeqXDl-1'] and section.settings['image_Mn1qofUAGNYeqXDl-1'] != blank %}
                    {{ section.settings['image_Mn1qofUAGNYeqXDl-1'] | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings['image_Mn1qofUAGNYeqXDl-1'].alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/xKPRji1zJITLVUiy/f0a240a4202affa37b37c707f04d0b81a4d7c8d3.svg" width="48" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
              </div>
              <div class="irWpibigtkqPoYpZM" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text iErGppDKH1o4RSj7t">
                  <div>{{ section.settings['text_ErGppDKH1o4RSj7t-1'] }}</div>
                </div>
                <div data-instant-type="text" class="instant-rich-text ic8oE9pf0SND3rPZR">
                  <div>{{ section.settings['text_c8oE9pf0SND3rPZR-1'] }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="iErmZYc4iUYdh5GGK" data-instant-type="container">
          <div class="ix1r9oGo8LQAiURTm instant-scroll-trigger instant-scroll-trigger--hidden instant-scroll--transform-fade" data-instant-scroll-into-view-replay="false" data-instant-scroll-into-view-offset="10" data-instant-type="container">
            <div class="iUogW1CMZEWttKVe6" data-instant-type="container">
              <div class="ir90MqKlq56bEdPKR" data-instant-type="container">
                {% if section.settings['image_r90MqKlq56bEdPKR-1'] and section.settings['image_r90MqKlq56bEdPKR-1'] != blank %}
                  {{ section.settings['image_r90MqKlq56bEdPKR-1'] | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings['image_r90MqKlq56bEdPKR-1'].alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                {% else %}
                  <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/HYJKB737RuslDhpA/263b47fac2b6b28c441eb97dc057ede586d7f3f9.svg" width="48" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                {% endif %}
              </div>
            </div>
            <div class="iTAjt5rpQ26GmU0xY" data-instant-type="container">
              <div data-instant-type="text" class="instant-rich-text ii4b0jzp2LmCCDcYn">
                <div>{{ section.settings['text_i4b0jzp2LmCCDcYn-1'] }}</div>
              </div>
              <div data-instant-type="text" class="instant-rich-text ibHZtoIzyN0kuDyTo">
                <div>{{ section.settings['text_bHZtoIzyN0kuDyTo-1'] }}</div>
              </div>
            </div>
          </div>
          <div class="iwwNkqVs8rvmdi9QN instant-scroll-trigger instant-scroll-trigger--hidden instant-scroll--transform-fade" data-instant-scroll-into-view-replay="false" data-instant-scroll-into-view-offset="10" data-instant-type="container">
            <div class="iAXj86xHrH0MaMtK3" data-instant-type="container">
              <div class="ivnlsEIBEBoCnp6VH" data-instant-type="container">
                {% if section.settings['image_vnlsEIBEBoCnp6VH-1'] and section.settings['image_vnlsEIBEBoCnp6VH-1'] != blank %}
                  {{ section.settings['image_vnlsEIBEBoCnp6VH-1'] | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings['image_vnlsEIBEBoCnp6VH-1'].alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                {% else %}
                  <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/rcln80v9grEqsK1e/24cf38f0f6a66e68d7a3e34ec6350e2a7cd06eb0.svg" width="48" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                {% endif %}
              </div>
            </div>
            <div class="iWXJ6X2BbyHneuOX7" data-instant-type="container">
              <div data-instant-type="text" class="instant-rich-text i6gfoRLrR1tofqixl">
                <div>{{ section.settings['text_6gfoRLrR1tofqixl-1'] }}</div>
              </div>
              <div data-instant-type="text" class="instant-rich-text iIvUGztKzgUCp1x2U">
                <div>{{ section.settings['text_IvUGztKzgUCp1x2U-1'] }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.4",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison Image With Feature",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "richtext",
      "id": "text_yhKWRBcBFYbAHeQH-1",
      "label": "Tagline",
      "default": "<p>WHY YOU WILL LOVE OUR SHOES</p>"
    },
    {
      "type": "richtext",
      "id": "text_mgwn1B5QXONmg1Ao-1",
      "label": "Heading",
      "default": "<p>HF Cloud: The #1 Rated Shoe for </p>"
    },
    {
      "type": "richtext",
      "id": "text_gaAT0QeTHEtmZA7I-1",
      "label": "Heading",
      "default": "<p>instant foot pain relief &amp; all day comfort</p>"
    },
    {
      "type": "richtext",
      "id": "text_YuweWWSPuzTZKC1a-1",
      "label": "Text",
      "default": "<p>With Hike Footwear Cloud, you&apos;re choosing comfort, healthy feet, and a natural walking experience. These unique shoes give you the feeling of walking barefoot while providing protection and support</p><p></p><p><strong>Invest in comfort you’re wearing daily with Hike.</strong></p>"
    },
    {
      "type": "image_picker",
      "id": "image_Z5VYVEmAVuDnxLex-1",
      "label": "Yoga-Meditate--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_UCXwBVOYJu4mRxXg-1",
      "label": "Heading",
      "default": "<p>Thin Soles to feel more grounded</p>"
    },
    {
      "type": "richtext",
      "id": "text_Bxt3CCbpHxh1jLhK-1",
      "label": "Text",
      "default": "<p>Kleine body, hoge volume, dus we kunnen meer lucht reinigen dan andere modellen</p>"
    },
    {
      "type": "image_picker",
      "id": "image_IgdOtO7LgT2Amq2d-1",
      "label": "Yoga-Meditate--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_cYxK6O4ztIYmsnYl-1",
      "label": "Heading",
      "default": "<p>Wide Toe Box to get space needed</p>"
    },
    {
      "type": "richtext",
      "id": "text_Fbh7kdkT1y5RXxYE-1",
      "label": "Text",
      "default": "<p>The wide toe box provides your toes the space they need and allows them to relax in their natural position.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_BRE7xKuTSREbNeRn-1",
      "label": "Image"
    },
    {
      "type": "image_picker",
      "id": "image_FEtuzu4xOrvmyMLY-1",
      "label": "Yoga-Meditate--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_EpEulnjZsNV0aG2U-1",
      "label": "Heading",
      "default": "<p>Thin Soles to feel more grounded</p>"
    },
    {
      "type": "richtext",
      "id": "text_SbuWZC7ULMS2c62X-1",
      "label": "Text",
      "default": "<p>Kleine body, hoge volume, dus we kunnen meer lucht reinigen dan andere modellen</p>"
    },
    {
      "type": "image_picker",
      "id": "image_Mn1qofUAGNYeqXDl-1",
      "label": "Yoga-Meditate--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_ErGppDKH1o4RSj7t-1",
      "label": "Heading",
      "default": "<p>Wide Toe Box to get space needed</p>"
    },
    {
      "type": "richtext",
      "id": "text_c8oE9pf0SND3rPZR-1",
      "label": "Text",
      "default": "<p>The wide toe box provides your toes the space they need and allows them to relax in their natural position.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_r90MqKlq56bEdPKR-1",
      "label": "Yoga-Meditate--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_i4b0jzp2LmCCDcYn-1",
      "label": "Heading",
      "default": "<p>Natural Comfort</p>"
    },
    {
      "type": "richtext",
      "id": "text_bHZtoIzyN0kuDyTo-1",
      "label": "Text",
      "default": "<p>Natural comfort provides the support and foundation for an upright posture and prevents knee, hip, and back pain.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_vnlsEIBEBoCnp6VH-1",
      "label": "Yoga-Meditate--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_6gfoRLrR1tofqixl-1",
      "label": "Heading",
      "default": "<p>Flexible for more movement</p>"
    },
    {
      "type": "richtext",
      "id": "text_IvUGztKzgUCp1x2U-1",
      "label": "Text",
      "default": "<p>The soft, flexible, and thin soles give your feet an unlimited range of motion and ensure a healthy walking experience.</p>"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison Image With Feature",
      "settings": {
        "text_yhKWRBcBFYbAHeQH-1": "<p>WHY YOU WILL LOVE OUR SHOES</p>",
        "text_mgwn1B5QXONmg1Ao-1": "<p>HF Cloud: The #1 Rated Shoe for </p>",
        "text_gaAT0QeTHEtmZA7I-1": "<p>instant foot pain relief &amp; all day comfort</p>",
        "text_YuweWWSPuzTZKC1a-1": "<p>With Hike Footwear Cloud, you&apos;re choosing comfort, healthy feet, and a natural walking experience. These unique shoes give you the feeling of walking barefoot while providing protection and support</p><p></p><p><strong>Invest in comfort you’re wearing daily with Hike.</strong></p>",
        "text_UCXwBVOYJu4mRxXg-1": "<p>Thin Soles to feel more grounded</p>",
        "text_Bxt3CCbpHxh1jLhK-1": "<p>Kleine body, hoge volume, dus we kunnen meer lucht reinigen dan andere modellen</p>",
        "text_cYxK6O4ztIYmsnYl-1": "<p>Wide Toe Box to get space needed</p>",
        "text_Fbh7kdkT1y5RXxYE-1": "<p>The wide toe box provides your toes the space they need and allows them to relax in their natural position.</p>",
        "text_EpEulnjZsNV0aG2U-1": "<p>Thin Soles to feel more grounded</p>",
        "text_SbuWZC7ULMS2c62X-1": "<p>Kleine body, hoge volume, dus we kunnen meer lucht reinigen dan andere modellen</p>",
        "text_ErGppDKH1o4RSj7t-1": "<p>Wide Toe Box to get space needed</p>",
        "text_c8oE9pf0SND3rPZR-1": "<p>The wide toe box provides your toes the space they need and allows them to relax in their natural position.</p>",
        "text_i4b0jzp2LmCCDcYn-1": "<p>Natural Comfort</p>",
        "text_bHZtoIzyN0kuDyTo-1": "<p>Natural comfort provides the support and foundation for an upright posture and prevents knee, hip, and back pain.</p>",
        "text_6gfoRLrR1tofqixl-1": "<p>Flexible for more movement</p>",
        "text_IvUGztKzgUCp1x2U-1": "<p>The soft, flexible, and thin soles give your feet an unlimited range of motion and ensure a healthy walking experience.</p>"
      }
    }
  ]
}
{% endschema %}
