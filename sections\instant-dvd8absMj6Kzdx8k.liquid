{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant idvd8absMj6Kzdx8k" data-instant-id="dvd8absMj6Kzdx8k" data-instant-version="3.0.3" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-dvd8absMj6Kzdx8k.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="iwqOxCVVOBkcphQ8J">
    {%- liquid
      assign product_2Q0OncUjMGQMzgPt_handle = 'hf-cloud'
      assign product_2Q0OncUjMGQMzgPt = section.settings.product_2Q0OncUjMGQMzgPt | default: all_products[product_2Q0OncUjMGQMzgPt_handle]
      assign product_2Q0OncUjMGQMzgPt_variant = product_2Q0OncUjMGQMzgPt.selected_or_first_available_variant

      if product_2Q0OncUjMGQMzgPt.selected_variant
        assign product_2Q0OncUjMGQMzgPt_variant = product_2Q0OncUjMGQMzgPt.selected_variant
      endif

      assign product_2Q0OncUjMGQMzgPt_image = product_2Q0OncUjMGQMzgPt_variant.featured_image | default: product_2Q0OncUjMGQMzgPt.featured_image
    -%}
    <!--  -->
    {%- liquid
      assign loading = 'eager'
      assign fetchpriority = 'auto'
      if section.location == 'footer'
        assign loading = 'lazy'
      elsif section.location == 'header'
        assign fetchpriority = 'high'
      elsif section.location == 'template'
        if section.index == 1
          assign fetchpriority = 'high'
        elsif section.index > 2
          assign loading = 'lazy'
        endif
      endif
    -%}
    <form class="i2Q0OncUjMGQMzgPt instant-scroll-trigger instant-scroll-trigger--hidden instant-scroll--transform-fade" data-instant-scroll-into-view-replay="true" data-instant-scroll-into-view-offset="1800" data-instant-type="container" id="i2Q0OncUjMGQMzgPt" data-instant-form-product-url="{{ product_2Q0OncUjMGQMzgPt.url }}" data-instant-form-variant-id="{{ product_2Q0OncUjMGQMzgPt_variant.id }}">
      <div class="ip67Fr2gCLM949fAY" data-instant-type="container">
        <div class="iLRF2jlDC4ZO1nmCN" data-instant-type="container">
          <div data-instant-type="image" class="iCo6sSn0oBZ5EnIrd"><img alt="{{ product_2Q0OncUjMGQMzgPt_image.alt }}" src="{{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 800 }}" data-instant-dynamic-content-source="FEATURED_IMAGE" width="{{ product_2Q0OncUjMGQMzgPt_image.width }}" height="{{ product_2Q0OncUjMGQMzgPt_image.height }}" srcSet="{{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 360 }} 360w, {{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 640 }} 640w, {{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 750 }} 750w, {{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 828 }} 828w, {{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 1080 }} 1080w, {{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 1200 }} 1200w, {{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 1920 }} 1920w, {{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 2048 }} 2048w, {{ product_2Q0OncUjMGQMzgPt_image | image_url: width: 3840 }} 3840w" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main"></div>
          <div class="i0AVyGvUtTihUpipJ" data-instant-type="container">
            <div data-instant-type="text" class="instant-rich-text i3MR6pY18pRYXTN9l">
              <div>{{ section.settings.text_3MR6pY18pRYXTN9l }}</div>
            </div>
            <div class="iRxzLFUvXUF3cOQOp" data-instant-type="container">
              <p data-instant-dynamic-content-source="PRICE" data-instant-type="text" class="ilFaNv3sCYPwan12A">
                {%- liquid
                  assign variant_price = product_2Q0OncUjMGQMzgPt_variant.price

                  if product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation
                    assign variant_price = product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation.price
                  endif
                -%}
                {{- variant_price | money -}}
              </p>

              {%- liquid
                assign style = ''
                assign compare_at_price = product_2Q0OncUjMGQMzgPt_variant.compare_at_price
                assign variant_price = product_2Q0OncUjMGQMzgPt_variant.price

                if product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation
                  assign compare_at_price = product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation.compare_at_price
                  assign variant_price = product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation.price
                endif

                if compare_at_price <= variant_price or compare_at_price == 0 or compare_at_price == null
                  assign style = 'style="display: none;"'
                endif
              -%}
              <span
                data-instant-dynamic-content-source="COMPARE_AT"
                class="ij8Sqg1zrhMlLuEUq"
                {{- style -}}
              >
                {{- compare_at_price | money -}}
              </span>
            </div>
          </div>
        </div>
        <div class="ifti1WyzJAkn8eDXM" data-instant-type="container">
          <div class="iCAgj3AoTLUuGwTbc" data-instant-type="container">
            {%- liquid
              assign selected_options = product_2Q0OncUjMGQMzgPt_variant.options
              assign variants_option1 = product_2Q0OncUjMGQMzgPt.variants | map: 'option1'
              assign variants_option2 = product_2Q0OncUjMGQMzgPt.variants | map: 'option2'
              assign variants_option3 = product_2Q0OncUjMGQMzgPt.variants | map: 'option3'
              assign selected_option = product_2Q0OncUjMGQMzgPt.options_with_values | where: 'name', 'Size' | first

              if selected_option == null
                assign selected_option = product_2Q0OncUjMGQMzgPt.options_with_values[1]
              endif

              assign hide_variant_picker = false

              if product_2Q0OncUjMGQMzgPt.has_only_default_variant or selected_option == null
                assign hide_variant_picker = true
              endif
            -%}
            {%- unless hide_variant_picker -%}
              <fieldset class="instant-variant-picker instant-variant-picker--select ig4ENthq9Zy5mOvTI" data-instant-type="VARIANT_PICKER">
                <legend class="instant-visually-hidden">{{ selected_option.name }}</legend>

                <select name="2Q0OncUjMGQMzgPt__{{ selected_option.name | escape }}">
                  {%- for value in selected_option.values -%}
                    {%- liquid
                      assign option_disabled = true

                      for option1_name in variants_option1
                        if selected_option.position == 1 and option1_name == value and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == selected_options[2]
                          assign option_disabled = false
                        elsif selected_option.position == 2 and option1_name == selected_options[0] and variants_option2[forloop.index0] == value and variants_option3[forloop.index0] == selected_options[2]
                          assign option_disabled = false
                        elsif selected_option.position == 3 and option1_name == selected_options[0] and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == value
                          assign option_disabled = false
                        endif
                      endfor

                      assign is_in_stock = true

                      unless product_2Q0OncUjMGQMzgPt_variant.inventory_management != 'shopify'
                        assign selected_variant = ''

                        for variant in product_2Q0OncUjMGQMzgPt.variants
                          if selected_option.position == 1 and variant.option1 == value and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                            assign selected_variant = variant
                          elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == value and variant.option3 == selected_options[2]
                            assign selected_variant = variant
                          elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == value
                            assign selected_variant = variant
                          endif
                        endfor

                        if selected_variant != '' and selected_variant.available == false
                          assign is_in_stock = false
                        endif
                      endunless
                    -%}
                    <option
                      id="g4ENthq9Zy5mOvTI-{{ selected_option.position }}-{{ forloop.index0 }}"
                      value="{{ value | escape }}"
                      {% if selected_options[1] == value %}
                        selected
                      {% endif %}
                      {% if option_disabled or is_in_stock == false %}
                        data-instant-disabled="true"
                      {% endif %}
                    >
                      {{ value }}
                      {% if option_disabled or is_in_stock == false %}- Unavailable{% endif %}
                    </option>
                  {%- endfor -%}
                </select>

                <!--  -->

                {%- for value in selected_option.values -%}
                  {% if selected_options[1] == value %}
                    <span
                      class="i1Iu3pf2VfyKt68C7"
                      data-instant-type="selected-variant-option"
                    >
                      {{ value }}
                    </span>
                  {% endif %}
                {%- endfor -%}

                <div data-instant-type="icon" class="iEzM4kWdtUbdLMzHx">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z"></path>
                  </svg>
                </div>
              </fieldset>
            {%- endunless -%}

            <!--  -->
            {%- liquid
              assign selected_options = product_2Q0OncUjMGQMzgPt_variant.options
              assign variants_option1 = product_2Q0OncUjMGQMzgPt.variants | map: 'option1'
              assign variants_option2 = product_2Q0OncUjMGQMzgPt.variants | map: 'option2'
              assign variants_option3 = product_2Q0OncUjMGQMzgPt.variants | map: 'option3'
              assign selected_option = product_2Q0OncUjMGQMzgPt.options_with_values | where: 'name', 'Color' | first

              if selected_option == null
                assign selected_option = product_2Q0OncUjMGQMzgPt.options_with_values[0]
              endif

              assign hide_variant_picker = false

              if product_2Q0OncUjMGQMzgPt.has_only_default_variant or selected_option == null
                assign hide_variant_picker = true
              endif
            -%}
            {%- unless hide_variant_picker -%}
              <fieldset class="instant-variant-picker instant-variant-picker--select iBLFj0IIMwUG8AjiA" data-instant-type="VARIANT_PICKER">
                <legend class="instant-visually-hidden">{{ selected_option.name }}</legend>

                <select name="2Q0OncUjMGQMzgPt__{{ selected_option.name | escape }}">
                  {%- for value in selected_option.values -%}
                    {%- liquid
                      assign option_disabled = true

                      for option1_name in variants_option1
                        if selected_option.position == 1 and option1_name == value and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == selected_options[2]
                          assign option_disabled = false
                        elsif selected_option.position == 2 and option1_name == selected_options[0] and variants_option2[forloop.index0] == value and variants_option3[forloop.index0] == selected_options[2]
                          assign option_disabled = false
                        elsif selected_option.position == 3 and option1_name == selected_options[0] and variants_option2[forloop.index0] == selected_options[1] and variants_option3[forloop.index0] == value
                          assign option_disabled = false
                        endif
                      endfor

                      assign is_in_stock = true

                      unless product_2Q0OncUjMGQMzgPt_variant.inventory_management != 'shopify'
                        assign selected_variant = ''

                        for variant in product_2Q0OncUjMGQMzgPt.variants
                          if selected_option.position == 1 and variant.option1 == value and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                            assign selected_variant = variant
                          elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == value and variant.option3 == selected_options[2]
                            assign selected_variant = variant
                          elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == value
                            assign selected_variant = variant
                          endif
                        endfor

                        if selected_variant != '' and selected_variant.available == false
                          assign is_in_stock = false
                        endif
                      endunless
                    -%}
                    <option
                      id="BLFj0IIMwUG8AjiA-{{ selected_option.position }}-{{ forloop.index0 }}"
                      value="{{ value | escape }}"
                      {% if selected_options[0] == value %}
                        selected
                      {% endif %}
                      {% if option_disabled or is_in_stock == false %}
                        data-instant-disabled="true"
                      {% endif %}
                    >
                      {{ value }}
                      {% if option_disabled or is_in_stock == false %}- Unavailable{% endif %}
                    </option>
                  {%- endfor -%}
                </select>

                <!--  -->

                {%- for value in selected_option.values -%}
                  {% if selected_options[0] == value %}
                    <span
                      class="i59fpID6lWRPxGx0T"
                      data-instant-type="selected-variant-option"
                    >
                      {{ value }}
                    </span>
                  {% endif %}
                {%- endfor -%}

                <div data-instant-type="icon" class="i9g5hso1QFolqfGeK">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M216.49,104.49l-80,80a12,12,0,0,1-17,0l-80-80a12,12,0,0,1,17-17L128,159l71.51-71.52a12,12,0,0,1,17,17Z"></path>
                  </svg>
                </div>
              </fieldset>
            {%- endunless -%}
          </div>
          <a
            class="ifhPgRcwHVJMzsO9Z"
            data-instant-type="container"
            href="{{ routes.cart_url }}/{{ product_2Q0OncUjMGQMzgPt_variant.id }}:1?storefront=true"
            rel="noopener noreferrer"
            data-instant-action-type="redirect-to-cart"
            data-instant-action-id="{{ product_2Q0OncUjMGQMzgPt.id }}"
            data-instant-action-variant-id="{{ product_2Q0OncUjMGQMzgPt_variant.id }}"
            data-instant-disabled="
              {%- unless product_2Q0OncUjMGQMzgPt_variant.inventory_management != 'shopify' -%}
              	{%- if product_2Q0OncUjMGQMzgPt_variant.available == false -%}
              		true
              	{%-	endif -%}
              {%- endunless -%}
            "
          >
            <div data-instant-type="text" class="instant-rich-text iwXHGGbUOHzxRnlWe">
              <div>{{ section.settings.text_wXHGGbUOHzxRnlWe }}</div>
            </div>
            <div class="i7kxBdG5ZtrfQUkQv" data-instant-type="container">
              <p data-instant-dynamic-content-source="PRICE" data-instant-type="text" class="ihOLEowOuF0lf99uC">
                {%- liquid
                  assign variant_price = product_2Q0OncUjMGQMzgPt_variant.price

                  if product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation
                    assign variant_price = product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation.price
                  endif
                -%}
                {{- variant_price | money -}}
              </p>

              {%- liquid
                assign style = ''
                assign compare_at_price = product_2Q0OncUjMGQMzgPt_variant.compare_at_price
                assign variant_price = product_2Q0OncUjMGQMzgPt_variant.price

                if product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation
                  assign compare_at_price = product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation.compare_at_price
                  assign variant_price = product_2Q0OncUjMGQMzgPt_variant.selected_selling_plan_allocation.price
                endif

                if compare_at_price <= variant_price or compare_at_price == 0 or compare_at_price == null
                  assign style = 'style="display: none;"'
                endif
              -%}
              <span
                data-instant-dynamic-content-source="COMPARE_AT"
                class="ibhhJLiMgYzoO597o"
                {{- style -}}
              >
                {{- compare_at_price | money -}}
              </span>
            </div>
          </a>
        </div>
      </div>
      <script type="application/json" id="variants__2Q0OncUjMGQMzgPt--{{ section.id }}">
        {{ product_2Q0OncUjMGQMzgPt.variants | json }}
      </script>
      <script type="application/json" id="options__2Q0OncUjMGQMzgPt--{{ section.id }}">
        {{ product_2Q0OncUjMGQMzgPt.options_with_values | json }}
      </script>
    </form>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.3",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison Sticky CTA",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "product",
      "id": "product_2Q0OncUjMGQMzgPt",
      "label": "Add to cart bar"
    },
    {
      "type": "richtext",
      "id": "text_3MR6pY18pRYXTN9l",
      "label": "Text",
      "default": "<h3>HF Series x Cloud (LAUNCH DEAL)</h3>"
    },
    {
      "type": "richtext",
      "id": "text_wXHGGbUOHzxRnlWe",
      "label": "Text",
      "default": "<p>Add to Bag +</p>"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison Sticky CTA",
      "settings": {
        "product_2Q0OncUjMGQMzgPt": "hf-cloud",
        "text_3MR6pY18pRYXTN9l": "<h3>HF Series x Cloud (LAUNCH DEAL)</h3>",
        "text_wXHGGbUOHzxRnlWe": "<p>Add to Bag +</p>"
      }
    }
  ]
}
{% endschema %}
