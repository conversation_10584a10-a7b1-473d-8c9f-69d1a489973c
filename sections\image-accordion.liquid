{% comment %}
  Section: Image + Accordion (two-column)
  - Left: fixed-height image
  - Right: accordion list (max 10 items via blocks)
  - Progressive disclosure: show first 5, reveal rest via "Show More"
  - Smooth transitions for open/close; isolated styles per-section
{% endcomment %}

<section id="image-accordion-{{ section.id }}" class="image-accordion" style="--ia-image-h: {{ section.settings.image_height | default: 280 }}px; --ia-gap: 20px; --ia-head: {{ section.settings.heading_color }}; --ia-body: {{ section.settings.body_color }}; --ia-border: {{ section.settings.border_color }}; --ia-icon: {{ section.settings.icon_color }}; --ia-radius: {{ section.settings.corner_radius | default: 10 }}px; --ia-speed: {{ section.settings.anim_speed | default: 240 }}ms;">
  <div class="container">

  <div class="ia__grid">
    <div class="ia__media">
      {% assign image_rendered = false %}
      {% for block in section.blocks %}
        {% if block.type == 'image' and image_rendered == false %}
          {% if block.settings.image != blank %}
            {{ block.settings.image | image_url: width: 1600 | image_tag: loading: 'lazy', sizes: '(min-width: 1000px) 600px, 100vw', widths: '400,600,800,1200,1600', class: 'ia__img', alt: block.settings.alt | default: section.settings.heading | default: 'Illustration' }}
            {% assign image_rendered = true %}
          {% endif %}
        {% endif %}
      {% endfor %}

    </div>

    <div class="ia__accordions" data-initial-count="5">
      {% if section.settings.heading != blank %}
        <h2 class="ia__heading h3">{{ section.settings.heading }}</h2>
      {% endif %}
      {% if section.settings.subheading != blank %}
        <div class="ia__subheading rte text-base">{{ section.settings.subheading }}</div>
      {% endif %}

      <div class="ia__list" role="list">
        {% assign item_count = 0 %}
        {% for block in section.blocks %}
          {% if block.type == 'item' %}
            {% assign item_count = item_count | plus: 1 %}
            {%- assign index = item_count -%}
            {%- assign panel_id = 'ia-panel-' | append: block.id -%}
            {%- assign btn_id = 'ia-trigger-' | append: block.id -%}
            <div class="ia-item{% if index > 5 %} is-extra is-hidden{% endif %}" role="listitem" data-accordion-item {{ block.shopify_attributes }}>
              <button type="button" class="ia-trigger text-base" id="{{ btn_id }}" aria-expanded="false" aria-controls="{{ panel_id }}">
                {% if block.settings.icon != blank %}
                  {{ block.settings.icon | image_url: width: 96 | image_tag: class: 'ia-icon', alt: '', loading: 'lazy' }}
                {% endif %}
                <span class="ia-title" style="font-weight:{% if section.settings.heading_weight == 'bold' %}700{% else %}400{% endif %};">{{ block.settings.title | default: 'Accordion title' }}</span>
                <span class="ia-plusminus" aria-hidden="true">
                  <svg class="ia-icon-plusminus" viewBox="0 0 24 24" width="20" height="20" focusable="false" aria-hidden="true">
                    <path class="h" d="M5 12h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                    <path class="v" d="M12 5v14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
                  </svg>
                </span>
              </button>
              <div id="{{ panel_id }}" class="ia-panel" role="region" aria-labelledby="{{ btn_id }}" hidden>
                <div class="ia-panel__inner rte text-base">
                  {{ block.settings.content }}
                  {% if block.settings.content_image != blank %}
                    <div class="ia-panel__media">
                      {{ block.settings.content_image | image_url: width: 1600 | image_tag: loading: 'lazy', sizes: '100vw', widths: '400,600,800,1200,1600', class: 'ia-panel__image', alt: block.settings.content_alt | default: block.settings.title | escape }}
                    </div>
                  {% endif %}
                </div>
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>

      {% if item_count > 5 %}
        <button class="ia__more text-sm" type="button">{{ section.settings.more_label | default: 'Show More' }}</button>
      {% endif %}
    </div>
  </div>
  </div>


  <style>
    #image-accordion-{{ section.id }} {
      padding: 24px 0;
    }

    #image-accordion-{{ section.id }} .ia__grid {
      display: grid;

    /* Vertically center the right column within the grid row */
    #image-accordion-{{ section.id }} .ia__accordions { align-self: center; }

      grid-template-columns: 1fr 1fr;
      gap: var(--ia-gap);
      align-items: start;
    }

    /* Mobile container hidden by default; shown under breakpoint */
    #image-accordion-{{ section.id }} .ia__mobile { display: none; }


    /* Left media: fixed height container */
    #image-accordion-{{ section.id }} .ia__media {
      height: var(--ia-image-h);
      align-self: start; /* keep left column pinned to top regardless of right height */
      overflow: hidden;
      border: 0;
      border-radius: var(--ia-radius);
      background: #fff;
    }
    #image-accordion-{{ section.id }} .ia__img {
      width: 100%;
      height: 100%;
      object-fit: {{ section.settings.image_fit | default: 'cover' }};
      display: block;
    }

    /* Accordion */
    #image-accordion-{{ section.id }} .ia__heading {
      margin: 0 0 8px;
      color: var(--ia-head);

    }
    #image-accordion-{{ section.id }} .ia__subheading {
      margin-bottom: 12px;
      color: var(--ia-body);

    }

    #image-accordion-{{ section.id }} .ia__list {
      border: 0;
      border-radius: 0;
      overflow: visible;
    }
    #image-accordion-{{ section.id }} .ia-item { border-top: 1px solid var(--ia-border); }


    #image-accordion-{{ section.id }} .ia-item.is-hidden { display: none; }

    #image-accordion-{{ section.id }} .ia-trigger {
      width: 100%;
      min-height: 52px;
      padding: 14px 16px;
      display: grid;
      grid-template-columns: auto 1fr auto;
      gap: 10px;
      align-items: center;
      text-align: left;
      background: #fff;
      color: var(--ia-head);

      cursor: pointer;
      border: 0;
    }
    #image-accordion-{{ section.id }} .ia-icon { width: {{ section.settings.icon_size | default: 22 }}px; height: auto; }
    #image-accordion-{{ section.id }} .ia-title { line-height: 1.2; }
    #image-accordion-{{ section.id }} .ia-plusminus { display: inline-flex; color: var(--ia-icon); justify-self: end; }
    #image-accordion-{{ section.id }} .ia-plusminus .v { transform-origin: 12px 12px; transition: transform var(--ia-speed) ease, opacity var(--ia-speed) ease; }
    #image-accordion-{{ section.id }} .ia-trigger[aria-expanded="true"] .ia-plusminus .v { transform: scaleY(0); opacity: 0; }

    #image-accordion-{{ section.id }} .ia-panel {
      max-height: 0;
      overflow: hidden;
      color: var(--ia-body);

      transition: max-height var(--ia-speed) ease;
    }
    #image-accordion-{{ section.id }} .ia-panel__inner { padding: 0 16px 14px 48px; transform: translateY(-6px); opacity: 0; transition: transform var(--ia-speed) ease, opacity var(--ia-speed) ease; }
    #image-accordion-{{ section.id }} .ia-panel.is-open .ia-panel__inner { transform: translateY(0); opacity: 1; }
    #image-accordion-{{ section.id }} .ia-panel__image { display: block; width: 100%; height: auto; margin-top: 12px; border-radius: var(--ia-radius); }

    /* RTE list and paragraph spacing inside accordion panels */
    #image-accordion-{{ section.id }} .ia-panel__inner.rte ul,
    #image-accordion-{{ section.id }} .ia-panel__inner.rte ol { margin: 10px 0; padding-left: 1.25rem; list-style-position: outside; }
    #image-accordion-{{ section.id }} .ia-panel__inner.rte li { margin: 6px 0; }
    #image-accordion-{{ section.id }} .ia-panel__inner.rte p { margin: 0 0 10px; }
    #image-accordion-{{ section.id }} .ia-panel__inner.rte :is(ul,ol) + p,
    #image-accordion-{{ section.id }} .ia-panel__inner.rte p + :is(ul,ol) { margin-top: 12px; }



    #image-accordion-{{ section.id }} .ia__more {
      margin-top: 10px;
      border: 1px solid var(--ia-border);
      background: #fff;
      border-radius: 6px;
      padding: 10px 12px;

      cursor: pointer;
    }

    /* Reveal animation for newly shown items */
    #image-accordion-{{ section.id }} .ia-item.revealed { animation: ia-fade-in var(--ia-speed) ease both; }
    @keyframes ia-fade-in { from { opacity: 0; transform: translateY(6px); } to { opacity: 1; transform: translateY(0); } }

    /* Accessibility focus */
    #image-accordion-{{ section.id }} .ia-trigger:focus-visible { outline: 2px solid var(--ia-border); outline-offset: 2px; }

    @media (prefers-reduced-motion: reduce) {
      #image-accordion-{{ section.id }} .ia-chev { transition: none; }
      #image-accordion-{{ section.id }} .ia-panel { transition: none; }
      #image-accordion-{{ section.id }} .ia-item.revealed { animation: none; }
    }

    @media (max-width: 900px) {
      #image-accordion-{{ section.id }} .ia__grid { display: none; }
      #image-accordion-{{ section.id }} .ia__mobile { display: block; }
    }
  </style>

  <script>
    (function(){
      var root = document.getElementById('image-accordion-{{ section.id }}');
      if (!root) return;

      // Accordion toggles — single-open per container with smooth slide animation (max-height)
      function openPanel(trigger, panel) {
        trigger.setAttribute('aria-expanded', 'true');
        panel.hidden = false;
        panel.classList.add('is-open');
        // measure and animate
        panel.style.maxHeight = panel.scrollHeight + 'px';
      }
      function closePanel(trigger, panel) {
        trigger.setAttribute('aria-expanded', 'false');
        // set current height to allow transition
        panel.style.maxHeight = panel.scrollHeight + 'px';
        // force reflow then collapse
        panel.getBoundingClientRect();
        panel.classList.remove('is-open');
        panel.style.maxHeight = '0px';
        panel.addEventListener('transitionend', function(){ panel.hidden = true; }, { once: true });
      }

      var items = root.querySelectorAll('[data-accordion-item]');
      items.forEach(function(item){
        var trigger = item.querySelector('.ia-trigger');
        var panel = item.querySelector('.ia-panel');
        if (!trigger || !panel) return;

        trigger.addEventListener('click', function(e){
          e.preventDefault();
          var container = item.closest('.ia__accordions, .ia__mobile') || root;
          var isOpen = trigger.getAttribute('aria-expanded') === 'true';

          if (!isOpen) {
            var openTriggers = container.querySelectorAll('[data-accordion-item] .ia-trigger[aria-expanded="true"]');
            openTriggers.forEach(function(otherTrigger){
              if (otherTrigger !== trigger) {
                var otherPanel = otherTrigger.closest('[data-accordion-item]').querySelector('.ia-panel');
                if (otherPanel) closePanel(otherTrigger, otherPanel);
              }
            });
            openPanel(trigger, panel);
          } else {
            closePanel(trigger, panel);
          }
        });
      });

      // Show More / Show Less for both desktop and mobile containers
      var moreBtns = root.querySelectorAll('.ia__more');
      moreBtns.forEach(function(moreBtn){
        var container = moreBtn.closest('.ia__accordions, .ia__mobile') || root;
        var extras = container.querySelectorAll('.ia-item.is-extra');
        var expanded = false;
        moreBtn.setAttribute('aria-expanded', 'false');

        moreBtn.addEventListener('click', function(){
          expanded = !expanded;
          extras.forEach(function(item){
            if (expanded) {
              item.classList.remove('is-hidden');
              item.classList.add('revealed');
              setTimeout(function(){ item.classList.remove('revealed'); }, 400);
            } else {
              var t = item.querySelector('.ia-trigger');
              var p = item.querySelector('.ia-panel');
              if (t && p) {
                t.setAttribute('aria-expanded', 'false');
                // collapse animation then hide
                p.style.maxHeight = p.scrollHeight + 'px';
                p.getBoundingClientRect();
                p.classList.remove('is-open');
                p.style.maxHeight = '0px';
                p.addEventListener('transitionend', function(){ p.hidden = true; }, { once: true });
              }
              item.classList.add('is-hidden');
            }
          });
          moreBtn.textContent = expanded ? ('{{ section.settings.less_label | default: "Show Less" }}') : ('{{ section.settings.more_label | default: "Show More" }}');
          moreBtn.setAttribute('aria-expanded', String(expanded));
        });
      });
    })();
  </script>


  <div class="container">

  <!-- Mobile single-column version honoring block order -->
  <div class="ia__mobile">
    {% if section.settings.heading != blank %}
      <h2 class="ia__heading h3">{{ section.settings.heading }}</h2>
    {% endif %}
    {% if section.settings.subheading != blank %}
      <div class="ia__subheading rte text-base">{{ section.settings.subheading }}</div>
    {% endif %}

    {% assign count = 0 %}
    {% for block in section.blocks %}
      {% if block.type == 'image' %}
        <div class="ia__media">
          {% if block.settings.image != blank %}
            {{ block.settings.image | image_url: width: 1600 | image_tag: loading: 'lazy', sizes: '100vw', widths: '400,600,800,1200,1600', class: 'ia__img', alt: block.settings.alt | default: section.settings.heading | default: 'Illustration' }}
          {% endif %}
        </div>
      {% elsif block.type == 'item' %}
        {% assign count = count | plus: 1 %}
        {% assign panel_id = 'ia-m-panel-' | append: block.id %}
        {% assign btn_id = 'ia-m-trigger-' | append: block.id %}
        <div class="ia-item{% if count > 5 %} is-extra is-hidden{% endif %}" role="listitem" data-accordion-item {{ block.shopify_attributes }}>
          <button type="button" class="ia-trigger text-base" id="{{ btn_id }}" aria-expanded="false" aria-controls="{{ panel_id }}">
            {% if block.settings.icon != blank %}
              {{ block.settings.icon | image_url: width: 96 | image_tag: class: 'ia-icon', alt: '', loading: 'lazy' }}
            {% endif %}
            <span class="ia-title" style="font-weight:{% if section.settings.heading_weight == 'bold' %}700{% else %}400{% endif %};">{{ block.settings.title | default: 'Accordion title' }}</span>
            <span class="ia-plusminus" aria-hidden="true">
              <svg class="ia-icon-plusminus" viewBox="0 0 24 24" width="20" height="20" focusable="false" aria-hidden="true">
                <path class="h" d="M5 12h14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" />



                <path class="v" d="M12 5v14" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
              </svg>
            </span>
          </button>
          <div id="{{ panel_id }}" class="ia-panel" role="region" aria-labelledby="{{ btn_id }}" hidden>
            <div class="ia-panel__inner rte text-base">
              {{ block.settings.content }}

              {% if block.settings.content_image != blank %}
                <div class="ia-panel__media">
                  {{ block.settings.content_image | image_url: width: 1600 | image_tag: loading: 'lazy', sizes: '100vw', widths: '400,600,800,1200,1600', class: 'ia-panel__image', alt: block.settings.content_alt | default: block.settings.title | escape }}
                </div>
              {% endif %}

            </div>
          </div>
        </div>
      {% endif %}
    {% endfor %}

    {% if count > 5 %}
      <button class="ia__more text-sm" type="button">{{ section.settings.more_label | default: 'Show More' }}</button>
    {% endif %}
  </div>
</div>

  {% schema %}
  {
    "name": "Image + Accordion",
    "tag": "section",
    "class": "section-image-accordion",
    "max_blocks": 10,
    "settings": [
      { "type": "text", "id": "heading", "label": "Heading" },
      { "type": "richtext", "id": "subheading", "label": "Subheading (RTE)" },

      { "type": "range", "id": "image_height", "label": "Left image height", "min": 160, "max": 600, "step": 10, "unit": "px", "default": 280 },
      { "type": "select", "id": "image_fit", "label": "Left image fit", "options": [ {"value":"cover","label":"Cover"}, {"value":"contain","label":"Contain"} ], "default": "cover" },
      { "type": "color", "id": "heading_color", "label": "Heading color", "default": "#111111" },
      { "type": "color", "id": "body_color", "label": "Body color", "default": "#333333" },
      { "type": "color", "id": "border_color", "label": "Border color", "default": "#e5e7eb" },
      { "type": "color", "id": "icon_color", "label": "Icon accent color", "default": "#2e6a3d" },

      { "type": "select", "id": "heading_weight", "label": "Heading weight", "options": [ {"value":"normal","label":"Normal"}, {"value":"bold","label":"Bold"} ], "default": "bold" },
      { "type": "range", "id": "icon_size", "label": "Accordion icon width", "min": 16, "max": 48, "step": 1, "unit": "px", "default": 22 },
      { "type": "text", "id": "more_label", "label": "Show More label", "default": "Show More" },
      { "type": "text", "id": "less_label", "label": "Show Less label", "default": "Show Less" },
      { "type": "range", "id": "anim_speed", "label": "Animation speed", "min": 100, "max": 600, "step": 20, "unit": "ms", "default": 240 },
      { "type": "range", "id": "corner_radius", "label": "Corner radius", "min": 0, "max": 24, "step": 2, "unit": "px", "default": 10 }
    ],
    "blocks": [
      {
        "type": "image",
        "name": "Left image (block)",
        "limit": 1,
        "settings": [
          { "type": "image_picker", "id": "image", "label": "Image" },
          { "type": "text", "id": "alt", "label": "Alt text" }
        ]
      },

      {
        "type": "item",
        "name": "Accordion item",
        "settings": [
          { "type": "image_picker", "id": "icon", "label": "Item icon" },
          { "type": "text", "id": "title", "label": "Title", "default": "Accordion title" },
          { "type": "richtext", "id": "content", "label": "Content (RTE)", "default": "<p>Describe the details here. You can include images, lists, and links.</p>" },
          { "type": "image_picker", "id": "content_image", "label": "Image below content" },
          { "type": "text", "id": "content_alt", "label": "Image alt text" }
        ]
      }
    ],
    "presets": [
      {
        "name": "Image + Accordion",
        "category": "Content",
        "blocks": [
          { "type": "image" },
          { "type": "item" },
          { "type": "item" },
          { "type": "item" }
        ]
      }
    ]
  }
  {% endschema %}
</section>

