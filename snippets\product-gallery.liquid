{%- assign default_media = product.selected_or_first_available_variant.featured_media | default: product.featured_media -%}
{%- capture product_form_id -%}product-form-{{ product.id }}-{{ section.id }}{%- endcapture -%}
{%- capture product_gallery_id -%}product-gallery-{{ product.id }}-{{ section.id }}{%- endcapture -%}

{%- comment -%}
IMPLEMENTATION NOTE: Shopify does not natively offer a way to create a set of images per variant. This is often
pretty limited when you have a lot of colors and would like to only see the images specific to a given color. To
goes around that, Impact offers a hack using alt tags whose usage is described here: https://support.maestrooo.com/article/187-product-creating-variant-image-set
The implementation is rather complex and inefficient due to a lot of string parsings, but it is, unfortunately, the
only way to do it on Shopify right now.
{%- endcomment -%}

{% assign filtered_indexes = null | sort %}

{%- if product.variants.size > 1 -%}
  {%- for media in product.media -%}
    {%- if media.alt contains '#' and media.alt != product.title -%}
      {%- assign alt_parts = media.alt | split: '#' -%}

      {%- assign media_group_parts = alt_parts.last | split: '_' -%}
      {%- assign option = product.options_by_name[media_group_parts.first] -%}
      {%- assign option_value = option.selected_value | downcase -%}

      {%- assign downcase_group_value = media_group_parts.last | strip | downcase -%}

      {%- if option_value != downcase_group_value and media != default_media -%}
        {%- assign media_position = media.position | sort -%}
        {%- assign filtered_indexes = filtered_indexes | concat: media_position -%}
      {%- endif -%}
    {%- endif -%}
  {%- endfor -%}
{%- endif -%}

<product-gallery form="{{ product_form_id }}" {%- if section.settings.enable_image_zoom -%}allow-zoom="{{ section.settings.max_image_zoom_level }}"{% endif %} class="product-gallery {% if section.settings.mobile_carousel_control contains 'dots' %}product-gallery--mobile-dots{% endif %} {% if section.settings.desktop_media_layout contains 'grid' %}product-gallery--desktop-grid{% else %}product-gallery--desktop-carousel{% endif %} {% if section.settings.desktop_media_layout == 'carousel_thumbnails_left' %}product-gallery--desktop-thumbnails-left{% endif %} {% if section.settings.mobile_media_size == 'expanded' %}product-gallery--mobile-expanded{% endif %}">
  {%- capture page_dots -%}
    <page-dots class="page-dots {% if section.settings.mobile_carousel_control == 'floating_dots' %}page-dots--blurred{% endif %} md:hidden" aria-controls="{{ product_gallery_id }}">
      {%- for media in product.media -%}
        <button type="button" class="tap-area" {% if filtered_indexes contains media.position %}hidden{% endif %} aria-current="{% if media == default_media %}true{% else %}false{% endif %}">
          <span class="sr-only">{{ 'general.accessibility.go_to_item' | t: index: media.position }}</span>
        </button>
      {%- endfor -%}
    </page-dots>
  {%- endcapture -%}

  <div class="product-gallery__ar-wrapper">
    <div class="product-gallery__media-list-wrapper">
      {%- if section.settings.desktop_media_layout contains 'carousel' and product.media.size > 1 -%}
        <custom-cursor class="product-gallery__cursor" {% if default_media.media_type != 'image' %}hidden{% endif %}>
          <div class="circle-button circle-button--fill circle-button--lg">
            {%- render 'icon' with 'chevron-right' -%}
          </div>
        </custom-cursor>
      {%- endif -%}

      {%- comment -%}
      --------------------------------------------------------------------------------------------------------------------
      MEDIA LIST
      --------------------------------------------------------------------------------------------------------------------
      {%- endcomment -%}
      <media-carousel desktop-mode="{{ section.settings.desktop_media_layout }}" adaptive-height initial-index="{{ default_media.position | minus: 1 }}" {% if section.settings.enable_video_autoplay %}autoplay{% endif %} id="{{ product_gallery_id }}" class="product-gallery__media-list {% if section.settings.mobile_media_size == 'expanded' %}full-bleed{% else %}bleed{% endif %} scroll-area md:unbleed">
        {%- assign media_attached_to_variant = product.images | where: 'attached_to_variant?', true | map: 'src' -%}

        {%- for media in product.media -%}
          {%- case section.settings.desktop_media_layout -%}
            {%- when 'carousel_thumbnails_bottom' -%}
              {%- capture sizes -%}(max-width: 740px) calc(100vw - 40px), (max-width: 999px) calc(100vw - 64px), min(760px, 42vw){%- endcapture -%}

            {%- when 'carousel_thumbnails_left' -%}
              {%- capture sizes -%}(max-width: 740px) calc(100vw - 40px), (max-width: 999px) calc(100vw - 64px), min(730px, 40vw){%- endcapture -%}

            {%- when 'grid' -%}
              {%- capture sizes -%}(max-width: 740px) calc(100vw - 40px), (max-width: 999px) calc(100vw - 64px), min(580px, 30vw){%- endcapture -%}

            {% when 'grid_highlight' %}
              {%- capture sizes -%}(max-width: 740px) calc(100vw - 40px), (max-width: 999px) calc(100vw - 64px), {% if forloop.first or media_attached_to_variant contains media.src %}min(1200px, 60vw){% else %}min(580px, 30vw){% endif %}{%- endcapture -%}
          {%- endcase -%}

          <div class="product-gallery__media {% if media.alt contains '@expand' %}product-gallery__media--expand{% endif %} {% if section.settings.mobile_carousel_control != 'free_scroll' %}snap-center{% endif %}" data-media-type="{{ media.media_type }}" data-media-id="{{ media.id }}" {% if filtered_indexes contains media.position %}hidden{% endif %}>
            {%- if default_media == media -%}
              {%- assign preload = true -%}
            {%- else -%}
              {%- assign preload = false -%}
            {%- endif -%}

            {%- render 'media', media: media, sizes: sizes, preload: preload, autoplay: section.settings.enable_video_autoplay, loop: section.settings.enable_video_looping, group: 'product' -%}

            {%- if section.settings.enable_image_zoom and section.settings.desktop_media_layout contains 'grid' and media.media_type == 'image' -%}
              <div class="product-gallery__zoom hidden md:block">
                <button type="button" is="product-zoom-button" class="circle-button circle-button--fill ring">
                  <span class="sr-only">{{ 'product.gallery.zoom' | t }}</span>
                  {%- render 'icon' with 'image-zoom' -%}
                </button>
              </div>
            {%- endif -%}
          </div>
        {%- endfor -%}
      </media-carousel>

      {%- if section.settings.enable_image_zoom -%}
        <div class="product-gallery__zoom {% if section.settings.desktop_media_layout contains 'grid' %}md:hidden{% endif %}">
          <button type="button" is="product-zoom-button" class="circle-button circle-button--fill ring">
            <span class="sr-only">{{ 'product.gallery.zoom' | t }}</span>
            {%- render 'icon' with 'image-zoom' -%}
          </button>
        </div>
      {%- endif -%}

      {%- if product.media.size > 1 and section.settings.mobile_carousel_control == 'floating_dots' -%}
        {{- page_dots -}}
      {%- endif -%}
    </div>

    {%- comment -%}
    ----------------------------------------------------------------------------------------------------------------------
    VIEW IN YOUR SPACE
    ----------------------------------------------------------------------------------------------------------------------
    {%- endcomment -%}

    {%- assign first_3d_model = product.media | where: 'media_type', 'model' | first -%}

    {%- if first_3d_model -%}
      <link rel="stylesheet" href="https://cdn.shopify.com/shopifycloud/model-viewer-ui/assets/v1.0/model-viewer-ui.css" media="print" onload="this.media='all'">

      <button class="button button--lg button--subdued w-full" data-shopify-xr data-shopify-model3d-id="{{ first_3d_model.id }}" data-shopify-model3d-default-id="{{ first_3d_model.id }}" data-shopify-title="{{ product.title | escape }}" data-shopify-xr-hidden>
        <span class="text-with-icon justify-center">
          {%- render 'icon' with 'media-view-in-space' -%}
          {{- 'product.general.view_in_space' | t -}}
        </span>
      </button>
    {%- endif -%}
  </div>

  {%- comment -%}
  --------------------------------------------------------------------------------------------------------------------
  CONTROLS
  --------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  {%- if product.media.size > 1 -%}
    {%- if section.settings.mobile_carousel_control == 'dots' -%}
      {{- page_dots -}}
    {%- endif -%}

    {%- if section.settings.mobile_carousel_control == 'thumbnails' or section.settings.desktop_media_layout contains 'thumbnails' -%}
      <scroll-shadow class="product-gallery__thumbnail-list-wrapper">
        <page-dots align-selected class="product-gallery__thumbnail-list scroll-area bleed md:unbleed" aria-controls="{{ product_gallery_id }}">
          {%- for media in product.media -%}
            <button type="button" class="product-gallery__thumbnail" {% if filtered_indexes contains media.position %}hidden{% endif %} aria-current="{% if media == default_media %}true{% else %}false{% endif %}" aria-label="{{ 'general.accessibility.go_to_item' | t: index: forloop.index | escape }}">
              {{- media | image_url: width: media.preview_image.width | image_tag: loading: 'lazy', sizes: '(max-width: 699px) 56px, 64px', widths: '56,64,112,128,168,192', class: 'object-contain rounded-sm' -}}

              {%- unless media.media_type == 'image' -%}
                <div class="product-gallery__media-badge">
                  {%- if media.media_type == 'model' -%}
                    {%- render 'icon' with 'play-model', width: 10, height: 12 -%}
                  {%- else -%}
                    {%- render 'icon' with 'play-video', width: 7, height: 9 -%}
                  {%- endif -%}
                </div>
              {% endunless %}
            </button>
          {%- endfor -%}
        </page-dots>
      </scroll-shadow>
    {%- endif -%}
  {%- endif -%}
</product-gallery>