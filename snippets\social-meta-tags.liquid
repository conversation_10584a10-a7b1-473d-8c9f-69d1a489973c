{%- comment -%}
Add Facebook and Pinterest Open Graph meta tags to product pages
for friendly Facebook sharing and Pinterest pinning.

More info Open Graph meta tags
- https://developers.facebook.com/docs/opengraph/using-objects/
- https://developers.pinterest.com/rich_pins/

Use the Facebook Open Graph Debugger for validation (and cache clearing)
- https://developers.facebook.com/tools/debug

Validate your Pinterest rich pins
- https://developers.pinterest.com/tools/url-debugger/
{%- endcomment -%}

{%- if request.page_type == 'product' -%}
  <meta property="og:type" content="product">
  <meta property="og:title" content="{{ product.title | strip_html | escape }}">
  <meta property="product:price:amount" content="{{ product.selected_or_first_available_variant.price | money_without_currency | strip_html | escape }}">
  <meta property="product:price:currency" content="{{ cart.currency.iso_code }}">
{%- elsif request.page_type == 'article' -%}
  <meta property="og:type" content="article">
  <meta property="og:title" content="{{ article.title | strip_html | escape }}">
{%- elsif request.page_type == 'collection' -%}
  <meta property="og:type" content="website">
  <meta property="og:title" content="{{ collection.title | strip_html | escape }}">
{%- else -%}
  <meta property="og:type" content="website">
  <meta property="og:title" content="{{ page_title | escape }}">
{%- endif -%}

{%- if page_image -%}
  <meta property="og:image" content="http:{{ page_image | image_url: width: 2048 }}">
  <meta property="og:image:secure_url" content="https:{{ page_image | image_url: width: 2048 }}">
  <meta property="og:image:width" content="{{ page_image.width }}">
  <meta property="og:image:height" content="{{ page_image.height }}">
{%- endif -%}

{%- if page_description -%}
  <meta property="og:description" content="{{ page_description | escape }}">
{%- endif -%}

<meta property="og:url" content="{{ canonical_url }}">
<meta property="og:site_name" content="{{ shop.name }}">

{%- comment -%}
This snippet renders meta data needed to create a Twitter card
for products and articles.

Your cards must be approved by Twitter to be activated
- https://dev.twitter.com/docs/cards/validation/validator

More information:
- https://dev.twitter.com/cards/types/summary
{%- endcomment -%}

<meta name="twitter:card" content="summary">

{%- if request.page_type == 'product' -%}
  <meta name="twitter:title" content="{{ product.title | escape }}">
  <meta name="twitter:description" content="{{ product.description | strip_html | truncatewords: 140, '' | escape }}">
{%- elsif request.page_type == 'article' -%}
  <meta name="twitter:title" content="{{ article.title }}">
  <meta name="twitter:description" content="{{ article.excerpt_or_content | strip_html | truncatewords: 140, '' | escape }}">
{%- elsif request.page_type == 'collection' -%}
  <meta name="twitter:title" content="{{ collection.title }}">
  <meta name="twitter:description" content="{{ collection.description | strip_html | truncatewords: 140, '' | escape }}">
{%- else -%}
  <meta name="twitter:title" content="{{ page_title | escape }}">
  <meta name="twitter:description" content="{{ page_description | default: page_title | escape }}">
{%- endif -%}

{%- if page_image -%}
  <meta name="twitter:image" content="https:{{ page_image | image_url: width: 1200, height: 1200, crop: 'center' }}">
  <meta name="twitter:image:alt" content="{{ page_image.alt | escape }}">
{%- endif -%}