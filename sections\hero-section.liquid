<section class="hero-section hero">
  <div class="container">
    <div class="content">
      <p class="recommendation">{{ section.settings.caption_text }} </p>
      <h1 class="headline">{{ section.settings.title_text }}</h1>
      <p class="subtext">{{ section.settings.description_text }} </p>
      <div class="cta-buttons">
        <a href="{{ section.settings.button_link_1 }} " class="btn shop-now" style="background-color: {{ section.settings.button_color_1 }}">{{ section.settings.button_text_1 }} </a>
        <a href="{{ section.settings.button_link_2 }}" class="btn view-categories" style="background-color: {{ section.settings.button_color_2 }}">{{ section.settings.button_text_2 }} </a>
      </div>
      <div class="trust-score">
        <h3><span class="score-heading">{{ section.settings.trust_score_text }}</span></h3>
        <p class="rating"><span class="rating-num">{{ section.settings.rating }}</span>  <span class="star">★</span> <span class="reviews">({{ section.settings.reviews }})</span></p>
      </div>
    </div>
    <div class="image-container">
      <img src="{{ section.settings.image | img_url: '1000x1000' }}" alt="Shoe image" />
    </div>
  </div>
</section>


{% schema %}
  {
  "name": "Custom Hero Section",
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "caption_text",
      "label": "Caption Text"
    },
    {
      "type": "text",
      "id": "title_text",
      "label": "Title Text"
    },
    {
      "type": "richtext",
      "id": "description_text",
      "label": "Description Text"
    },
    {
      "type": "color",
      "id": "button_color_1",
      "label": "Button Color 1"
    },
    {
      "type": "text",
      "id": "button_text_1",
      "label": "Button Text 1"
    },
    {
      "type": "url",
      "id": "button_link_1",
      "label": "Button Link 1"
    },
    {
      "type": "color",
      "id": "button_color_2",
      "label": "Button Color 2"
    },
    {
      "type": "text",
      "id": "button_text_2",
      "label": "Button Text 2"
    },
    {
      "type": "url",
      "id": "button_link_2",
      "label": "Button Link 2"
    },
    {
      "type": "text",
      "id": "trust_score_text",
      "label": "Score Text"
    },
    {
      "type": "text",
      "id": "rating",
      "label": "Rating"
    },
    {
      "type": "text",
      "id": "reviews",
      "label": "Reviews"
    }
  ],
  "presets": [
    {
      "name": "Custom Hero Section"
    }
  ]
}  
{% endschema %}

{% stylesheet %}
{% endstylesheet %}

{% javascript %}
{% endjavascript %}

