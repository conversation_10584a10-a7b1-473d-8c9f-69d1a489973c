{%- assign content_block = section.blocks | where: 'type', 'content' | first -%}

<div {% render 'surface', background: section.settings.background, background_gradient: section.settings.background_gradient, text_color: content_block.settings.background %}>
  {%- if section.settings.background_image != blank -%}
    {{- section.settings.background_image | image_url: width: section.settings.background_image.width | image_tag: widths: '200,300,400,500,600,800,1000,1200,1400,1600,1800,2000,2200,2400,2600,2800', class: 'object-fill' -}}
  {%- endif -%}

  <div class="container">
    <div class="password password--{{ section.settings.content_position }}">
      <div class="password__main">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'logo' -%}
              <h1 class="justify-self-center" {{ block.shopify_attributes }}>
                {%- if block.settings.image != blank -%}
                  {%- capture sizes -%}{{ block.settings.max_width }}px{%- endcapture -%}
                  {%- capture widths -%}{{ block.settings.max_width }}, {{ block.settings.max_width | times: 2 | at_most: block.settings.image.width }}{%- endcapture -%}
                  {%- capture style -%}max-width: {{ block.settings.max_width }}px{%- endcapture -%}

                  <span class="sr-only">{{ shop.name }}</span>
                  {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', style: style, sizes: sizes, widths: widths -}}
                {%- else -%}
                  <span class="h2">{{ shop.name }}</span>
                {%- endif -%}
              </h1>

            {%- when 'content' -%}
              <div class="v-stack gap-2 sm:gap-4">
                <div {% render 'surface', class: 'box rounded text-center', background_gradient: block.settings.background_gradient, background: block.settings.background, text_color: block.settings.text_color %}>
                  <div class="v-stack gap-6 sm:gap-8">
                    {%- if block.settings.title != blank or shop.password_message != blank -%}
                      <div class="prose">
                        {%- if block.settings.title != blank -%}
                          <h2 class="h3">{{ block.settings.title | escape }}</h2>
                        {%- endif -%}

                        {%- if shop.password_message != blank -%}
                          <p>{{ shop.password_message }}</p>
                        {%- endif -%}
                      </div>
                    {%- endif -%}

                    {%- if block.settings.show_newsletter_form -%}
                      {%- form 'customer', class: 'form form--tight' -%}
                        {%- if form.posted_successfully? -%}
                          {%- capture success_message -%}{{ 'general.newsletter.subscribed_successfully' | t }}{%- endcapture -%}
                          {%- render 'banner', status: 'success', content: success_message -%}
                        {%- else -%}
                          {%- if form.errors -%}
                            {%- capture error_message -%}{{ form.errors.translated_fields['email'] }} {{ form.errors.messages['email'] }}{%- endcapture -%}
                            {%- render 'banner', status: 'error', content: error_message -%}
                          {%- endif -%}

                          <input type="hidden" name="contact[tags]" value="newsletter">

                          {%- assign email_label = 'general.newsletter.email' | t -%}
                          {%- render 'input', type: 'email', name: 'contact[email]', label: email_label, required: true -%}

                          {%- assign submit_button = 'general.newsletter.notify_me' | t -%}
                          {%- render 'button', type: 'submit', content: submit_button, icon: 'picto-envelope', size: 'xl', stretch: true, background: block.settings.button_background, text_color: block.settings.button_text_color -%}
                        {%- endif -%}
                      {%- endform -%}
                    {%- endif -%}

                    <button type="button" class="justify-self-center" aria-controls="storefront-password-drawer" aria-expanded="false">
                      <span class="link text-sm text-subdued">{{ 'password.storefront_access.enter_password' | t }}</span>
                    </button>

                    {%- assign has_errors = false -%}

                    {%- capture password_form -%}
                      {%- form 'storefront_password', class: 'form' -%}
                        {%- if form.errors -%}
                          {%- assign has_errors = true -%}
                          {%- render 'banner', status: 'error', content: form.errors.messages['form'] -%}
                        {%- endif -%}

                        {%- assign password_label = 'password.storefront_access.password' | t -%}
                        {%- assign submit_label = 'password.storefront_access.enter_store' | t -%}

                        {%- render 'input', type: 'password', name: 'password', label: password_label, required: true -%}
                        {%- render 'button', type: 'submit', size: 'xl', content: submit_label, stretch: true -%}
                      {%- endform -%}
                    {%- endcapture -%}

                    <x-drawer id="storefront-password-drawer" class="password__storefront-drawer drawer" {% if has_errors %}open="immediate"{% endif %}>
                      <button is="close-button" aria-label="{{ 'general.accessibility.close' | t | escape }}">
                        {%- render 'icon' with 'close' -%}
                      </button>

                      <div class="password__storefront-form align-self-center">
                        <div class="prose text-center">
                          <div class="h3">{{ 'password.storefront_access.store_access' | t }}</div>
                          <p>{{ 'password.storefront_access.instructions' | t }}</p>
                        </div>

                        {{- password_form -}}
                      </div>
                    </x-drawer>
                  </div>
                </div>

                {%- capture social_media -%}{%- render 'social-media', size: 'sm' -%}{%- endcapture -%}

                {%- if block.settings.show_social_media and social_media != blank -%}
                  <div {% render 'surface', class: 'password__social-box rounded', background_gradient: block.settings.background_gradient, background: block.settings.background, text_color: block.settings.text_color %}>
                    <span class="shrink-0 text-center">{{ 'password.general.follow_us' | t }}</span>
                    {{- social_media -}}
                  </div>
                {%- endif -%}
              </div>
          {%- endcase -%}
        {%- endfor -%}
      </div>

      <div class="password__aside">
        <div class="h-stack gap-2 text-xs">
          {{- 'password.general.powered_by' | t -}}
          {%- render 'icon' with 'shopify-logo' -%}
        </div>

        <div class="h-stack gap-1 text-xs">
          {{- 'password.general.store_owner' | t -}}
          <a href="/admin" target="_blank">
            <span class="link">{{ 'password.general.login' | t }}</span>
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Password",
  "class": "shopify-section--main-password",
  "tag": "section",
  "blocks": [
    {
      "type": "logo",
      "name": "Logo",
      "limit": 1,
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "280 x 80px .png recommended"
        },
        {
          "type": "range",
          "id": "max_width",
          "min": 50,
          "max": 300,
          "step": 5,
          "unit": "px",
          "label": "Max width",
          "default": 140
        }
      ]
    },
    {
      "type": "content",
      "name": "Content",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "Opening soon"
        },
        {
          "type": "checkbox",
          "id": "show_social_media",
          "label": "Show social media",
          "info": "To configure social media, go to your social media settings."
        },
        {
          "type": "checkbox",
          "id": "show_newsletter_form",
          "label": "Show newsletter form",
          "info": "Customers who subscribe will have their email address added to the \"accepts marketing\" [customer list](/admin/customers?query=&accepts_marketing=1).",
          "default": true
        },
        {
          "type": "header",
          "content": "Colors",
          "info": "Gradient replaces solid colors when set."
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background",
          "default": "#ffffff"
        },
        {
          "type": "color_background",
          "id": "background_gradient",
          "label": "Background gradient"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text",
          "default": "#252627"
        },
        {
          "type": "color",
          "id": "button_background",
          "label": "Button background"
        },
        {
          "type": "color",
          "id": "button_text_color",
          "label": "Button text"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "image_picker",
      "id": "background_image",
      "label": "Background image",
      "info": "2000 x 1500px .jpg recommended"
    },
    {
      "type": "select",
      "id": "content_position",
      "label": "Content position",
      "options": [
        {
          "value": "start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "end",
          "label": "End"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Only used if no image is uploaded."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background",
      "default": "#252627"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    }
  ]
}
{% endschema %}