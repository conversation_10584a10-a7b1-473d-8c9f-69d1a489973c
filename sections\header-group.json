/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "type": "header",
  "name": "Header group",
  "sections": {
    "announcement-bar": {
      "type": "announcement-bar",
      "blocks": {
        "message": {
          "type": "message",
          "settings": {
            "text": "Today: Free Shipping on ALL orders",
            "emoji": "",
            "url": ""
          }
        },
        "message_UxjmYE": {
          "type": "message",
          "settings": {
            "text": "End of Season Sale: Don't miss out!",
            "emoji": "",
            "url": ""
          }
        },
        "message_mcx89M": {
          "type": "message",
          "settings": {
            "text": "45 days money-back-guarantee",
            "emoji": "",
            "url": ""
          }
        }
      },
      "block_order": [
        "message",
        "message_UxjmYE",
        "message_mcx89M"
      ],
      "settings": {
        "enable_sticky": true,
        "text_size": "text-xs",
        "navigation_mode": "arrows",
        "scrolling_speed": 15,
        "background": "#354632",
        "background_gradient": "",
        "text_color": "#ffffff"
      }
    },
    "header": {
      "type": "header",
      "settings": {
        "enable_sticky": true,
        "hide_on_scroll": false,
        "logo": "shopify://shop_images/Group_1000005680.png",
        "logo_max_width": 165,
        "logo_mobile_max_width": 130,
        "menu": "main-menu",
        "sidebar_menu": "",
        "secondary_menu": "",
        "menu_open_trigger": "click",
        "opening_from": "bottom",
        "layout": "logo_left_navigation_center",
        "show_social_media": true,
        "show_locale_selector": true,
        "show_country_selector": true,
        "show_country_flag": true,
        "show_country_name": false,
        "transparent_header_text_color": "#ffffff",
        "reduce_padding": true,
        "background_opacity": 100,
        "background_blur_radius": 0
      }
    }
  },
  "order": [
    "announcement-bar",
    "header"
  ]
}
