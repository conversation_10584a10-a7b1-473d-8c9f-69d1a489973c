/* Custom PDP styles for the new variant icon selector (scoped to .cvp)
   Keep this isolated so the existing theme design is untouched.  */

.cvp { --_radius: 10px; }

.cvp__legend { display:flex; align-items:center; gap:.5rem; margin:0 0 .5rem; }
.cvp__legend-label { font-weight:600; }
.cvp__legend-value { color: var(--text-color-subdued, #6b7280); font-size:.9em; }

.cvp__items { display:flex; gap:.5rem; flex-wrap:wrap; }

.cvp__tile {
  display:flex; align-items:center; gap:.5rem; cursor:pointer;
  background: var(--cvp-tile-bg);
  color: var(--cvp-text);
  border: 1px solid var(--cvp-border);
  border-radius: var(--_radius);
  padding: .5rem .6rem;
  min-width: 7.5rem;
  transition: border-color .2s ease, background .2s ease, box-shadow .2s ease;
}

.cvp__tile:hover { border-color: var(--cvp-border-selected); }
.cvp__tile.is-selected { background: var(--cvp-selected-bg); border-color: var(--cvp-border-selected); box-shadow: 0 0 0 2px color-mix(in srgb, var(--cvp-border-selected), transparent 70%); }
.cvp__tile.is-disabled { opacity:.5; cursor:pointer; }

/* Selected state via adjacent sibling so it updates with JS */
/* Selected state driven by real radios next to the label */
.cvp input[type="radio"][name^="option"]:checked + .cvp__tile { background: var(--cvp-selected-bg); border-color: var(--cvp-border-selected); box-shadow: 0 0 0 2px color-mix(in srgb, var(--cvp-border-selected), transparent 70%); color: var(--cvp-selected-text, var(--cvp-text)); }
.cvp input[type="radio"][name^="option"]:checked + .cvp__tile .cvp__compare { color: var(--cvp-selected-compare, var(--cvp-compare, #9ca3af)); }

 .cvp input[type="radio"][name^="option"]:checked + .cvp__tile .cvp__save { color: var(--cvp-selected-text, var(--cvp-text)); }

/* Disabled via class from theme */
.cvp__tile.is-disabled { opacity:.5; cursor:pointer; }

.cvp__img { width: 44px; height: 44px; object-fit: cover; border-radius: 6px; }
.cvp__text { display:flex; flex-direction:column; line-height:1.15; }
.cvp__label { font-weight:600; }
.cvp__price { color: var(--cvp-price); font-size:.9em; }

@media (max-width: 768px){
  .cvp__label, .cvp__price { font-size: 14px; }
  .cvp__compare, .pdp-checklist__text { font-size: 11px; }
  .pdp-checklist__item { padding: .3rem .45rem;}
  price-list.price-list.price-list--lg .text-lg, price-list.price-list.price-list--lg compare-at-price.text-subdued.line-through { font-size: 18px!important; }

  /* Mobile PDP font size optimizations */
  .product-info__title.h4 { font-size: 1.5rem !important; }
  .pdp-title__main { font-size: 1.4rem !important; }
  .pdp-title__accent { font-size: 1.2rem !important; }
  .pdp-feature-badge { font-size: 11px; }
  .pdp-rating-strip__label { font-size: 13px; }
  .pdp-rating-strip__badge { font-size: 11px; }
  .pdp-guarantee__title { font-size: 14px; }
  .pdp-guarantee__text { font-size: 12px; }
}
@media (max-width: 480px){
  .cvp__tile { width: calc(50% - .25rem); min-width: unset; }
}

/* Desktop font size optimizations */
@media (min-width: 769px){
  .product-info__title.h4 { font-size: 2rem !important; }
  .pdp-title__main { font-size: 1.8rem !important; }
  .pdp-title__accent { font-size: 1.5rem !important; }
  .pdp-feature-badge { font-size: 13px; }
  .pdp-rating-strip__label { font-size: 15px; }
  .pdp-rating-strip__badge { font-size: 13px; }
  .pdp-guarantee__title { font-size: 16px; }
  .pdp-guarantee__text { font-size: 14px; }
  .pdp-checklist__text { font-size: 14px; }
  .cvp__label, .cvp__price { font-size: 16px; }
  .cvp__compare { font-size: 14px; }
}



/* List layout to match design: stacked tiles with price on the right */
.cvp__group .cvp__items { flex-direction: column; }
.cvp__tile { justify-content: space-between; width: 100%; }
.cvp__text { flex: 1; }
.cvp__price { font-weight: 600; white-space: nowrap; text-align: right; display: flex; flex-direction: column; align-items: flex-end; line-height: 1.2; }
.cvp__compare { color: var(--cvp-compare, #9ca3af); text-decoration: line-through; margin-left: 0; margin-top: .125rem; font-weight: 400; font-size: .9em; }
.cvp__save { color: var(--text-color-subdued, #6b7280); font-size: .85em; }

/* Purchase type stack */
.cvp__purchase-type .cvp__items { display: flex; flex-direction: column; gap: .5rem; }
.cvp__purchase-type .cvp__tile { width: 100%; }

/* Size selection with visible radio buttons (like purchase type) */
.cvp__group:not(.cvp__purchase-type) .cvp__tile {
  display: flex;
  align-items: center;
  gap: .75rem;
  padding: .75rem;
}

.cvp__group:not(.cvp__purchase-type) input[type="radio"] {
  position: relative;
  width: 18px;
  height: 18px;
  margin: 0;
  opacity: 1;
  cursor: pointer;
  flex-shrink: 0;
}

.cvp__group:not(.cvp__purchase-type) .cvp__text {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: .25rem;
}


/* PDP Benefit check list */
.pdp-checklist { display:inline-flex; flex-direction:column; gap:.5rem; align-items:stretch; }
.pdp-checklist__item { display:flex; align-items:center; gap:.625rem; padding:.5rem .625rem; border-radius:10px; background: var(--check-row-bg, #f2f6f2); box-shadow: 0 0 0 1px rgba(0,0,0,.035) inset; width: 100%; }
.pdp-checklist__icon { display:inline-flex; align-items:center; justify-content:center; width:22px; height:22px; border-radius:50%; background: var(--check-icon-bg, #2e6a3d); }
.pdp-checklist__icon svg { display:block; width:18px; height:18px; }
.pdp-checklist__text { color: var(--check-text-color, #2e6a3d); font-weight:600; font-size: small; }


/* Feature badge grid (2 per row, up to 4) */
.pdp-feature-badges { display:grid; grid-template-columns:repeat(2, minmax(0,1fr)); gap:.5rem; width:max-content; max-width:100%; }
.pdp-feature-badge { display:flex; align-items:center; gap:.5rem; padding:.5rem .75rem; border-radius:10px; background: var(--fb-badge-bg, #eef3fb); color: var(--fb-text, #2a3a57); font-size: 12px; }
.pdp-feature-badge__icon { width:20px; height:20px; object-fit:contain; }
.pdp-feature-badge__text { color: var(--fb-text, #2a3a57); font-weight:600; }

/* Rating strip */
.pdp-rating-strip { display:flex; align-items:center; gap:.5rem; }
.pdp-rating-strip__content { display:flex; align-items:center; gap:.5rem; }
.pdp-rating-strip__stars { display:inline-flex; gap: 2px; line-height:0; }
.pdp-rating-strip__label { color: var(--rs-label, #2e6a3d); font-weight:600; }
.pdp-rating-strip__badge { background: var(--rs-badge-bg, #e7f29b); color: var(--rs-badge-text, #2e6a3d); padding:.25rem .5rem; border-radius:8px; font-weight:600; font-size: 12px; }

/* Rating format responsive display */
.pdp-rating-strip__desktop { display: inline; }
.pdp-rating-strip__mobile { display: none; }

@media (max-width: 768px) {
  .pdp-rating-strip__desktop { display: none; }
  .pdp-rating-strip__mobile { display: inline; }
}


/* Custom product title */
.pdp-title { margin: 0 0 .5rem; line-height: 1.1; }
.pdp-title__main { color: var(--title-main, inherit); }
.pdp-title__accent { color: var(--title-accent, currentColor); margin-left: .4ch; }

/* Price color customizations */
.product-info__price sale-price { color: var(--price-color, inherit); }
.product-info__price compare-at-price { color: var(--price-compare, #6b7280); }
.product-info__price .product-info__badge-list .badge--on-sale { background: var(--badge-bg, #e7f29b); color: var(--badge-text, #2e6a3d); }


/* Mobile layout adjustments for rating strip */
@media (max-width: 480px){
  .pdp-rating-strip { flex-direction: column; align-items: flex-start; gap: .25rem; }
  .pdp-rating-strip__badge { align-self: center; }
}


/* Mobile layout tweak: stars + label stacked, badge on the right */
@media (max-width: 480px){
  .pdp-rating-strip { display: grid; grid-template-columns: max-content 1fr; column-gap: .75rem; row-gap: .25rem; align-items: center; }
  .pdp-rating-strip__stars { grid-column: 1; grid-row: 1; }
  .pdp-rating-strip__label { grid-column: 1; grid-row: 2; }
  .pdp-rating-strip__badge { grid-column: 2; grid-row: 1 / span 2; justify-self: start; }
}

/* ATC content styling (icon + label + price) */
.buy-buttons .button .atc__content { display:inline-flex; align-items:center; gap:.3rem; }
.buy-buttons .button .atc__icon-img { width:18px; height:18px; display:inline-block; vertical-align:middle; }
.buy-buttons .button .atc__label { font-weight:600; }
.buy-buttons .button .atc__price { color: var(--atc-price, currentColor); font-weight:700; }
.buy-buttons .button .atc__compare { color: var(--atc-compare, currentColor); text-decoration: line-through; margin-left:.25rem; }


/* Guarantee box */
.pdp-guarantee { display:flex; gap:.75rem; align-items:flex-start; background: var(--g-bg, #fff); border: 1px solid var(--g-border, #e5e7eb); color: var(--g-color, inherit); border-radius: 10px; padding: .875rem; }
.pdp-guarantee__icon-img { width:40px; height:40px; }
.pdp-guarantee__title { font-weight:700; margin-bottom:.125rem; }
.pdp-guarantee__text { opacity:.9; }



/* Buy buttons: corner style via CSS variable */
.buy-buttons .button { border-radius: var(--btn-radius, 8px); }
.buy-buttons .shopify-payment-button__button { border-radius: var(--btn-radius, 8px); }

/* PDP Accordions */
.pdp-accordions { display:block; }
.pdp-accordions .pdp-acc { border:1px solid var(--acc-border, #e5e7eb); background: var(--acc-bg, #fff); border-radius: 12px; overflow:hidden; transition: border-color .2s ease, box-shadow .2s ease, background-color .2s ease; }
.pdp-accordions .pdp-acc + .pdp-acc { margin-top:.75rem; }
.pdp-accordions .pdp-acc[open] { border-color: var(--acc-border-active, var(--acc-border)); background: var(--acc-bg-active, var(--acc-bg)); box-shadow: 0 8px 24px rgba(0,0,0,.06); }
.pdp-acc__summary { display:flex; align-items:center; justify-content:space-between; padding:1rem 1.125rem; cursor:pointer; list-style:none; color: var(--acc-head, inherit); position:relative; font-weight:600; transition: color .2s ease, background-color .2s ease; }
.pdp-acc__summary::-webkit-details-marker { display:none; }
.pdp-acc__summary:hover { background: color-mix(in srgb, var(--acc-bg, #fff), black 4%); }
.pdp-acc__summary::after { content:''; width:10px; height:10px; flex:0 0 auto; border-right:2px solid var(--acc-icon, currentColor); border-bottom:2px solid var(--acc-icon, currentColor); transform: rotate(-45deg); transition: transform .2s ease, border-color .2s ease; margin-left:1rem; }
.pdp-acc[open] > .pdp-acc__summary { color: var(--acc-head-active, var(--acc-head)); }
.pdp-acc[open] > .pdp-acc__summary::after { transform: rotate(45deg); border-right-color: var(--acc-icon-active, var(--acc-icon)); border-bottom-color: var(--acc-icon-active, var(--acc-icon)); }
.pdp-acc__summary:focus-visible { outline: 2px solid var(--acc-border-active, #111); outline-offset: 2px; border-radius: 10px; }
.pdp-acc__content { padding: 0 1.125rem; color: var(--acc-body, inherit); opacity: 0; max-height: 0; overflow: hidden; transition: opacity .2s ease, max-height .25s ease, padding-top .2s ease, padding-bottom .2s ease; }
.pdp-acc[open] > .pdp-acc__content { color: var(--acc-body-active, var(--acc-body)); opacity: 1; max-height: 100vh; padding: .5rem 1.125rem 1rem; border-top: 1px solid color-mix(in srgb, var(--acc-border, #e5e7eb), transparent 30%); }

/* Product Quick Add Scroll Animations */
.product-quick-add__variant,
.product-quick-add__variant-mobile {
  transition: transform 0.3s ease-out;
}

.product-quick-add__variant.scroll-down,
.product-quick-add__variant-mobile.scroll-down {
  transform: translateY(0);
  animation: slideDown 0.3s ease-out;
}

.product-quick-add__variant.scroll-up,
.product-quick-add__variant-mobile.scroll-up {
  transform: translateY(-10px);
  animation: slideUp 0.3s ease-out;
}

@keyframes slideDown {
  from { transform: translateY(-10px); }
  to { transform: translateY(0); }
}

@keyframes slideUp {
  from { transform: translateY(0); }
  to { transform: translateY(-10px); }
}

