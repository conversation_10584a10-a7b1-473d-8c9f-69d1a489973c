/* 
 * Custom Typography System
 * Unified heading classes for all custom sections
 * Ensures consistent font sizes across <PERSON>, Aspects, Benefits, Reviews, Ingredients, and Image+Text sections
 */

/* Main Section Headings (H1 equivalent) */
.custom-heading-primary {
  font-size: clamp(32px, 6vw, 56px);
  line-height: 1.05;
  font-weight: 800;
  margin: 6px 0 10px;
}

/* Secondary Headings (H2 equivalent) */
.custom-heading-secondary {
  font-size: clamp(28px, 4vw, 44px);
  line-height: 1.1;
  font-weight: 800;
  margin: 0 0 12px;
}

/* Tertiary Headings (H3 equivalent) */
.custom-heading-tertiary {
  font-size: clamp(18px, 2vw, 24px);
  line-height: 1.2;
  font-weight: 800;
  margin: 0 0 10px;
}

/* Eyebrow/Caption Text */
.custom-eyebrow {
  font-weight: 800;
  letter-spacing: 0.08em;
  text-transform: uppercase;
  font-size: 14px;
  margin: 0 0 8px;
  opacity: 0.7;
}

/* Body Text */
.custom-body-text {
  font-size: 16px;
  line-height: 1.5;
  margin: 0 0 16px;
  opacity: 0.9;
}

/* Description Text (for section descriptions) */
.custom-desc-text {
  font-size: 16px;
  line-height: 1.5;
  margin: 0 0 16px;
  opacity: 0.85;
}

/* Card Text (for card content) */
.custom-card-text {
  font-size: 16px;
  line-height: 1.5;
  margin: 0 0 12px;
  opacity: 0.85;
}

/* Desktop-specific overrides */
@media (min-width: 1000px) {
  .custom-heading-primary {
    font-size: clamp(48px, 6vw, 56px);
  }
  
  .custom-heading-secondary {
    font-size: clamp(40px, 5vw, 56px);
  }
  
  .custom-heading-tertiary {
    font-size: clamp(20px, 2.5vw, 28px);
  }
  
  .custom-eyebrow {
    font-size: 16px;
  }
  
  .custom-body-text {
    font-size: 18px;
  }

  .custom-desc-text {
    font-size: 18px;
  }

  .custom-card-text {
    font-size: 16px;
  }
}

/* Section-specific heading variants */

/* Hero Section - Largest headings */
.custom-hero .custom-heading-primary {
  font-size: clamp(32px, 6vw, 56px);
}

/* Reviews Section - Uppercase italic style (same size as other secondary headings) */
.custom-reviews .custom-heading-secondary {
  text-transform: uppercase;
  font-style: italic;
  letter-spacing: 0.06em;
  /* Font size inherited from .custom-heading-secondary base class */
}

/* Benefits Section - Consistent with hero */
.custom-benefits .custom-heading-primary {
  font-size: clamp(32px, 5vw, 48px);
}

/* Ingredients Section - Centered style (same size as other secondary headings) */
.custom-ingredients .custom-heading-secondary {
  text-align: center;
  /* Font size inherited from .custom-heading-secondary base class */
}

/* Aspects Section - Same size as other secondary headings */
.custom-aspects .custom-heading-secondary {
  /* Font size inherited from .custom-heading-secondary base class */
}

/* Image+Text Section - Same size as other secondary headings */
.custom-image-text .custom-heading-secondary {
  /* Font size inherited from .custom-heading-secondary base class */
}

/* Utility classes for alignment */
.custom-text-left { text-align: left; }
.custom-text-center { text-align: center; }
.custom-text-right { text-align: right; }

/* Utility classes for font weights */
.custom-weight-normal { font-weight: 400; }
.custom-weight-semibold { font-weight: 600; }
.custom-weight-bold { font-weight: 700; }
.custom-weight-extrabold { font-weight: 800; }

/* Utility classes for font styles */
.custom-style-normal { font-style: normal; }
.custom-style-italic { font-style: italic; }

/* Color utilities (inherit from section variables) */
.custom-color-primary { color: rgb(var(--text-primary)); }
.custom-color-secondary { color: rgb(var(--text-primary) / 0.8); }
.custom-color-muted { color: rgb(var(--text-primary) / 0.6); }
.custom-color-accent { color: rgb(var(--accent-color, var(--text-primary))); }

/* Responsive spacing utilities */
.custom-spacing-xs { margin-bottom: 8px; }
.custom-spacing-sm { margin-bottom: 12px; }
.custom-spacing-md { margin-bottom: 16px; }
.custom-spacing-lg { margin-bottom: 24px; }
.custom-spacing-xl { margin-bottom: 32px; }

/* Line height utilities */
.custom-line-tight { line-height: 1.05; }
.custom-line-normal { line-height: 1.2; }
.custom-line-relaxed { line-height: 1.5; }
