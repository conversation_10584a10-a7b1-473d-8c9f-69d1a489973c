{%- if section.blocks.size > 0 -%}
  {%- render 'section-spacing-collapsing' -%}

  <style>
    #shopify-section-{{ section.id }} {
      --rich-text-max-width: {% if section.settings.content_width == 'small' %}650px{% elsif section.settings.content_width == 'medium' %}780px{% else %}100%{% endif %}
    }

  </style>

  <div {% render 'section-properties' %}>
    {%- assign text_position = section.settings.text_position -%}
    {%- assign blocks_before_divider = '' -%}
    {%- assign blocks_after_divider = '' -%}

    {%- for block in section.blocks -%}
      {%- capture block_content -%}
        {%- case block.type -%}
          {%- when 'subheading' -%}
            {%- if block.settings.text != blank -%}
              <p class="bold" {{ block.shopify_attributes }}>{{ block.settings.text | escape }}</p>
            {%- endif -%}

          {%- when 'heading' -%}
            {%- if block.settings.text != blank -%}
              <p class="{{ block.settings.heading_tag }} hyphenate" {{ block.shopify_attributes }}>
                {%- render 'styled-text', content: block.settings.text, text_color: block.settings.text_color, gradient: block.settings.gradient -%}
              </p>
            {%- endif -%}

          {%- when 'richtext' -%}
            {%- if block.settings.content != blank -%}
              <div {{ block.shopify_attributes }}>
                {{- block.settings.content -}}
              </div>
            {%- endif -%}

          {%- when 'page' -%}
            {%- if block.settings.page.content != blank -%}
              <div {{ block.shopify_attributes }}>
                {{- block.settings.page.content -}}
              </div>
            {%- endif -%}

          {%- when 'liquid' -%}
            {%- if block.settings.liquid != blank -%}
              <div {{ block.shopify_attributes }}>
                {{- block.settings.liquid -}}
              </div>
            {%- endif -%}

          {%- when 'image' -%}
            {%- if block.settings.image != blank -%}
              <div {{ block.shopify_attributes }} style="margin-block-start: 0">
                {%- capture image_style_attribute -%}{% if section.settings.text_position == 'center' %}margin-inline: auto;{% endif %}{%- endcapture -%}

                {%- if block.settings.width_mode == 'custom' -%}
                  {%- capture image_style_attribute -%}{{ image_style_attribute }} --image-max-width: {{ block.settings.max_width }}px; --image-mobile-max-width: {{ block.settings.mobile_max_width }}px{%- endcapture -%}
                  {%- capture image_sizes_attribute -%}(max-width: 699px) min({{ block.settings.mobile_max_width }}px, 100vw), min({{ block.settings.max_width }}px, 100vw){%- endcapture -%}
                {%- else -%}
                  {%- capture image_sizes_attribute -%}min(100vw, var(--page-width)){%- endcapture -%}
                {%- endif -%}

                {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', sizes: image_sizes_attribute, widths: '200,300,400,500,600,800,1000,1200,1400,1600', style: image_style_attribute -}}
              </div>
            {%- endif -%}

          {%- when 'button' -%}
            {%- if block.settings.text != blank -%}
              {% render 'button', content: block.settings.text, href: block.settings.url, size: block.settings.size, style: block.settings.style, background: block.settings.background, text_color: block.settings.text_color, block: block %}
            {%- endif -%}

          {%- when 'icon' -%}
            {%- capture icon -%}
              {%- if block.settings.custom_icon != blank -%}
                {%- capture sizes -%}{{ block.settings.icon_width }}px{%- endcapture -%}
                {%- capture widths -%}{{ block.settings.icon_width }}, {{ block.settings.icon_width | times: 2 | at_most: block.settings.custom_icon.width }}{%- endcapture -%}
                {%- capture style -%}--icon-max-width: {{ block.settings.icon_width }}px{%- endcapture -%}
                {{- block.settings.custom_icon | image_url: width: block.settings.custom_icon.width | image_tag: loading: 'lazy', sizes: sizes, widths: widths, style: style, class: 'image-icon' -}}
              {%- else -%}
                {%- render 'icon' with block.settings.icon, width: block.settings.icon_width, height: block.settings.icon_width -%}
              {%- endif -%}
            {%- endcapture -%}

            {%- if icon != blank -%}
              <div {{ block.shopify_attributes }}>
                {%- if block.settings.icon_background_type == 'none' -%}
                  <div {% render 'surface', text_color: block.settings.icon_color %}>
                    {{- icon -}}
                  </div>
                {%- else -%}
                  {%- capture surface_class -%}icon-block {% if block.settings.icon_background_type == 'circle' %}rounded-full{% endif %}{%- endcapture -%}
                  <div {% render 'surface', class: surface_class, background: block.settings.icon_background, text_color: block.settings.icon_color, background_fallback: 'bg-secondary' %}>
                    {{- icon -}}
                  </div>
                {%- endif -%}
              </div>
            {%- endif -%}

          {%- when 'divider' -%}
            {%- assign divider_index = forloop.index -%}
        {%- endcase -%}
      {%- endcapture -%}

      {%- if divider_index == blank or forloop.index < divider_index -%}
        {%- assign blocks_before_divider = blocks_before_divider | append: block_content -%}
      {%- else -%}
        {%- assign blocks_after_divider = blocks_after_divider | append: block_content -%}
      {%- endif -%}
    {%- endfor -%}

    {%- if blocks_before_divider != blank and blocks_after_divider != blank -%}
      <div class="split-rich-text">
        <div class="prose justify-items-{{ text_position }} text-{{ text_position }}">
          {{- blocks_before_divider -}}
        </div>

        <div class="prose justify-items-{{ text_position }} text-{{ text_position }}">
          {{- blocks_after_divider -}}
        </div>
      </div>
    {%- endif -%}

    <div class="rich-text justify-{{ text_position }} {% if blocks_before_divider != blank and blocks_after_divider != blank %}md:hidden{% endif %}">
      <div class="rich-text__wrapper">
        <div class="prose justify-items-{{ text_position }} text-{{ text_position }}">
          {{- blocks_before_divider -}}
          {{- blocks_after_divider -}}
        </div>
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "Rich text",
  "class": "shopify-section--rich-text",
  "tag": "section",
  "disabled_on": {
    "groups": ["header", "custom.overlay"]
  },
  "settings": [
  
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "select",
      "id": "content_width",
      "label": "Content width",
      "options": [
        {
          "value": "small",
          "label": "Small"
        },
        {
          "value": "medium",
          "label": "Medium"
        },
        {
          "value": "large",
          "label": "Large"
        }
      ],
      "default": "small"
    },
    {
      "type": "select",
      "id": "text_position",
      "label": "Text position",
      "options": [
        {
          "value": "start",
          "label": "Left"
        },
        {
          "value": "center",
          "label": "Center"
        },
        {
          "value": "end",
          "label": "Right"
        }
      ],
      "default": "center"
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "subheading",
      "name": "Subheading",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Text",
          "default": "Subheading"
        }
      ]
    },
    {
      "type": "heading",
      "name": "Heading",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Text",
          "default": "Heading"
        },
        {
          "type": "select",
          "id": "heading_tag",
          "label": "Style",
          "options": [
            {
              "value": "h1",
              "label": "X-Large"
            },
            {
              "value": "h2",
              "label": "Large"
            },
            {
              "value": "h3",
              "label": "Medium"
            },
            {
              "value": "h4",
              "label": "Small"
            },
            {
              "value": "h5",
              "label": "X-Small"
            },
            {
              "value": "h6",
              "label": "XX-Small"
            }
          ],
          "default": "h1"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text"
        },
        {
          "type": "color_background",
          "id": "gradient",
          "label": "Gradient"
        }
      ]
    },
    {
      "type": "richtext",
      "name": "Paragraph",
      "settings": [
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Use this text to share information about your brand with your customers. Describe a product, share announcements, or welcome customers to your store.</p>"
        }
      ]
    },
    {
      "type": "page",
      "name": "Page",
      "settings": [
        {
          "type": "page",
          "id": "page",
          "label": "Page"
        }
      ]
    },
    {
      "type": "liquid",
      "name": "Liquid",
      "settings": [
        {
          "type": "liquid",
          "id": "liquid",
          "label": "Liquid",
          "default": "<p>Write custom Liquid code to include widget or dynamic code.</p>"
        }
      ]
    },
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image",
          "info": "1600 x 800px .jpg recommended"
        },
        {
          "type": "radio",
          "id": "width_mode",
          "label": "Image width",
          "options": [
            {
              "value": "full_width",
              "label": "Full width"
            },
            {
              "value": "custom",
              "label": "Custom"
            }
          ],
          "default": "custom"
        },
        {
          "type": "range",
          "id": "max_width",
          "min": 100,
          "max": 1000,
          "step": 10,
          "unit": "px",
          "label": "Maximum width",
          "default": 600
        },
        {
          "type": "range",
          "id": "mobile_max_width",
          "min": 100,
          "max": 600,
          "step": 10,
          "unit": "px",
          "label": "Mobile maximum width",
          "default": 400
        }
      ]
    },
    {
      "type": "button",
      "name": "Button",
      "settings": [
        {
          "type": "select",
          "id": "style",
          "label": "Style",
          "options": [
            {
              "value": "outline",
              "label": "Outline"
            },
            {
              "value": "fill",
              "label": "Fill"
            }
          ],
          "default": "fill"
        },
        {
          "type": "select",
          "id": "size",
          "label": "Size",
          "options": [
            {
              "value": "sm",
              "label": "Small"
            },
            {
              "value": "base",
              "label": "Medium"
            },
            {
              "value": "lg",
              "label": "Large"
            },
            {
              "value": "xl",
              "label": "X-Large"
            }
          ],
          "default": "xl"
        },
        {
          "type": "text",
          "id": "text",
          "label": "Text",
          "default": "Button text"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text"
        }
      ]
    },
    {
      "type": "icon",
      "name": "Icon",
      "settings": [
        {
          "type": "select",
          "id": "icon",
          "label": "Icon",
          "options": [
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-percent",
              "label": "Percent",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-star",
              "label": "Star",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-building",
              "label": "Building",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-award-gift",
              "label": "Award gift",
              "group": "Shop"
            },
            {
              "value": "picto-happy",
              "label": "Happy",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-pin",
              "label": "Pin",
              "group": "Shipping"
            },
            {
              "value": "picto-timer",
              "label": "Timer",
              "group": "Shipping"
            },
            {
              "value": "picto-validation",
              "label": "Validation",
              "group": "Shipping"
            },
            {
              "value": "picto-truck",
              "label": "Truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return",
              "label": "Return",
              "group": "Shipping"
            },
            {
              "value": "picto-earth",
              "label": "Earth",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-profile",
              "label": "Secure profile",
              "group": "Payment & Security"
            },
            {
              "value": "picto-money",
              "label": "Money",
              "group": "Payment & Security"
            },
            {
              "value": "picto-recycle",
              "label": "Recycle",
              "group": "Ecology"
            },
            {
              "value": "picto-leaf",
              "label": "Leaf",
              "group": "Ecology"
            },
            {
              "value": "picto-tree",
              "label": "Tree",
              "group": "Ecology"
            },
            {
              "value": "picto-mobile-phone",
              "label": "Mobile phone",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Operator",
              "group": "Communication"
            },
            {
              "value": "picto-mailbox",
              "label": "Mailbox",
              "group": "Communication"
            },
            {
              "value": "picto-envelope",
              "label": "Envelope",
              "group": "Communication"
            },
            {
              "value": "picto-comment",
              "label": "Comment",
              "group": "Communication"
            },
            {
              "value": "picto-question",
              "label": "Question",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-at-sign",
              "label": "At sign",
              "group": "Tech"
            },
            {
              "value": "picto-camera",
              "label": "Camera",
              "group": "Tech"
            },
            {
              "value": "picto-wifi",
              "label": "WiFi",
              "group": "Tech"
            },
            {
              "value": "picto-bluetooth",
              "label": "Bluetooth",
              "group": "Tech"
            },
            {
              "value": "picto-printer",
              "label": "Printer",
              "group": "Tech"
            },
            {
              "value": "picto-smart-watch",
              "label": "Smart watch",
              "group": "Tech"
            },
            {
              "value": "picto-coffee",
              "label": "Coffee",
              "group": "Food & Drink"
            },
            {
              "value": "picto-burger",
              "label": "Burger",
              "group": "Food & Drink"
            },
            {
              "value": "picto-beer",
              "label": "Beer",
              "group": "Food & Drink"
            },
            {
              "value": "picto-target",
              "label": "Target",
              "group": "Other"
            },
            {
              "value": "picto-document",
              "label": "Document",
              "group": "Other"
            },
            {
              "value": "picto-jewelry",
              "label": "Jewelry",
              "group": "Other"
            },
            {
              "value": "picto-music",
              "label": "Music",
              "group": "Other"
            },
            {
              "value": "picto-file",
              "label": "File",
              "group": "Other"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Other"
            },
            {
              "value": "picto-stop",
              "label": "Stop",
              "group": "Other"
            }
          ],
          "default": "picto-coupon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon",
          "info": "240 x 240px .png recommended"
        },
        {
          "type": "range",
          "id": "icon_width",
          "min": 24,
          "max": 120,
          "step": 4,
          "unit": "px",
          "label": "Icon width",
          "default": 32
        },
        {
          "type": "select",
          "id": "icon_background_type",
          "label": "Icon background type",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "square",
              "label": "Square"
            },
            {
              "value": "circle",
              "label": "Circle"
            }
          ],
          "default": "circle"
        },
        {
          "type": "color",
          "id": "icon_background",
          "label": "Icon background"
        },
        {
          "type": "color",
          "id": "icon_color",
          "label": "Icon color"
        }
      ]
    },
    {
      "type": "divider",
      "name": "Column divider",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Content before and after this block are divided into two columns (on desktop only)."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Rich text",
      "blocks": [
        {
          "type": "subheading",
          "settings": {
            "text": "Subheading"
          }
        },
        {
          "type": "heading",
          "settings": {
            "text": "Heading",
            "heading_tag": "tw-text-h1"
          }
        },
        {
          "type": "richtext"
        }
      ]
    }
  ]
}
{% endschema %}