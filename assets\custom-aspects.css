/* Custom Aspects section (scoped via .custom-aspects namespace) */
.custom-aspects {
  background: rgb(var(--ca-bg));
  color: rgb(var(--ca-text))
}

.custom-aspects .ca-wrapper {
  max-width: 1340px;
  margin: 0 auto;
  padding: 40px 16px
}

.custom-aspects .ca-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  align-items: start
}

.custom-aspects .ca-mobile {
  display: block
}

.custom-aspects .ca-desktop {
  display: none
}

.custom-aspects .ca-block img {
  display: block;
  width: 100%;
  height: auto;
  border-radius: 16px
}

.custom-aspects .ca-eyebrow {
  font-weight: 800;
  letter-spacing: .06em;
  color: rgb(var(--ca-eyebrow));
  margin: 0 0 8px;
  font-size: 14px
}

.custom-aspects .ca-eyebrow p {
  margin: 0
}

/* Heading typography handled by .custom-heading-secondary class */

.custom-aspects .ca-heading .accent {
  color: rgb(var(--ca-accent))
}

.custom-aspects .ca-heading.is-normal {
  font-weight: 400
}

.custom-aspects .ca-desc {
  color: #5a554f;
  /* Typography handled by .custom-body-text class */
}

.custom-aspects .ca-align-left {
  text-align: left
}

.custom-aspects .ca-align-center {
  text-align: center
}

.custom-aspects .ca-align-right {
  text-align: right
}

.custom-aspects .ca-align-justify {
  text-align: justify
}

.custom-aspects .ca-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin: 0 0 20px;
  background: rgb(var(--ca-card-bg));
  border-radius: 16px;
  padding: 16px
}

.custom-aspects .ca-card {
  background: transparent;
  border-radius: 12px;
  padding: 8px
}

.custom-aspects .ca-card h3 {
  font-size: 16px;
  text-transform: uppercase;
  letter-spacing: .04em;
  margin: 0 0 8px
}

.custom-aspects .ca-check {
  display: flex;
  gap: 10px;
  margin: 8px 0;
  font-weight: 700
}

.custom-aspects .ca-check__text p {
  margin: 0
}

.custom-aspects .ca-check svg {
  flex: 0 0 18px;
  margin-top: 2px;
  fill: rgb(var(--ca-checkmark))
}

.custom-aspects .ca-icons {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  margin: 6px 0 20px
}

.custom-aspects .ca-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  text-align: center
}

.custom-aspects .ca-icon img {
  width: 36px;
  height: 36px;
  object-fit: contain
}

.custom-aspects .ca-icon .ca-icon-label {
  font-size: 12px;
  line-height: 1.15;
  font-weight: 800;
  text-transform: uppercase
}

.custom-aspects .ca-icon .ca-icon-sub {
  font-size: 12px;
  line-height: 1.15;
  text-transform: uppercase;
  color: #5a554f
}

.custom-aspects .ca-ingredients {
  font-size: 12px;
  color: #5a554f
}

.custom-aspects .ca-actions {
  margin-top: 8px;
  width: 100%;
}

/* Group wrapper for Nutrition + Ingredients */
.custom-aspects .ca-actions-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 8px
}

.custom-aspects .ca-button {
  background: rgb(var(--ca-button-bg));
  color: rgb(var(--ca-button-text));
  border: none;
  border-radius: 12px;
  padding: 14px 20px;
  font-weight: 700;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 8px;
  text-decoration: none;
  font-size: 16px
}

.custom-aspects .ca-button:hover {
  filter: brightness(.95)
}

/* modal */
.custom-aspects .ca-modal-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, .5);
  display: none;
  align-items: center;
  justify-content: center;
  padding: 24px;
  z-index: 9999
}

.custom-aspects .ca-modal-backdrop[aria-hidden="false"] {
  display: flex
}

.custom-aspects .ca-modal {
  background: #fff;
  border-radius: 16px;
  max-width: min(900px, 90vw);
  max-height: 90vh;
  overflow: auto;
  position: relative
}

.custom-aspects .ca-modal img {
  display: block;
  width: 100%;
  height: auto
}

.custom-aspects .ca-modal-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 999px;
  width: 36px;
  height: 36px;
  display: grid;
  place-items: center;
  cursor: pointer
}

/* Responsive */
@media(min-width:1000px) {
  .custom-aspects .ca-mobile {
    display: none
  }

  .custom-aspects .ca-desktop {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    align-items: start
  }

  .custom-aspects .ca-desktop .ca-col-image {
    grid-column: 1
  }

  .custom-aspects .ca-desktop .ca-col-content {
    grid-column: 2;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 520px
  }

  .custom-aspects.is-image-right .ca-desktop .ca-col-image {
    grid-column: 2
  }

  .custom-aspects.is-image-right .ca-desktop .ca-col-content {
    grid-column: 1;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    min-height: 520px
  }

  /* Reduce right image height (desktop) */
  .custom-aspects .ca-desktop .ca-col-image .ca-side-image {
    height: 520px
  }

  .custom-aspects .ca-desktop .ca-col-image .ca-side-image img {
    height: 100%;
    width: 100%;
    object-fit: cover
  }

  .custom-aspects .ca-grid {
    grid-template-columns: 1fr 1fr;
    gap: 20px
  }

  /* default: image left */
  .custom-aspects .ca-grid>.ca-block {
    grid-column: 2
  }


  .custom-aspects .ca-grid>.ca-side-image {
    grid-column: 1;
    grid-row: 1 / -1;
    align-self: start
  }

  .custom-aspects.is-image-right .ca-grid>.ca-block {
    grid-column: 1
  }

  .custom-aspects.is-image-right .ca-grid>.ca-side-image {
    grid-column: 2;
    grid-row: 1 / -1
  }

  /* Desktop heading size handled by unified typography */

  .custom-aspects .ca-icons {
    grid-template-columns: repeat(5, 1fr)
  }

  .custom-aspects .ca-button {
    min-width: 260px;
    font-size: 18px
  }

  .custom-aspects .ca-cards {
    grid-template-columns: 1fr 1fr
  }

  /* Nutrition + ingredients side-by-side */
  .custom-aspects .ca-actions-group {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 24px
  }
}

@media(max-width:999px) {
  .custom-aspects .ca-actions .ca-button {
    width: 100%
  }

  .custom-aspects .ca-actions-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px
  }
}