<style>
.customer-support {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    justify-items: center;
}

.custom-card {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: stretch;
    justify-content: center;
    width: 100%;
    height: auto;
    gap: 20px;
}

img.icon {
    padding:15px;
    width: 90px;
    justify-self: center;
}
.support-link {
    padding: 2.5em;
    display: block;
    width: 100%;
    height: 100%;
    border-radius: 15px;
}
}
.customer-support h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.support-block {
    word-wrap: break-word;
    text-decoration: none;
    color: #000;
    border: 2px solid black;
    border-radius: 15px;
    text-align: center;
    box-sizing: border-box;
    max-width: 400px;
}
.column-2{
  width: calc(50% - 20px);
}
.column-3{
  width: calc(33% - 20px);
}
.column-4{
  width: calc(25% - 20px);
}

.support-block h3 {
  font-size: 18px;
  color: #555;
  margin-bottom: 10px;
}

.support-block p {
  font-size: 16px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 0;
}

.support-block:hover {
  border-color: black;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.1);
  background: #ffe8d285;
}

.support-title{
  font-size:xxx-large;
  font-weight:bold;
  padding-bottom: 20px;
}

@media (max-width: 768px) {
  .customer-support {
    padding: 15px;
  }

  .customer-support h2 {
    font-size: 22px;
  }

  .support-block h3 {
    font-size: 16px;
  }

  .support-block p {
    font-size: 14px;
  }

  .support-block {
    width: 100%; /* Stacks blocks on smaller screens */
    padding: 1.5em;
  }
}

}</style>
<div class="customer-support">
  <p class="support-title">{{ section.settings.title }}</p>
  <div class="custom-card">  
  {% for block in section.blocks %}
    <div class="support-block column-{{ section.settings.columns }}" {{ block.shopify_attributes }}>
        <a class="support-link" href="{{ block.settings.link }} "> 
      {%- assign icon = block.settings.icon -%}
      <img src="{{ icon | image_url: width: icon.width }}" class="icon" loading="lazy">
      <h3><b>{{ block.settings.subtitle }}</b></h3>
      <p>{{ block.settings.content }}</p>
          </a>
    </div>
  {% endfor %}
    </div> 
</div>

{% schema %}
{
  "name": "Custom Card",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Custom Card"
    },
    {
      "type": "range",
      "id": "columns",
      "label": "Columns",
      "min": 2,
      "max": 4,
      "step": 1,
      "default": 2
    }
  ],
  "blocks": [
    {
      "type": "support_item",
      "name": "Card Item",
      "settings": [
        {
      "type": "image_picker",
      "id": "icon",
      "label": "Icon"
    },
        {
      "type": "url",
      "id": "link",
      "label": "Link"
    },
        {
          "type": "text",
          "id": "subtitle",
          "label": "Subtitle",
          "default": "Title"
        },
        {
          "type": "textarea",
          "id": "content",
          "label": "Content",
          "default": "Custom Card Content"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Custom Card",
      "category": "Custom Sections",
      "blocks": [
        {
          "type": "support_item",
          "settings": {
            "subtitle": "FAQ",
            "content": "Frequently Asked Questions!"
          }
        }
      ]
    }
  ]
}
{% endschema %}