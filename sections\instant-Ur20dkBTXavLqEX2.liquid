{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant iUr20dkBTXavLqEX2" data-instant-id="Ur20dkBTXavLqEX2" data-instant-version="3.0.4" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-Ur20dkBTXavLqEX2.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&amp;family=Inter:wght@400&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="iKAePG326UQHwxFfX">
    {%- liquid
      assign product_nwq2dHhIes5q2BjH_handle = 'hf-cloud'
      assign product_nwq2dHhIes5q2BjH = section.settings.product_nwq2dHhIes5q2BjH | default: all_products[product_nwq2dHhIes5q2BjH_handle]
      assign product_nwq2dHhIes5q2BjH_variant = product_nwq2dHhIes5q2BjH.selected_or_first_available_variant

      if product_nwq2dHhIes5q2BjH.selected_variant
        assign product_nwq2dHhIes5q2BjH_variant = product_nwq2dHhIes5q2BjH.selected_variant
      endif

      assign product_nwq2dHhIes5q2BjH_image = product_nwq2dHhIes5q2BjH_variant.featured_image | default: product_nwq2dHhIes5q2BjH.featured_image
    -%}
    <!--  -->
    {%- liquid
      assign loading = 'eager'
      assign fetchpriority = 'auto'
      if section.location == 'footer'
        assign loading = 'lazy'
      elsif section.location == 'header'
        assign fetchpriority = 'high'
      elsif section.location == 'template'
        if section.index == 1
          assign fetchpriority = 'high'
        elsif section.index > 2
          assign loading = 'lazy'
        endif
      endif
    -%}
    <form class="inwq2dHhIes5q2BjH" data-instant-type="container" id="inwq2dHhIes5q2BjH" data-instant-form-product-url="{{ product_nwq2dHhIes5q2BjH.url }}" data-instant-form-variant-id="{{ product_nwq2dHhIes5q2BjH_variant.id }}">
      <div class="iopsYcDyFl0W5TGFn" data-instant-type="container">
        <div class="iklaTau1Lg07KTnZ6" data-instant-type="container">
          <p data-instant-dynamic-content-source="TITLE" data-instant-type="text" class="iyPLce6pFzeOjaFSu">{{ product_nwq2dHhIes5q2BjH.title }}</p>
          <div data-instant-type="text" class="instant-rich-text iEysZ4Y8OXPKWXeVl">
            <div>{{ section.settings.text_EysZ4Y8OXPKWXeVl }}</div>
          </div>
          <div class="iO061LoIdN14oOsYv" data-instant-type="container">
            <div class="iv6eVKRIJYywxQ8RL" data-instant-type="container">
              {% if section.settings.image_v6eVKRIJYywxQ8RL and section.settings.image_v6eVKRIJYywxQ8RL != blank %}
                {{ section.settings.image_v6eVKRIJYywxQ8RL | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_v6eVKRIJYywxQ8RL.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
              {% else %}
                <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/oSopK4DagRW18jcX/5bfbd91939f6b831a45f1431b3d502d2166cf297.svg" width="96" height="17" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
              {% endif %}
            </div>
            <a as="p" data-instant-type="text" class="ivh8kNkzc94khDAZs" href="{{ section.settings.url_vh8kNkzc94khDAZs | default: '#shopify-section-template--23938854453588__instant_rse_fowu_c4_a_cffmqw_K3Fh8e' }}" rel="noopener noreferrer">
              <div class="instant-rich-text">
                <div>{{ section.settings.text_vh8kNkzc94khDAZs }}</div>
              </div>
            </a>
          </div>
        </div>
        <div class="i9aJcR0ptjgNCyxoT" data-instant-type="container">
          <div class="instant-slider-container iDRCy4M738EpKl9uB" data-instant-type="slider-container">
            <div class="irY2VxJSSnDTW3vai" data-instant-type="container">
              <div class="i84pixxGO5dNangBc" data-instant-type="container">
                <div class="i6POHFBL7dEdaYDeq" data-instant-type="container">
                  {% if section.settings.image_6POHFBL7dEdaYDeq and section.settings.image_6POHFBL7dEdaYDeq != blank %}
                    {{ section.settings.image_6POHFBL7dEdaYDeq | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_6POHFBL7dEdaYDeq.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/h7PX2ZN4BIKNk7xQ/icon-1.svg" width="33" height="32" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
                <div data-instant-type="text" class="instant-rich-text icc552PP6E4bWnHaX">
                  <div>{{ section.settings.text_cc552PP6E4bWnHaX }}</div>
                </div>
              </div>
              <div class="ibJfHP8hWZsh3nRWQ" data-instant-type="container">
                <div class="iLALp4vPH0c86lVc0" data-instant-type="container">
                  {% if section.settings.image_LALp4vPH0c86lVc0 and section.settings.image_LALp4vPH0c86lVc0 != blank %}
                    {{ section.settings.image_LALp4vPH0c86lVc0 | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_LALp4vPH0c86lVc0.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/CzN223eSTSEjU6f4/icon-2.svg" width="33" height="32" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
                <div data-instant-type="text" class="instant-rich-text ieeZIT8n9K5KifBNy">
                  <div>{{ section.settings.text_eeZIT8n9K5KifBNy }}</div>
                </div>
              </div>
              <div class="iLFVqUK5m0ZTp6qSn" data-instant-type="container">
                <div class="iw0DJBwwMYgCBKNKN" data-instant-type="container">
                  {% if section.settings.image_w0DJBwwMYgCBKNKN and section.settings.image_w0DJBwwMYgCBKNKN != blank %}
                    {{ section.settings.image_w0DJBwwMYgCBKNKN | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_w0DJBwwMYgCBKNKN.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                  {% else %}
                    <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/yJwlXGFVjM17Bl8p/icon-3.svg" width="33" height="32" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                  {% endif %}
                </div>
                <div data-instant-type="text" class="instant-rich-text iNTpHfSbG9OMxGluB">
                  <div>{{ section.settings.text_NTpHfSbG9OMxGluB }}</div>
                </div>
              </div>
            </div>
            <div class="iXCFmXrXO0EyS6uVm" data-instant-type="container">
              <script type="application/json" id="instant-slider-hHoXBUpNAjkOXn9h-params">
                { "breakpoints": { "0": { "speed": 300, "direction": "horizontal", "slidesPerView": 1, "freeMode": false, "centeredSlides": true, "centerInsufficientSlides": true, "spaceBetween": 0 }, "1025": { "speed": 300, "direction": "horizontal", "slidesPerView": 1, "freeMode": false, "centeredSlides": true, "centerInsufficientSlides": true, "spaceBetween": 0 } }, "navigation": false, "watchOverflow": true, "thumbs": { "slideThumbActiveClass": "instant-slider-thumb-active", "thumbsContainerClass": "instant-slider-thumbs" }, "a11y": { "enabled": true, "notificationClass": "instant-slider-notification" }, "containerModifierClass": "instant-slider-", "noSwipingClass": "instant-slider-no-swiping", "slideClass": "instant-slider-slide", "slideBlankClass": "instant-slider-slide-blank", "slideActiveClass": "instant-slider-slide-active", "slideVisibleClass": "instant-slider-slide-visible", "slideFullyVisibleClass": "instant-slider-slide-fully-visible", "slideNextClass": "instant-slider-slide-next", "slidePrevClass": "instant-slider-slide-prev", "wrapperClass": "instant-slider-wrapper", "lazyPreloaderClass": "instant-slider-lazy-preloader" }
              </script>
              <div class="instant-slider ihHoXBUpNAjkOXn9h" data-instant-slider-id="hHoXBUpNAjkOXn9h" id="hHoXBUpNAjkOXn9h" data-instant-type="slider">
                <div class="instant-slider-wrapper">
                  {%- liquid
                    assign images_without_variant = '' | split: ''
                    assign featured_image = product_nwq2dHhIes5q2BjH_variant.featured_image | default: product_nwq2dHhIes5q2BjH_image

                    for image in product_nwq2dHhIes5q2BjH.images
                      if image.id != featured_image.id
                        assign image_element = image | sort
                        assign images_without_variant = images_without_variant | concat: image_element
                      endif
                    endfor

                    assign images = featured_image | sort | concat: images_without_variant
                  -%}
                  {%- for image in images -%}
                    {%- if forloop.length > 0 -%}
                      {%- assign repeater_index_aOVqCX4HWeyzNkFZ = forloop.index0 -%}
                      <div class="instant-slider-slide iaOVqCX4HWeyzNkFZ" data-instant-type="slider-slide">
                        <div data-instant-type="image" class="i72i7bioSEqYTsqy0"><img alt="{{ image.alt }}" src="{{ image | image_url: width: 800 }}" data-instant-dynamic-content-source="REPEATER" data-instant-repeater-id="aOVqCX4HWeyzNkFZ" data-instant-repeater-index="{{ repeater_index_aOVqCX4HWeyzNkFZ }}" width="{{ image.width }}" height="{{ image.height }}" srcSet="{{ image | image_url: width: 360 }} 360w, {{ image | image_url: width: 640 }} 640w, {{ image | image_url: width: 750 }} 750w, {{ image | image_url: width: 828 }} 828w, {{ image | image_url: width: 1080 }} 1080w, {{ image | image_url: width: 1200 }} 1200w, {{ image | image_url: width: 1920 }} 1920w, {{ image | image_url: width: 2048 }} 2048w, {{ image | image_url: width: 3840 }} 3840w" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main"></div>
                      </div>
                    {%- endif -%}
                  {%- endfor -%}
                </div>
              </div>
            </div>
            <div class="instant-slider-container iqRuWdpHjfdzALX7g" data-instant-type="slider-container">
              <script type="application/json" id="instant-slider-uLRwQRFbCI9maAWq-params">
                { "breakpoints": { "0": { "speed": 300, "direction": "horizontal", "slidesPerView": 5, "freeMode": false, "centeredSlides": false, "centerInsufficientSlides": false, "spaceBetween": 16 }, "769": { "speed": 300, "direction": "horizontal", "slidesPerView": 6, "freeMode": false, "centeredSlides": false, "centerInsufficientSlides": false, "spaceBetween": 16 } }, "navigation": false, "watchOverflow": true, "watchSlidesProgress": true, "a11y": { "enabled": true, "notificationClass": "instant-slider-notification" }, "containerModifierClass": "instant-slider-", "noSwipingClass": "instant-slider-no-swiping", "slideClass": "instant-slider-slide", "slideBlankClass": "instant-slider-slide-blank", "slideActiveClass": "instant-slider-slide-active", "slideVisibleClass": "instant-slider-slide-visible", "slideFullyVisibleClass": "instant-slider-slide-fully-visible", "slideNextClass": "instant-slider-slide-next", "slidePrevClass": "instant-slider-slide-prev", "wrapperClass": "instant-slider-wrapper", "lazyPreloaderClass": "instant-slider-lazy-preloader" }
              </script>
              <div class="instant-slider instant-slider-thumbs iuLRwQRFbCI9maAWq" data-instant-slider-id="hHoXBUpNAjkOXn9h" id="uLRwQRFbCI9maAWq" data-instant-type="thumbnails">
                <div class="instant-slider-wrapper">
                  {%- liquid
                    assign images_without_variant = '' | split: ''
                    assign featured_image = product_nwq2dHhIes5q2BjH_variant.featured_image | default: product_nwq2dHhIes5q2BjH_image

                    for image in product_nwq2dHhIes5q2BjH.images
                      if image.id != featured_image.id
                        assign image_element = image | sort
                        assign images_without_variant = images_without_variant | concat: image_element
                      endif
                    endfor

                    assign images = featured_image | sort | concat: images_without_variant
                  -%}
                  {%- for image in images -%}
                    {%- if forloop.length > 0 -%}
                      {%- assign repeater_index_oM5lE5nrzMWVhrHD = forloop.index0 -%}
                      <div class="instant-slider-slide ioM5lE5nrzMWVhrHD" data-instant-type="slider-slide">
                        <div data-instant-type="image" class="iRvOL4yKWlBcEb3cN"><img alt="{{ image.alt }}" src="{{ image | image_url: width: 800 }}" data-instant-dynamic-content-source="REPEATER" data-instant-repeater-id="oM5lE5nrzMWVhrHD" data-instant-repeater-index="{{ repeater_index_oM5lE5nrzMWVhrHD }}" width="{{ image.width }}" height="{{ image.height }}" srcSet="{{ image | image_url: width: 360 }} 360w, {{ image | image_url: width: 640 }} 640w, {{ image | image_url: width: 750 }} 750w, {{ image | image_url: width: 828 }} 828w, {{ image | image_url: width: 1080 }} 1080w, {{ image | image_url: width: 1200 }} 1200w, {{ image | image_url: width: 1920 }} 1920w, {{ image | image_url: width: 2048 }} 2048w, {{ image | image_url: width: 3840 }} 3840w" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main"></div>
                      </div>
                    {%- endif -%}
                  {%- endfor -%}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="ivdpr3h3EEsaywNfS" data-instant-type="container">
          <div class="iTzBnRUH23yZYzv2c" data-instant-type="container">
            <div class="iFRA87McYslFoc0Np" data-instant-type="container">
              <div class="ipJUZ9ZyAsckbUcxl" data-instant-type="container">
                <p data-instant-dynamic-content-source="TITLE" data-instant-type="text" class="iSOBRheHSV1S1YOpP">{{ product_nwq2dHhIes5q2BjH.title }}</p>
                <div data-instant-type="text" class="instant-rich-text iqxl8OyL107OJS3Rq">
                  <div>{{ section.settings.text_qxl8OyL107OJS3Rq }}</div>
                </div>
              </div>
              <div class="i6N95VmEBk7EmnNiR" data-instant-type="container">
                <div class="irvT0nGojNCezpcp8" data-instant-type="container">
                  <p data-instant-dynamic-content-source="PRICE" data-instant-type="text" class="iG1yN7oUb8v0zxgLZ">
                    {%- liquid
                      assign variant_price = product_nwq2dHhIes5q2BjH_variant.price

                      if product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation
                        assign variant_price = product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation.price
                      endif
                    -%}
                    {{- variant_price | money -}}
                  </p>

                  {%- liquid
                    assign style = ''
                    assign compare_at_price = product_nwq2dHhIes5q2BjH_variant.compare_at_price
                    assign variant_price = product_nwq2dHhIes5q2BjH_variant.price

                    if product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation
                      assign compare_at_price = product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation.compare_at_price
                      assign variant_price = product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation.price
                    endif

                    if compare_at_price <= variant_price or compare_at_price == 0 or compare_at_price == null
                      assign style = 'style="display: none;"'
                    endif
                  -%}
                  <span
                    data-instant-dynamic-content-source="COMPARE_AT"
                    class="iUuzgBTw8A7iZszbL"
                    {{- style -}}
                  >
                    {{- compare_at_price | money -}}
                  </span>
                </div>
                <div class="iKeKBJf9qODFUGvUv" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iKjkD5Gr5eFdIE3JC">
                    <div>{{ section.settings.text_KjkD5Gr5eFdIE3JC }}</div>
                  </div>

                  {%- liquid
                    assign style = ''

                    assign compare_at_price = product_nwq2dHhIes5q2BjH_variant.compare_at_price
                    assign variant_price = product_nwq2dHhIes5q2BjH_variant.price

                    if product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation
                      assign compare_at_price = product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation.compare_at_price
                      assign variant_price = product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation.price
                    endif

                    assign saved_amount = compare_at_price | minus: variant_price | times: 100 | divided_by: compare_at_price

                    if compare_at_price <= variant_price or saved_amount <= 0 or compare_at_price == null
                      assign style = 'style="display: none;"'
                    endif
                  -%}
                  <span
                    data-instant-dynamic-content-source="SAVED_PERCENTAGE"
                    class="iFAsqBbW0nziSgF29"
                    {{- style -}}
                  >
                    {{- saved_amount -}}
                    %
                  </span>
                </div>
              </div>
            </div>
            <div class="ipT71XJGWQ5M4sLXa" data-instant-type="container">
              <div class="iSUVFCgurYrEM7V5K" data-instant-type="container">
                {% if section.settings.image_SUVFCgurYrEM7V5K and section.settings.image_SUVFCgurYrEM7V5K != blank %}
                  {{ section.settings.image_SUVFCgurYrEM7V5K | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_SUVFCgurYrEM7V5K.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                {% else %}
                  <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/oSopK4DagRW18jcX/5bfbd91939f6b831a45f1431b3d502d2166cf297.svg" width="96" height="17" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                {% endif %}
              </div>
              <a as="p" data-instant-type="text" class="iIY0dys8yMoyIlMcs" href="{{ section.settings.url_IY0dys8yMoyIlMcs | default: 'null' }}" rel="noopener noreferrer">
                <div class="instant-rich-text">
                  <div>{{ section.settings.text_IY0dys8yMoyIlMcs }}</div>
                </div>
              </a>
            </div>
            <div data-instant-type="text" class="instant-rich-text iDUapKBRBADtUki46">
              <div>{{ section.settings.text_DUapKBRBADtUki46 }}</div>
            </div>
            <div class="ip4gdvIXm178Nbnzc" data-instant-type="container">
              <div class="iAXmud4uHhQq7bpDA" data-instant-type="container">
                <div class="ijeX0SMyBIZjPuWya" data-instant-type="tabs-container">
                  <div class="ic8cXP63ZmQvJKn8v" data-instant-type="tabs-menu">
                    <button data-instant-state="inactive" data-instant-tab-id="M4h3kWUlDBmLOpLt" data-instant-tabs-id="jeX0SMyBIZjPuWya" id="41ZAL3DavALx4zQ6_M4h3kWUlDBmLOpLt" type="button" class="i41ZAL3DavALx4zQ6 i41ZAL3DavALx4zQ6" data-instant-type="tabs-trigger" content="[object Object]">{{ section.settings.tab_title_M4h3kWUlDBmLOpLt }}</button>
                    <button data-instant-state="inactive" data-instant-tab-id="a3HsQJj8ouYheJtM" data-instant-tabs-id="jeX0SMyBIZjPuWya" id="41ZAL3DavALx4zQ6_a3HsQJj8ouYheJtM" type="button" class="i41ZAL3DavALx4zQ6 i41ZAL3DavALx4zQ6" data-instant-type="tabs-trigger" content="[object Object]">{{ section.settings.tab_title_a3HsQJj8ouYheJtM }}</button>
                  </div>
                  <div class="imSgLP15JdMvb8yW7" data-instant-type="tabs-content">
                    <div class="iM4h3kWUlDBmLOpLt" id="M4h3kWUlDBmLOpLt" data-instant-type="tabs-pane">
                      <div class="idGZnuiEeutsU94vn" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
                        <div data-state="closed" class="iYSxqjBwpev3gjSom" data-instant-type="accordion-item">
                          <button class="igTWYpUiZ9KQ8rIXg" data-instant-type="accordion-header" type="button">
                            <div data-instant-type="text" class="instant-rich-text iemjmGVTdokxKYPbj">
                              <div>{{ section.settings.text_emjmGVTdokxKYPbj }}</div>
                            </div>
                            <div data-instant-type="text" class="instant-rich-text iQEhBWVqQU4tbF5Pl">
                              <div>{{ section.settings.text_QEhBWVqQU4tbF5Pl }}</div>
                            </div>
                          </button>
                          <div class="iJSRpaDWHFin4AjgS" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                            <div data-instant-type="image" class="iE0VOV3ayM0HxWJW7">
                              {% if section.settings.image_E0VOV3ayM0HxWJW7 and section.settings.image_E0VOV3ayM0HxWJW7 != blank %}
                                {{ section.settings.image_E0VOV3ayM0HxWJW7 | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_E0VOV3ayM0HxWJW7.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                              {% else %}
                                <img alt="Placeholder" src="https://cdn.instant.so/static/templates/assets/placeholder-image.svg" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image__fallback instant-image instant-image__main">
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="imnEedmRbt8OFGPTA" data-instant-type="container">
                        <div class="i3qrmARqg5t4NRvtf" data-instant-type="container">
                          <div
                            class="instant-custom-variant-picker iOWqhdjlJnHxANluu"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[0] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[0] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[0] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[0] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[0]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text ideoFxk43PbHWPJ7a">
                              <div>{{ section.settings.text_deoFxk43PbHWPJ7a }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker iiTrEbNjY8YAlqARN"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[1] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[1] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[1] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[1]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text inCjAnBW7G68vb2AT">
                              <div>{{ section.settings.text_nCjAnBW7G68vb2AT }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker ip0mttoEEyRWTUmak"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[2] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[2] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[2] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[2] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[2]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text i1zeKr14uG3aoikTi">
                              <div>{{ section.settings.text_1zeKr14uG3aoikTi }}</div>
                            </div>
                          </div>
                        </div>
                        <div class="io53UPrxB91Y90e1X" data-instant-type="container">
                          <div
                            class="instant-custom-variant-picker iVHRZ0cIaLHLCrECr"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[3] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[3] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[3] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[3] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[3]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text i4U2akJHmN045XQc9">
                              <div>{{ section.settings.text_4U2akJHmN045XQc9 }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker iAho5fHFKUIQFF1MM"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[4] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[4] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[4] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[4] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[4]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text iioDOlsc9x5XMuHik">
                              <div>{{ section.settings.text_ioDOlsc9x5XMuHik }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker iWzBkNUbM5BrhOoAE"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[5] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[5] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[5] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[5] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[5]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text i2cWs9yhSq38BrxbM">
                              <div>{{ section.settings.text_2cWs9yhSq38BrxbM }}</div>
                            </div>
                          </div>
                        </div>
                        <div class="ihJwsbvPdkrjvtTP7" data-instant-type="container">
                          <div
                            class="instant-custom-variant-picker i8dlxtSuCgsYJ3Lf8"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[6] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[6] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[6] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[6] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[6]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text ijr2VLUk2eSzxq686">
                              <div>{{ section.settings.text_jr2VLUk2eSzxq686 }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker ic28mbjdun29YNFR6"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[7] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[7] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[7] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[7] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[7]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text iku2f720MXyoq1Yyj">
                              <div>{{ section.settings.text_ku2f720MXyoq1Yyj }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker i1cwKk4OrwkimdrKF"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[8] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[8] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[8] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[8] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[8]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text iQoU1AVvBaPrdNYNU">
                              <div>{{ section.settings.text_QoU1AVvBaPrdNYNU }}</div>
                            </div>
                          </div>
                        </div>
                        <div class="itUaIA3nUbzKyfItQ" data-instant-type="container">
                          <div
                            class="instant-custom-variant-picker iGL5SjkZCcLv9smlM"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[9] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[9] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[9] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[9] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[9]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text idZWEaW0kEoYzU2UG">
                              <div>{{ section.settings.text_dZWEaW0kEoYzU2UG }}</div>
                            </div>
                          </div>
                          <div class="idCZeugyfMUPalE9P" data-instant-type="container"></div>
                          <div
                            class="instant-custom-variant-picker iz1MVKzK0ed6NK3JN"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[8] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[8] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[8] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[8] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[8]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          ></div>
                        </div>
                      </div>
                    </div>
                    <div class="ia3HsQJj8ouYheJtM" id="a3HsQJj8ouYheJtM" data-instant-type="tabs-pane">
                      <div class="i9hDvOwP7EbVxCOTc" data-instant-type="accordion-container" data-is-first-open="false" data-is-multi-open-enabled="false">
                        <div data-state="closed" class="i3YGrJHyIIRSDIIv4" data-instant-type="accordion-item">
                          <button class="iZd5nMOcF5QsWgqLG" data-instant-type="accordion-header" type="button">
                            <div data-instant-type="text" class="instant-rich-text iw86DfAWvsgagymc8">
                              <div>{{ section.settings.text_w86DfAWvsgagymc8 }}</div>
                            </div>
                            <div data-instant-type="text" class="instant-rich-text is80oL4e5nLrriCcx">
                              <div>{{ section.settings.text_s80oL4e5nLrriCcx }}</div>
                            </div>
                          </button>
                          <div class="i5bHWoAtYRvNx3CJS" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                            <div data-instant-type="image" class="iG1njL7FNgW2AAqIN">
                              {% if section.settings.image_G1njL7FNgW2AAqIN and section.settings.image_G1njL7FNgW2AAqIN != blank %}
                                {{ section.settings.image_G1njL7FNgW2AAqIN | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_G1njL7FNgW2AAqIN.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                              {% else %}
                                <img alt="Placeholder" src="https://cdn.instant.so/static/templates/assets/placeholder-image.svg" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image__fallback instant-image instant-image__main">
                              {% endif %}
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="ihCsqdhDFxUP0zBLP" data-instant-type="container">
                        <div class="iKLIEEvxJ4SB6G6xc" data-instant-type="container">
                          <div
                            class="instant-custom-variant-picker iKC13Ekx9hIKChC3y"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[0] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[0] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[0] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[0] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[0]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text iCHPOqEX4RbiEEi9h">
                              <div>{{ section.settings.text_CHPOqEX4RbiEEi9h }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker ipFqBjgXkOdI1tU5u"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[1] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[1] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[1] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[1]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text ixVJI2f0W4loLyCng">
                              <div>{{ section.settings.text_xVJI2f0W4loLyCng }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker iBBegLOYKTsOpZmUd"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[2] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[2] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[2] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[2] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[2]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text ifJ5f2u9L2r8ECTBk">
                              <div>{{ section.settings.text_fJ5f2u9L2r8ECTBk }}</div>
                            </div>
                          </div>
                        </div>
                        <div class="i1fDzbOq3ZXGJjSoa" data-instant-type="container">
                          <div
                            class="instant-custom-variant-picker iJH81dl0FsMyPInap"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[3] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[3] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[3] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[3] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[3]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text iBQzGLbcF0Dn0s01R">
                              <div>{{ section.settings.text_BQzGLbcF0Dn0s01R }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker ieqAAE3eaJFO3dhgy"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[4] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[4] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[4] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[4] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[4]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text iKE5WM4QbnkifY0AG">
                              <div>{{ section.settings.text_KE5WM4QbnkifY0AG }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker iRpVXRHQAPO26vSLn"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[3] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[3] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[3] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[3] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[3]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text izmUYcMG3ACL5G7Qn">
                              <div>{{ section.settings.text_zmUYcMG3ACL5G7Qn }}</div>
                            </div>
                          </div>
                        </div>
                        <div class="ibn50cQDiKSLnd2t9" data-instant-type="container">
                          <div
                            class="instant-custom-variant-picker i9E1rURCOzj57HSzt"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[6] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[6] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[6] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[6] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[6]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text iHoZ5pgPjFZto7Ida">
                              <div>{{ section.settings.text_HoZ5pgPjFZto7Ida }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker iD1xI33cGdfyftVwy"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[7] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[7] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[7] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[7] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[7]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text iYGoLnZlmkPUvf4JH">
                              <div>{{ section.settings.text_YGoLnZlmkPUvf4JH }}</div>
                            </div>
                          </div>
                          <div
                            class="instant-custom-variant-picker iTDvPYI0Bhis6yLCE"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[8] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[8] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[8] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[8] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[8]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text im0h7meJIlV6xWUhg">
                              <div>{{ section.settings.text_m0h7meJIlV6xWUhg }}</div>
                            </div>
                          </div>
                        </div>
                        <div class="iepp5j0KCgOp2A1y4" data-instant-type="container">
                          <div
                            class="instant-custom-variant-picker iwlyzplfMCsskpE3y"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[9] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[9] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[9] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[9] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[9]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          >
                            <div data-instant-type="text" class="instant-rich-text iLnqMGp7gd2fKGMr3">
                              <div>{{ section.settings.text_LnqMGp7gd2fKGMr3 }}</div>
                            </div>
                          </div>
                          <div class="iATgw2zYgHOWvGkux" data-instant-type="container"></div>
                          <div
                            class="instant-custom-variant-picker iK7LFuwx8cs5rAIhL"
                            data-instant-type="container"
                            data-instant-action-type="select-variant-option"
                            data-instant-option-value="
                              {%- liquid
                              	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Size' | first
                              	if selected_option == null
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              	endif
                              -%}{{- selected_option.values[8] | escape -}}
                            "
                            data-instant-option-name="{{ selected_option.name | escape }}"
                            data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[8] -%}active{%- else -%}inactive{%- endif -%}"
                            data-instant-disabled="
                              {%- liquid
                              	assign is_out_of_stock = false

                              	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                              		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                              		assign selected_variant = ''
                              		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                              		if selected_option == null
                              			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[1]
                              		endif

                              		for variant in product_nwq2dHhIes5q2BjH.variants
                              			if selected_option.position == 1 and variant.option1 == selected_option.values[8] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[8] and variant.option3 == selected_options[2]
                              				assign selected_variant = variant
                              			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[8]
                              				assign selected_variant = variant
                              			endif
                              		endfor

                              		if selected_variant != '' and selected_variant.available == false
                              			assign is_out_of_stock = true
                              		endif
                              	endunless
                              -%}{{- is_out_of_stock -}}
                            "
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="iVb90DpvJf2moihAo" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text iLNuAQdh0lyebiaWJ">
                  <div>{{ section.settings.text_LNuAQdh0lyebiaWJ }}</div>
                </div>
                <div class="iNSvFt7OsqjidPbJg" data-instant-type="container">
                  <div class="i0eRdHv7GJx02BIii" data-instant-type="container">
                    <div
                      class="instant-custom-variant-picker iDPpNQ75JdTWHoa2y"
                      data-instant-type="container"
                      data-instant-action-type="select-variant-option"
                      data-instant-option-value="
                        {%- liquid
                        	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Color' | first
                        	if selected_option == null
                        		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                        	endif
                        -%}{{- selected_option.values[0] | escape -}}
                      "
                      data-instant-option-name="{{ selected_option.name | escape }}"
                      data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[0] -%}active{%- else -%}inactive{%- endif -%}"
                      data-instant-disabled="
                        {%- liquid
                        	assign is_out_of_stock = false

                        	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                        		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                        		assign selected_variant = ''
                        		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                        		if selected_option == null
                        			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                        		endif

                        		for variant in product_nwq2dHhIes5q2BjH.variants
                        			if selected_option.position == 1 and variant.option1 == selected_option.values[0] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[0] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[0]
                        				assign selected_variant = variant
                        			endif
                        		endfor

                        		if selected_variant != '' and selected_variant.available == false
                        			assign is_out_of_stock = true
                        		endif
                        	endunless
                        -%}{{- is_out_of_stock -}}
                      "
                    >
                      <div class="ikfNUjtqP2f1nsqDg" data-instant-type="container"><div class="i3Ev1KRodbrbz3Emh" data-instant-type="container"></div></div>
                      <div data-instant-type="text" class="instant-rich-text iQ7cCcQ94yrVoSHA5">
                        <div>{{ section.settings.text_Q7cCcQ94yrVoSHA5 }}</div>
                      </div>
                    </div>
                    <div
                      class="instant-custom-variant-picker iIcTlVc3mQPzR8xpq"
                      data-instant-type="container"
                      data-instant-action-type="select-variant-option"
                      data-instant-option-value="
                        {%- liquid
                        	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Color' | first
                        	if selected_option == null
                        		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                        	endif
                        -%}{{- selected_option.values[1] | escape -}}
                      "
                      data-instant-option-name="{{ selected_option.name | escape }}"
                      data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[1] -%}active{%- else -%}inactive{%- endif -%}"
                      data-instant-disabled="
                        {%- liquid
                        	assign is_out_of_stock = false

                        	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                        		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                        		assign selected_variant = ''
                        		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                        		if selected_option == null
                        			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                        		endif

                        		for variant in product_nwq2dHhIes5q2BjH.variants
                        			if selected_option.position == 1 and variant.option1 == selected_option.values[1] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[1]
                        				assign selected_variant = variant
                        			endif
                        		endfor

                        		if selected_variant != '' and selected_variant.available == false
                        			assign is_out_of_stock = true
                        		endif
                        	endunless
                        -%}{{- is_out_of_stock -}}
                      "
                    >
                      <div class="iI6RzeNnm8suEoiLr" data-instant-type="container"><div class="i23FeZ7ROvhMveIE7" data-instant-type="container"></div></div>
                      <div data-instant-type="text" class="instant-rich-text im9T9W8shmxD4PhSg">
                        <div>{{ section.settings.text_m9T9W8shmxD4PhSg }}</div>
                      </div>
                    </div>
                    <div
                      class="instant-custom-variant-picker iXGJbMwlc19K56GF7"
                      data-instant-type="container"
                      data-instant-action-type="select-variant-option"
                      data-instant-option-value="
                        {%- liquid
                        	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Color' | first
                        	if selected_option == null
                        		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                        	endif
                        -%}{{- selected_option.values[2] | escape -}}
                      "
                      data-instant-option-name="{{ selected_option.name | escape }}"
                      data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[2] -%}active{%- else -%}inactive{%- endif -%}"
                      data-instant-disabled="
                        {%- liquid
                        	assign is_out_of_stock = false

                        	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                        		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                        		assign selected_variant = ''
                        		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                        		if selected_option == null
                        			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                        		endif

                        		for variant in product_nwq2dHhIes5q2BjH.variants
                        			if selected_option.position == 1 and variant.option1 == selected_option.values[2] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[2] and variant.option3 == selected_options[2]
                        				assign selected_variant = variant
                        			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[2]
                        				assign selected_variant = variant
                        			endif
                        		endfor

                        		if selected_variant != '' and selected_variant.available == false
                        			assign is_out_of_stock = true
                        		endif
                        	endunless
                        -%}{{- is_out_of_stock -}}
                      "
                    >
                      <div class="i4mSVJMcaQMMyCL3n" data-instant-type="container"><div class="ikJGh7PFkU1HAlOV5" data-instant-type="container"></div></div>
                      <div data-instant-type="text" class="instant-rich-text iYk0IK5WWamxNz16L">
                        <div>{{ section.settings.text_Yk0IK5WWamxNz16L }}</div>
                      </div>
                    </div>
                  </div>
                  {%- liquid
                    assign images_without_variant = '' | split: ''
                    assign featured_image = product_nwq2dHhIes5q2BjH_variant.featured_image | default: product_nwq2dHhIes5q2BjH_image

                    for image in product_nwq2dHhIes5q2BjH.images
                      if image.id != featured_image.id
                        assign image_element = image | sort
                        assign images_without_variant = images_without_variant | concat: image_element
                      endif
                    endfor

                    assign images = featured_image | sort | concat: images_without_variant
                  -%}
                  {%- for image in images -%}
                    {%- if forloop.length > 0 -%}
                      {%- assign repeater_index_dmWZAGcGeoZdfRqv = forloop.index0 -%}
                      <div class="idmWZAGcGeoZdfRqv" data-instant-type="container">
                        <div
                          class="instant-custom-variant-picker iDuDH7lbr1LYxQikC"
                          data-instant-type="container"
                          data-instant-action-type="select-variant-option"
                          data-instant-option-value="
                            {%- liquid
                            	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Color' | first
                            	if selected_option == null
                            		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                            	endif
                            -%}{{- selected_option.values[3] | escape -}}
                          "
                          data-instant-option-name="{{ selected_option.name | escape }}"
                          data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[3] -%}active{%- else -%}inactive{%- endif -%}"
                          data-instant-disabled="
                            {%- liquid
                            	assign is_out_of_stock = false

                            	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                            		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                            		assign selected_variant = ''
                            		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                            		if selected_option == null
                            			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                            		endif

                            		for variant in product_nwq2dHhIes5q2BjH.variants
                            			if selected_option.position == 1 and variant.option1 == selected_option.values[3] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                            				assign selected_variant = variant
                            			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[3] and variant.option3 == selected_options[2]
                            				assign selected_variant = variant
                            			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[3]
                            				assign selected_variant = variant
                            			endif
                            		endfor

                            		if selected_variant != '' and selected_variant.available == false
                            			assign is_out_of_stock = true
                            		endif
                            	endunless
                            -%}{{- is_out_of_stock -}}
                          "
                        >
                          <div class="iS47j1Dk988YVCNDg" data-instant-type="container"><div class="id9gY0RyfeJLaj8UZ" data-instant-type="container"></div></div>
                          <div data-instant-type="text" class="instant-rich-text i0EAzf5zKgt3p2i8W">
                            <div>{{ section.settings.text_0EAzf5zKgt3p2i8W }}</div>
                          </div>
                        </div>
                        <div
                          class="instant-custom-variant-picker iccv3psdtnf5HaJI8"
                          data-instant-type="container"
                          data-instant-action-type="select-variant-option"
                          data-instant-option-value="
                            {%- liquid
                            	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Color' | first
                            	if selected_option == null
                            		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                            	endif
                            -%}{{- selected_option.values[4] | escape -}}
                          "
                          data-instant-option-name="{{ selected_option.name | escape }}"
                          data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[4] -%}active{%- else -%}inactive{%- endif -%}"
                          data-instant-disabled="
                            {%- liquid
                            	assign is_out_of_stock = false

                            	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                            		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                            		assign selected_variant = ''
                            		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                            		if selected_option == null
                            			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                            		endif

                            		for variant in product_nwq2dHhIes5q2BjH.variants
                            			if selected_option.position == 1 and variant.option1 == selected_option.values[4] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                            				assign selected_variant = variant
                            			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[4] and variant.option3 == selected_options[2]
                            				assign selected_variant = variant
                            			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[4]
                            				assign selected_variant = variant
                            			endif
                            		endfor

                            		if selected_variant != '' and selected_variant.available == false
                            			assign is_out_of_stock = true
                            		endif
                            	endunless
                            -%}{{- is_out_of_stock -}}
                          "
                        >
                          <div class="iRFWdcrpJ06XavgN2" data-instant-type="container"><div class="iSJG6NMZUi88GjTK4" data-instant-type="container"></div></div>
                          <div data-instant-type="text" class="instant-rich-text iTAW88TvGfaoVo7FT">
                            <div>{{ section.settings.text_TAW88TvGfaoVo7FT }}</div>
                          </div>
                        </div>
                        <div
                          class="instant-custom-variant-picker iyvnz3WKLzlNzh85f"
                          data-instant-type="container"
                          data-instant-action-type="select-variant-option"
                          data-instant-option-value="
                            {%- liquid
                            	assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', 'Color' | first
                            	if selected_option == null
                            		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                            	endif
                            -%}{{- selected_option.values[5] | escape -}}
                          "
                          data-instant-option-name="{{ selected_option.name | escape }}"
                          data-instant-state="{%- if product_nwq2dHhIes5q2BjH_variant.options contains selected_option.values[5] -%}active{%- else -%}inactive{%- endif -%}"
                          data-instant-disabled="
                            {%- liquid
                            	assign is_out_of_stock = false

                            	unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify'
                            		assign selected_options = product_nwq2dHhIes5q2BjH_variant.options
                            		assign selected_variant = ''
                            		assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values | where : 'name', '{{ selected_option.name | escape }}' | first
                            		if selected_option == null
                            			assign selected_option = product_nwq2dHhIes5q2BjH.options_with_values[0]
                            		endif

                            		for variant in product_nwq2dHhIes5q2BjH.variants
                            			if selected_option.position == 1 and variant.option1 == selected_option.values[5] and variant.option2 == selected_options[1] and variant.option3 == selected_options[2]
                            				assign selected_variant = variant
                            			elsif selected_option.position == 2 and variant.option1 == selected_options[0] and variant.option2 == selected_option.values[5] and variant.option3 == selected_options[2]
                            				assign selected_variant = variant
                            			elsif selected_option.position == 3 and variant.option1 == selected_options[0] and variant.option2 == selected_options[1] and variant.option3 == selected_option.values[5]
                            				assign selected_variant = variant
                            			endif
                            		endfor

                            		if selected_variant != '' and selected_variant.available == false
                            			assign is_out_of_stock = true
                            		endif
                            	endunless
                            -%}{{- is_out_of_stock -}}
                          "
                        >
                          <div class="ilIxg5wgXGOrplB9Q" data-instant-type="container"><div class="ihuXf9PyHhm6cJchL" data-instant-type="container"></div></div>
                          <div data-instant-type="text" class="instant-rich-text ieid1O0EXNjiAhMCM">
                            <div>{{ section.settings.text_eid1O0EXNjiAhMCM }}</div>
                          </div>
                        </div>
                      </div>
                    {%- endif -%}
                  {%- endfor -%}
                </div>
              </div>
              <div class="ihYV3i6KOApTUTD85" data-instant-type="container">
                <div class="i0Sx6zxkTRBIXcmcT" data-instant-type="container">
                  {% assign app_blocks = section.blocks | where: 'type', '@app' %}
                  {% if app_blocks.size != 0 %}
                    <div schemaId="kix6rowxg4PiIHua" data-instant-type="app-island" class="ikix6rowxg4PiIHua">
                      {% for block in app_blocks %}
                        {% render block %}
                      {% endfor %}
                    </div>
                  {% endif %}
                  <a
                    class="icxXsN4n9mWxyPJcK maison-atc-button"
                    data-instant-type="container"
                    href="{{ routes.cart_url }}/{{ product_nwq2dHhIes5q2BjH_variant.id }}:1?storefront=true"
                    rel="noopener noreferrer"
                    data-instant-action-type="redirect-to-cart"
                    data-instant-action-id="{{ product_nwq2dHhIes5q2BjH.id }}"
                    data-instant-action-variant-id="{{ product_nwq2dHhIes5q2BjH_variant.id }}"
                    data-instant-disabled="
                      {%- unless product_nwq2dHhIes5q2BjH_variant.inventory_management != 'shopify' -%}
                      	{%- if product_nwq2dHhIes5q2BjH_variant.available == false -%}
                      		true
                      	{%-	endif -%}
                      {%- endunless -%}
                    "
                  >
                    <div data-instant-type="text" class="instant-rich-text im26D6ATGyyiu1e4G">
                      <div>{{ section.settings.text_m26D6ATGyyiu1e4G }}</div>
                    </div>

                    {%- liquid
                      assign style = ''

                      assign compare_at_price = product_nwq2dHhIes5q2BjH_variant.compare_at_price
                      assign variant_price = product_nwq2dHhIes5q2BjH_variant.price

                      if product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation
                        assign compare_at_price = product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation.compare_at_price
                        assign variant_price = product_nwq2dHhIes5q2BjH_variant.selected_selling_plan_allocation.price
                      endif

                      assign saved_amount = compare_at_price | minus: variant_price | times: 100 | divided_by: compare_at_price

                      if compare_at_price <= variant_price or saved_amount <= 0 or compare_at_price == null
                        assign style = 'style="display: none;"'
                      endif
                    -%}
                    <span
                      data-instant-dynamic-content-source="SAVED_PERCENTAGE"
                      class="ib72MX6aSJls8qwN6"
                      {{- style -}}
                    >
                      {{- saved_amount -}}
                      %
                    </span>

                    <div data-instant-type="text" class="instant-rich-text iy8eFlGRS8b02Om7z">
                      <div>{{ section.settings.text_y8eFlGRS8b02Om7z }}</div>
                    </div>
                    <div class="iZ4cVOlrL6iV5nSiq" data-instant-type="container">
                      {% if section.settings.image_Z4cVOlrL6iV5nSiq and section.settings.image_Z4cVOlrL6iV5nSiq != blank %}
                        {{ section.settings.image_Z4cVOlrL6iV5nSiq | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_Z4cVOlrL6iV5nSiq.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                      {% else %}
                        <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/vNZD6qx56xNG4BOB/c7ce2ae2d067b27a0bc9ca1bca80c59f67500c8d.svg" width="24" height="24" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                      {% endif %}
                    </div>
                  </a>
                </div>
                <div class="iFc3oSFwVMOCeAy7h" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iGZBgJK4dEJzrIGZ0">
                    <div>{{ section.settings.text_GZBgJK4dEJzrIGZ0 }}</div>
                  </div>
                  <div class="i2D2PAXFSsuIYIv68" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text irdLEcMzEi2b68bbQ">
                      <div>{{ section.settings.text_rdLEcMzEi2b68bbQ }}</div>
                    </div>
                    <div data-instant-type="text" class="instant-rich-text iUhlZ7v0H0yHZbbDN maison-country-flag">
                      <div>{{ section.settings.text_UhlZ7v0H0yHZbbDN }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="iCGv3AcyvmKMeZEg7" data-instant-type="container">
              <div class="iKXtUkJ15q6Jq4J8x" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text ihO2GYItxNJcSpXtA">
                  <div>{{ section.settings.text_hO2GYItxNJcSpXtA }}</div>
                </div>
              </div>
              <div class="iV1ULpRGHuM6TeoYd" data-instant-type="container">
                <div class="imSVDgodwCEqV3mqk" data-instant-type="container">
                  <div data-instant-type="image" class="iI05AYUno8BAdtT6U">
                    {% if section.settings.image_I05AYUno8BAdtT6U and section.settings.image_I05AYUno8BAdtT6U != blank %}
                      {{ section.settings.image_I05AYUno8BAdtT6U | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_I05AYUno8BAdtT6U.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                    {% else %}
                      <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/A1Rmro3sjK36dG7H/e7bb935c0ff031ddddb91b032a1bf5d9b47c5fa5.png" width="1100" height="1100" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
                    {% endif %}
                  </div>
                </div>
                <div class="iNir0yULM4HSP7Unp" data-instant-type="container">
                  <div class="igGVmoVLItH03zcwg" data-instant-type="container">
                    <div data-instant-type="text" class="instant-rich-text iAmMDkVQL0jWWJ70b">
                      <div>{{ section.settings.text_AmMDkVQL0jWWJ70b }}</div>
                    </div>
                  </div>
                  <div class="ijhbo3ahAewgpcsxl" data-instant-type="container">
                    <div class="i8ZXoQ3VebpUOflLN" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text iE03T3660KGrtmjmV">
                        <div>{{ section.settings.text_E03T3660KGrtmjmV }}</div>
                      </div>
                      <div data-instant-type="text" class="instant-rich-text iFZ9AGPXQUaJWfBoa">
                        <div>{{ section.settings.text_FZ9AGPXQUaJWfBoa }}</div>
                      </div>
                    </div>
                    <div class="iCypdclARSsu3JrIT" data-instant-type="container">
                      <div data-instant-type="text" class="instant-rich-text iiDKymoDR38o7GR73">
                        <div>{{ section.settings.text_iDKymoDR38o7GR73 }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="iOu1A25vPs1umo7hW" data-instant-type="accordion-container" data-is-first-open="true" data-is-multi-open-enabled="false">
            <div data-state="open" class="iH8OnU9bKVgITD5ss" data-instant-type="accordion-item">
              <button class="iSscSfYGfBGyeGxgS" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text ig9ksOC5Po3bE9LCg">
                  <div>{{ section.settings.text_g9ksOC5Po3bE9LCg }}</div>
                </div>
                <div data-instant-type="icon" class="iyD9IJWzDyUShBCc6">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="icPtaWO6jaoFkLpaE" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div class="iFljuoxg02n7mGOJj" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iQDJBuy2UXeeA3LqT">
                    <div>{{ section.settings.text_QDJBuy2UXeeA3LqT }}</div>
                  </div>
                  <div class="iCdl21XFviKuQJbnP" data-instant-type="container">
                    <div class="iOLkaOUlch932T37T" data-instant-type="container">
                      <div class="ibteapWtmZfxTyBlY" data-instant-type="container">
                        {% if section.settings.image_bteapWtmZfxTyBlY and section.settings.image_bteapWtmZfxTyBlY != blank %}
                          {{ section.settings.image_bteapWtmZfxTyBlY | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_bteapWtmZfxTyBlY.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                        {% else %}
                          <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/5djohSjHkK6vVLLT/7054654596433f74c31e0487b11e2af4a8b649bd.svg" width="49" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                        {% endif %}
                      </div>
                      <div data-instant-type="text" class="instant-rich-text i49seiPNlsOrxOVfH">
                        <div>{{ section.settings.text_49seiPNlsOrxOVfH }}</div>
                      </div>
                    </div>
                    <div class="iJbvGGt3oP2Ile4AK" data-instant-type="container">
                      <div class="i9F6kDlnBzVpd72sS" data-instant-type="container">
                        {% if section.settings.image_9F6kDlnBzVpd72sS and section.settings.image_9F6kDlnBzVpd72sS != blank %}
                          {{ section.settings.image_9F6kDlnBzVpd72sS | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_9F6kDlnBzVpd72sS.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                        {% else %}
                          <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/9v4cki5YE7UNxsZW/a1a359f8f8f14b30a77fa6e5dbdd588563901fa6.svg" width="48" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                        {% endif %}
                      </div>
                      <div data-instant-type="text" class="instant-rich-text ix7AtIFBadXSDbEba">
                        <div>{{ section.settings.text_x7AtIFBadXSDbEba }}</div>
                      </div>
                    </div>
                    <div class="igkTYqX3Zoqxvkgg1" data-instant-type="container">
                      <div class="iEqgDcrwS10DWFHwI" data-instant-type="container">
                        {% if section.settings.image_EqgDcrwS10DWFHwI and section.settings.image_EqgDcrwS10DWFHwI != blank %}
                          {{ section.settings.image_EqgDcrwS10DWFHwI | image_url: width: 1280 | image_tag: class: 'instant-fill instant-image__fill', alt: section.settings.image_EqgDcrwS10DWFHwI.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
                        {% else %}
                          <img alt="" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/MHvi0r133dJa69XB/ea9b201c4c9f15d7613d00bc9a6778094105b8d8.svg" width="49" height="48" class="instant-fill instant-image__fill" style="object-fit:cover" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}">
                        {% endif %}
                      </div>
                      <div data-instant-type="text" class="instant-rich-text iIyN66s5r4ruR0shN">
                        <div>{{ section.settings.text_IyN66s5r4ruR0shN }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div data-state="closed" class="i5d0LjxrUS1nHRAzi" data-instant-type="accordion-item">
              <button class="ik96erk1xD5ZfUZhY" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text ibHbYrEd9CgiePn3c">
                  <div>{{ section.settings.text_bHbYrEd9CgiePn3c }}</div>
                </div>
                <div data-instant-type="icon" class="iEBOKmanhH5YfsEm8">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="ipm7ypy8Z7w0AtajE" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div class="iNpp7lYpPR4z58dCd" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text iQb3FXcbtBuhvg88g">
                    <div>{{ section.settings.text_Qb3FXcbtBuhvg88g }}</div>
                  </div>
                </div>
              </div>
            </div>
            <div data-state="closed" class="iPln0nre4XEyDV3Jx" data-instant-type="accordion-item">
              <button class="i8ofamjMlKyjCnGRo" data-instant-type="accordion-header" type="button">
                <div data-instant-type="text" class="instant-rich-text ibNLrojOn8WeW7BA6">
                  <div>{{ section.settings.text_bNLrojOn8WeW7BA6 }}</div>
                </div>
                <div data-instant-type="icon" class="iG5nFgQZgZlUucef8">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 256 256" class="instant-icon">
                    <title>caret-down</title><path d="M213.66,101.66l-80,80a8,8,0,0,1-11.32,0l-80-80A8,8,0,0,1,53.66,90.34L128,164.69l74.34-74.35a8,8,0,0,1,11.32,11.32Z"></path>
                  </svg>
                </div>
              </button>
              <div class="iArleUp5V17g7MgNn" data-instant-type="accordion-content" style="--instant-accordion-content-height:auto;--instant-accordion-content-width:auto">
                <div class="icDLp6AgwA1YvaB0u" data-instant-type="container">
                  <div data-instant-type="text" class="instant-rich-text ieG21ZmykNzp4a0dG">
                    <div>{{ section.settings.text_eG21ZmykNzp4a0dG }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <script type="application/json" id="variants__nwq2dHhIes5q2BjH--{{ section.id }}">
        {{ product_nwq2dHhIes5q2BjH.variants | json }}
      </script>
      <script type="application/json" id="options__nwq2dHhIes5q2BjH--{{ section.id }}">
        {{ product_nwq2dHhIes5q2BjH.options_with_values | json }}
      </script>
    </form>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.4",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison Product | Variants",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "product",
      "id": "product_nwq2dHhIes5q2BjH",
      "label": "Product Header / 5 /"
    },
    {
      "type": "richtext",
      "id": "text_EysZ4Y8OXPKWXeVl",
      "label": "Text",
      "default": "<p>Perfect for: ‘Everyday, Walking, Travel, Workouts’</p>"
    },
    {
      "type": "image_picker",
      "id": "image_v6eVKRIJYywxQ8RL",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_vh8kNkzc94khDAZs",
      "label": "Review text mobile",
      "default": "<p>6,627 Reviews</p>"
    },
    {
      "type": "url",
      "id": "url_vh8kNkzc94khDAZs",
      "label": "Review text mobile URL"
    },
    {
      "type": "image_picker",
      "id": "image_6POHFBL7dEdaYDeq",
      "label": "Smiley-Smile--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_cc552PP6E4bWnHaX",
      "label": "Text",
      "default": "<p>Supports your feet</p>"
    },
    {
      "type": "image_picker",
      "id": "image_LALp4vPH0c86lVc0",
      "label": "Drugs-Cannabis--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_eeZIT8n9K5KifBNy",
      "label": "Text",
      "default": "<p>All day comfortable</p>"
    },
    {
      "type": "image_picker",
      "id": "image_w0DJBwwMYgCBKNKN",
      "label": "Cash-Shield--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_NTpHfSbG9OMxGluB",
      "label": "Text",
      "default": "<p>60 Day Money Back</p>"
    },
    {
      "type": "richtext",
      "id": "text_qxl8OyL107OJS3Rq",
      "label": "Text",
      "default": "<p>Perfect for: ‘Everyday, Walking, Travel, Workouts’</p>"
    },
    {
      "type": "richtext",
      "id": "text_KjkD5Gr5eFdIE3JC",
      "label": "YOU SAVE 70%",
      "default": "<p>YOU SAVE</p>"
    },
    {
      "type": "image_picker",
      "id": "image_SUVFCgurYrEM7V5K",
      "label": "Stars"
    },
    {
      "type": "richtext",
      "id": "text_IY0dys8yMoyIlMcs",
      "label": "Reviews text",
      "default": "<p>6,627 Reviews</p>"
    },
    {
      "type": "url",
      "id": "url_IY0dys8yMoyIlMcs",
      "label": "Reviews text URL"
    },
    {
      "type": "richtext",
      "id": "text_DUapKBRBADtUki46",
      "label": "Text",
      "default": "<ul><li><p>Relieves pressure on feet and joints</p></li><li><p>Developed with orthopedists</p></li><li><p>Free US Shipping</p></li></ul>"
    },
    {
      "type": "text",
      "id": "tab_title_M4h3kWUlDBmLOpLt",
      "label": "Women Size",
      "default": "Women Size"
    },
    {
      "type": "richtext",
      "id": "text_emjmGVTdokxKYPbj",
      "label": "Variant",
      "default": "<p>Choose Your Size</p>"
    },
    {
      "type": "richtext",
      "id": "text_QEhBWVqQU4tbF5Pl",
      "label": "Variant",
      "default": "<p>CLICK FOR SIZE CHART</p>"
    },
    {
      "type": "image_picker",
      "id": "image_E0VOV3ayM0HxWJW7",
      "label": "Image"
    },
    {
      "type": "richtext",
      "id": "text_deoFxk43PbHWPJ7a",
      "label": "Button",
      "default": "<p>6</p>"
    },
    {
      "type": "richtext",
      "id": "text_nCjAnBW7G68vb2AT",
      "label": "Button",
      "default": "<p>7</p>"
    },
    {
      "type": "richtext",
      "id": "text_1zeKr14uG3aoikTi",
      "label": "Button",
      "default": "<p>7.5</p>"
    },
    {
      "type": "richtext",
      "id": "text_4U2akJHmN045XQc9",
      "label": "Button",
      "default": "<p>8</p>"
    },
    {
      "type": "richtext",
      "id": "text_ioDOlsc9x5XMuHik",
      "label": "Button",
      "default": "<p>9</p>"
    },
    {
      "type": "richtext",
      "id": "text_2cWs9yhSq38BrxbM",
      "label": "Button",
      "default": "<p>10</p>"
    },
    {
      "type": "richtext",
      "id": "text_jr2VLUk2eSzxq686",
      "label": "Button",
      "default": "<p>11</p>"
    },
    {
      "type": "richtext",
      "id": "text_ku2f720MXyoq1Yyj",
      "label": "Button",
      "default": "<p>12</p>"
    },
    {
      "type": "richtext",
      "id": "text_QoU1AVvBaPrdNYNU",
      "label": "Button",
      "default": "<p>13</p>"
    },
    {
      "type": "richtext",
      "id": "text_dZWEaW0kEoYzU2UG",
      "label": "Button",
      "default": "<p>14</p>"
    },
    {
      "type": "text",
      "id": "tab_title_a3HsQJj8ouYheJtM",
      "label": "Men Size",
      "default": "Men Size"
    },
    {
      "type": "richtext",
      "id": "text_w86DfAWvsgagymc8",
      "label": "Variant",
      "default": "<p>Choose Your Size</p>"
    },
    {
      "type": "richtext",
      "id": "text_s80oL4e5nLrriCcx",
      "label": "Variant",
      "default": "<p>CLICK FOR SIZE CHART</p>"
    },
    {
      "type": "image_picker",
      "id": "image_G1njL7FNgW2AAqIN",
      "label": "Image"
    },
    {
      "type": "richtext",
      "id": "text_CHPOqEX4RbiEEi9h",
      "label": "Button",
      "default": "<p>4</p>"
    },
    {
      "type": "richtext",
      "id": "text_xVJI2f0W4loLyCng",
      "label": "Button",
      "default": "<p>5</p>"
    },
    {
      "type": "richtext",
      "id": "text_fJ5f2u9L2r8ECTBk",
      "label": "Button",
      "default": "<p>5.5</p>"
    },
    {
      "type": "richtext",
      "id": "text_BQzGLbcF0Dn0s01R",
      "label": "Button",
      "default": "<p>6</p>"
    },
    {
      "type": "richtext",
      "id": "text_KE5WM4QbnkifY0AG",
      "label": "Button",
      "default": "<p>7</p>"
    },
    {
      "type": "richtext",
      "id": "text_zmUYcMG3ACL5G7Qn",
      "label": "Button",
      "default": "<p>8</p>"
    },
    {
      "type": "richtext",
      "id": "text_HoZ5pgPjFZto7Ida",
      "label": "Button",
      "default": "<p>9</p>"
    },
    {
      "type": "richtext",
      "id": "text_YGoLnZlmkPUvf4JH",
      "label": "Button",
      "default": "<p>10</p>"
    },
    {
      "type": "richtext",
      "id": "text_m0h7meJIlV6xWUhg",
      "label": "Button",
      "default": "<p>11</p>"
    },
    {
      "type": "richtext",
      "id": "text_LnqMGp7gd2fKGMr3",
      "label": "Button",
      "default": "<p>12</p>"
    },
    {
      "type": "richtext",
      "id": "text_LNuAQdh0lyebiaWJ",
      "label": "Variant",
      "default": "<p>Choose Your Color</p>"
    },
    {
      "type": "richtext",
      "id": "text_Q7cCcQ94yrVoSHA5",
      "label": "Button",
      "default": "<p>BROWN</p>"
    },
    {
      "type": "richtext",
      "id": "text_m9T9W8shmxD4PhSg",
      "label": "Button",
      "default": "<p>Blue</p>"
    },
    {
      "type": "richtext",
      "id": "text_Yk0IK5WWamxNz16L",
      "label": "Button",
      "default": "<p>GREEN</p>"
    },
    {
      "type": "richtext",
      "id": "text_0EAzf5zKgt3p2i8W",
      "label": "Button",
      "default": "<p>BLACK</p>"
    },
    {
      "type": "richtext",
      "id": "text_TAW88TvGfaoVo7FT",
      "label": "Button",
      "default": "<p>PURPLE</p>"
    },
    {
      "type": "richtext",
      "id": "text_eid1O0EXNjiAhMCM",
      "label": "Button",
      "default": "<p>White</p>"
    },
    {
      "type": "richtext",
      "id": "text_m26D6ATGyyiu1e4G",
      "label": "Button",
      "default": "<p>Add To Cart - </p>"
    },
    {
      "type": "richtext",
      "id": "text_y8eFlGRS8b02Om7z",
      "label": "Button",
      "default": "<p>Off</p>"
    },
    {
      "type": "image_picker",
      "id": "image_Z4cVOlrL6iV5nSiq",
      "label": "Icon / cart-add"
    },
    {
      "type": "richtext",
      "id": "text_GZBgJK4dEJzrIGZ0",
      "label": "Free Shipping on Your Order!",
      "default": "<p>Free Shipping on Your Order! </p>"
    },
    {
      "type": "richtext",
      "id": "text_rdLEcMzEi2b68bbQ",
      "label": "Get a refund in one click.",
      "default": "<p>Shipping to</p>"
    },
    {
      "type": "richtext",
      "id": "text_UhlZ7v0H0yHZbbDN",
      "label": "Get a refund in one click.",
      "default": "<p></p>"
    },
    {
      "type": "richtext",
      "id": "text_hO2GYItxNJcSpXtA",
      "label": "FREE WITH YOUR ORDER",
      "default": "<p>FREE WITH TODAY’S ORDER:</p>"
    },
    {
      "type": "image_picker",
      "id": "image_I05AYUno8BAdtT6U",
      "label": "Ebook Image"
    },
    {
      "type": "richtext",
      "id": "text_AmMDkVQL0jWWJ70b",
      "label": "Your order comes with 2 E-Books, Barefoot Guide, Habit Tracker & Commu",
      "default": "<p>Your order comes with 2 E-Books, Barefoot Guide, Habit Tracker &amp; Community Access</p>"
    },
    {
      "type": "richtext",
      "id": "text_E03T3660KGrtmjmV",
      "label": "Text",
      "default": "<p>Today:</p>"
    },
    {
      "type": "richtext",
      "id": "text_FZ9AGPXQUaJWfBoa",
      "label": "Text",
      "default": "<p>FREE!</p>"
    },
    {
      "type": "richtext",
      "id": "text_iDKymoDR38o7GR73",
      "label": "Text",
      "default": "<p>WORTH: $99</p>"
    },
    {
      "type": "richtext",
      "id": "text_g9ksOC5Po3bE9LCg",
      "label": "Heading",
      "default": "<p>Why choose HK Cloud for happier feet?</p>"
    },
    {
      "type": "richtext",
      "id": "text_QDJBuy2UXeeA3LqT",
      "label": "Text",
      "default": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>"
    },
    {
      "type": "image_picker",
      "id": "image_bteapWtmZfxTyBlY",
      "label": "Stairs-Person-Ascend-1--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_49seiPNlsOrxOVfH",
      "label": "Text",
      "default": "<p>Cloud-Like Comfort</p>"
    },
    {
      "type": "image_picker",
      "id": "image_9F6kDlnBzVpd72sS",
      "label": "Smiley-Smile--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_x7AtIFBadXSDbEba",
      "label": "Text",
      "default": "<p>All Day Comfort</p>"
    },
    {
      "type": "image_picker",
      "id": "image_EqgDcrwS10DWFHwI",
      "label": "Cash-Shield--Streamline-Ultimate.svg"
    },
    {
      "type": "richtext",
      "id": "text_IyN66s5r4ruR0shN",
      "label": "Text",
      "default": "<p>60 Day Money Back </p>"
    },
    {
      "type": "richtext",
      "id": "text_bHbYrEd9CgiePn3c",
      "label": "Heading",
      "default": "<p>FREE FAST WORLDWIDE Shipping</p>"
    },
    {
      "type": "richtext",
      "id": "text_Qb3FXcbtBuhvg88g",
      "label": "Text",
      "default": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>"
    },
    {
      "type": "richtext",
      "id": "text_bNLrojOn8WeW7BA6",
      "label": "Heading",
      "default": "<p>EASY 30 DAY Returns</p>"
    },
    {
      "type": "richtext",
      "id": "text_eG21ZmykNzp4a0dG",
      "label": "Text",
      "default": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison Product | Variants",
      "settings": {
        "product_nwq2dHhIes5q2BjH": "hf-cloud",
        "text_EysZ4Y8OXPKWXeVl": "<p>Perfect for: ‘Everyday, Walking, Travel, Workouts’</p>",
        "text_vh8kNkzc94khDAZs": "<p>6,627 Reviews</p>",
        "url_vh8kNkzc94khDAZs": "#shopify-section-template--23938854453588__instant_rse_fowu_c4_a_cffmqw_K3Fh8e",
        "text_cc552PP6E4bWnHaX": "<p>Supports your feet</p>",
        "text_eeZIT8n9K5KifBNy": "<p>All day comfortable</p>",
        "text_NTpHfSbG9OMxGluB": "<p>60 Day Money Back</p>",
        "text_qxl8OyL107OJS3Rq": "<p>Perfect for: ‘Everyday, Walking, Travel, Workouts’</p>",
        "text_KjkD5Gr5eFdIE3JC": "<p>YOU SAVE</p>",
        "text_IY0dys8yMoyIlMcs": "<p>6,627 Reviews</p>",
        "text_DUapKBRBADtUki46": "<ul><li><p>Relieves pressure on feet and joints</p></li><li><p>Developed with orthopedists</p></li><li><p>Free US Shipping</p></li></ul>",
        "text_emjmGVTdokxKYPbj": "<p>Choose Your Size</p>",
        "text_QEhBWVqQU4tbF5Pl": "<p>CLICK FOR SIZE CHART</p>",
        "text_deoFxk43PbHWPJ7a": "<p>6</p>",
        "text_nCjAnBW7G68vb2AT": "<p>7</p>",
        "text_1zeKr14uG3aoikTi": "<p>7.5</p>",
        "text_4U2akJHmN045XQc9": "<p>8</p>",
        "text_ioDOlsc9x5XMuHik": "<p>9</p>",
        "text_2cWs9yhSq38BrxbM": "<p>10</p>",
        "text_jr2VLUk2eSzxq686": "<p>11</p>",
        "text_ku2f720MXyoq1Yyj": "<p>12</p>",
        "text_QoU1AVvBaPrdNYNU": "<p>13</p>",
        "text_dZWEaW0kEoYzU2UG": "<p>14</p>",
        "text_w86DfAWvsgagymc8": "<p>Choose Your Size</p>",
        "text_s80oL4e5nLrriCcx": "<p>CLICK FOR SIZE CHART</p>",
        "text_CHPOqEX4RbiEEi9h": "<p>4</p>",
        "text_xVJI2f0W4loLyCng": "<p>5</p>",
        "text_fJ5f2u9L2r8ECTBk": "<p>5.5</p>",
        "text_BQzGLbcF0Dn0s01R": "<p>6</p>",
        "text_KE5WM4QbnkifY0AG": "<p>7</p>",
        "text_zmUYcMG3ACL5G7Qn": "<p>8</p>",
        "text_HoZ5pgPjFZto7Ida": "<p>9</p>",
        "text_YGoLnZlmkPUvf4JH": "<p>10</p>",
        "text_m0h7meJIlV6xWUhg": "<p>11</p>",
        "text_LnqMGp7gd2fKGMr3": "<p>12</p>",
        "text_LNuAQdh0lyebiaWJ": "<p>Choose Your Color</p>",
        "text_Q7cCcQ94yrVoSHA5": "<p>BROWN</p>",
        "text_m9T9W8shmxD4PhSg": "<p>Blue</p>",
        "text_Yk0IK5WWamxNz16L": "<p>GREEN</p>",
        "text_0EAzf5zKgt3p2i8W": "<p>BLACK</p>",
        "text_TAW88TvGfaoVo7FT": "<p>PURPLE</p>",
        "text_eid1O0EXNjiAhMCM": "<p>White</p>",
        "text_m26D6ATGyyiu1e4G": "<p>Add To Cart - </p>",
        "text_y8eFlGRS8b02Om7z": "<p>Off</p>",
        "text_GZBgJK4dEJzrIGZ0": "<p>Free Shipping on Your Order! </p>",
        "text_rdLEcMzEi2b68bbQ": "<p>Shipping to</p>",
        "text_UhlZ7v0H0yHZbbDN": "<p></p>",
        "text_hO2GYItxNJcSpXtA": "<p>FREE WITH TODAY’S ORDER:</p>",
        "text_AmMDkVQL0jWWJ70b": "<p>Your order comes with 2 E-Books, Barefoot Guide, Habit Tracker &amp; Community Access</p>",
        "text_E03T3660KGrtmjmV": "<p>Today:</p>",
        "text_FZ9AGPXQUaJWfBoa": "<p>FREE!</p>",
        "text_iDKymoDR38o7GR73": "<p>WORTH: $99</p>",
        "text_g9ksOC5Po3bE9LCg": "<p>Why choose HK Cloud for happier feet?</p>",
        "text_QDJBuy2UXeeA3LqT": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>",
        "text_49seiPNlsOrxOVfH": "<p>Cloud-Like Comfort</p>",
        "text_x7AtIFBadXSDbEba": "<p>All Day Comfort</p>",
        "text_IyN66s5r4ruR0shN": "<p>60 Day Money Back </p>",
        "text_bHbYrEd9CgiePn3c": "<p>FREE FAST WORLDWIDE Shipping</p>",
        "text_Qb3FXcbtBuhvg88g": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>",
        "text_bNLrojOn8WeW7BA6": "<p>EASY 30 DAY Returns</p>",
        "text_eG21ZmykNzp4a0dG": "<p>Experience the ultimate in foot comfort and support. Our innovative design aligns your body naturally, reducing pain and improving posture.</p>"
      }
    }
  ],
  "blocks": [{ "type": "@app" }]
}
{% endschema %}
