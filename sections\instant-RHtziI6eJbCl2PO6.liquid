{% comment %} This file is generated by Instant and can be overwritten at any moment. {% endcomment %}
<div class="__instant iRHtziI6eJbCl2PO6" data-instant-id="RHtziI6eJbCl2PO6" data-instant-version="3.0.3" data-instant-layout="SECTION" data-section-id="{{ section.id }}">
  {%- style -%}
    .__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    	padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    	padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
    }

    @media screen and (min-width: 769px) {
    	.__instant[data-section-id='{{ section.id }}'] div[data-instant-type='root'] {
    		padding-top: {{ section.settings.padding_top }}px;
    		padding-bottom: {{ section.settings.padding_bottom }}px;
    	}
    }
  {%- endstyle -%}
  <!--  -->
  {{ 'instant-RHtziI6eJbCl2PO6.css' | asset_url | stylesheet_tag }}
  <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&amp;display=swap" rel="stylesheet">
  <div data-instant-type="root" class="i5QWX66UHgMYOSz3j">
    {%- liquid
      assign loading = 'eager'
      assign fetchpriority = 'auto'
      if section.location == 'footer'
        assign loading = 'lazy'
      elsif section.location == 'header'
        assign fetchpriority = 'high'
      elsif section.location == 'template'
        if section.index == 1
          assign fetchpriority = 'high'
        elsif section.index > 2
          assign loading = 'lazy'
        endif
      endif
    -%}
    <div class="iPhiErw3Ut5OEDMa9" data-instant-type="container" id="iPhiErw3Ut5OEDMa9">
      <div class="i7bzbVXL2ObzX77t1" data-instant-type="container">
        <div class="ioAmNX22Kbw1q8x35" data-instant-type="container">
          <div class="i7ekDapQOPPe2ZWUW" data-instant-type="container">
            <div class="iDrnQbfQJ8CWI10HH" data-instant-type="container">
              <div class="infa5i8zcA29wghNv" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text iblIbK80U2RP1VD5x">
                  <div>{{ section.settings.text_blIbK80U2RP1VD5x }}</div>
                </div>
              </div>
              <div class="i9rssJIxOu2YGXZKD" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text iBPSRP69NSAynItxJ">
                  <div>{{ section.settings.text_BPSRP69NSAynItxJ }}</div>
                </div>
                <div data-instant-type="text" class="instant-rich-text iBqgwqxsbnw2Ccw8B">
                  <div>{{ section.settings.text_Bqgwqxsbnw2Ccw8B }}</div>
                </div>
              </div>
            </div>
            <div class="iuXXYM7KoackOjBKD" data-instant-type="container">
              <div class="i6ygMb1f0XDpet2LX" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text isWMcAKqZTAkru6nW">
                  <div>{{ section.settings.text_sWMcAKqZTAkru6nW }}</div>
                </div>
              </div>
              <div class="i8FTE4sweLEgEgKpN" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text izBddLdhuyJMdHHh1">
                  <div>{{ section.settings.text_zBddLdhuyJMdHHh1 }}</div>
                </div>
              </div>
              <div class="iSWG0lnrUzjpm2MyY" data-instant-type="container">
                <div data-instant-type="text" class="instant-rich-text icaRH8Nm9cJnRqPCQ">
                  <div>{{ section.settings.text_caRH8Nm9cJnRqPCQ }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="iC7hth7I9tkZMgjMa" data-instant-type="container">
          <div data-instant-type="image" class="ifqCjHu6r382UxtG4">
            {% if section.settings.image_fqCjHu6r382UxtG4 and section.settings.image_fqCjHu6r382UxtG4 != blank %}
              {{ section.settings.image_fqCjHu6r382UxtG4 | image_url: width: 1280 | image_tag: class: 'instant-image instant-image__main', alt: section.settings.image_fqCjHu6r382UxtG4.alt, fetchpriority: fetchpriority, loading: loading, decoding: 'async' }}
            {% else %}
              <img alt="" srcSet="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=360 360w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=640 640w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=750 750w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=828 828w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1080 1080w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1200 1200w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=1920 1920w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=2048 2048w, https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png?width=3840 3840w" src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/wliOvjJHmH8IBfCA/72f7479e1318ab1af0e923734f74d04e8ab44567.png" width="2000" height="2000" decoding="async" fetchpriority="{{ fetchpriority }}" loading="{{ loading }}" class="instant-image instant-image__main">
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- prettier-ignore -->
  <script>(()=>{let t=window.Instant||{};if(!t.initializedAppEmbed&&!window.__instant_loading_core){window.__instant_loading_core=!0,t.initializedVersion="3.0.3",t.initialized=!0;let i=()=>{let i=(t,i)=>t.split(".").map(Number).reduce((t,e,n)=>t||e-i.split(".")[n],0),e=[...document.querySelectorAll(".__instant")].map(t=>t.getAttribute("data-instant-version")||"1.0.0").sort(i).pop()||"1.0.0",n=document.createElement("script");n.src="https://client.instant.so/scripts/instant-core.min.js?version="+e,document.body.appendChild(n),t.initializedVersion=e};"loading"!==document.readyState?i():document.addEventListener("DOMContentLoaded",i)}})();</script>
</div>
{% schema %}
{
  "name": "Maison Image With Text",
  "tag": "section",
  "enabled_on": { "templates": ["*"] },
  "settings": [
    {
      "type": "richtext",
      "id": "text_blIbK80U2RP1VD5x",
      "label": "Tagline",
      "default": "<p>WHY HIKE?</p>"
    },
    {
      "type": "richtext",
      "id": "text_BPSRP69NSAynItxJ",
      "label": "Heading",
      "default": "<p>Normal shoes aren’t made for comfort. They can feel like ‘foot prisons’.</p>"
    },
    {
      "type": "richtext",
      "id": "text_Bqgwqxsbnw2Ccw8B",
      "label": "Text",
      "default": "<p>They’re made purely for style, rather than comfort all day. They don’t provide proper support &amp; often are too tight.</p><p></p><p><strong>Not investing in something comfortable you wear daily, can lead to...</strong></p>"
    },
    {
      "type": "richtext",
      "id": "text_sWMcAKqZTAkru6nW",
      "label": "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      "default": "<ul><li><p>Bad posture, which leads to back and knee pain</p></li></ul>"
    },
    {
      "type": "richtext",
      "id": "text_zBddLdhuyJMdHHh1",
      "label": "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      "default": "<ul><li><p>Pain after a day of wearing them, even without walking a lot</p></li></ul>"
    },
    {
      "type": "richtext",
      "id": "text_caRH8Nm9cJnRqPCQ",
      "label": "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
      "default": "<ul><li><p>Bunions and hammertoes causing pain</p></li></ul>"
    },
    {
      "type": "image_picker",
      "id": "image_fqCjHu6r382UxtG4",
      "label": "Image"
    },
    {
      "type": "header",
      "content": "Section padding"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Top padding",
      "default": 0
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "Bottom padding",
      "default": 0
    }
  ],
  "presets": [
    {
      "name": "Maison Image With Text",
      "settings": {
        "text_blIbK80U2RP1VD5x": "<p>WHY HIKE?</p>",
        "text_BPSRP69NSAynItxJ": "<p>Normal shoes aren’t made for comfort. They can feel like ‘foot prisons’.</p>",
        "text_Bqgwqxsbnw2Ccw8B": "<p>They’re made purely for style, rather than comfort all day. They don’t provide proper support &amp; often are too tight.</p><p></p><p><strong>Not investing in something comfortable you wear daily, can lead to...</strong></p>",
        "text_sWMcAKqZTAkru6nW": "<ul><li><p>Bad posture, which leads to back and knee pain</p></li></ul>",
        "text_zBddLdhuyJMdHHh1": "<ul><li><p>Pain after a day of wearing them, even without walking a lot</p></li></ul>",
        "text_caRH8Nm9cJnRqPCQ": "<ul><li><p>Bunions and hammertoes causing pain</p></li></ul>"
      }
    }
  ]
}
{% endschema %}
