{%- comment -%}
------------------------------------------------------------------------------------------------------------------------
NOTE TO DEVELOPERS: welcome to Impact theme! We hope that you will enjoy editing this theme as much as we did for
  developing it. We have put a lot of work to make this theme as developer friendly as possible by offering you
  hooks to integrate into critical parts of the theme. You will find the complete technical documentation (including
  all events, dependencies...) in the "documentation.txt" file, located in the Assets folder.
------------------------------------------------------------------------------------------------------------------------
{%- endcomment -%}

<!doctype html>

<html class="no-js" lang="{{ request.locale.iso_code }}" dir="{% render 'direction' %}">
  <head>
    <script type="module" src="{{ 'custom-checkout.js' | asset_url }}"></script>
    <script src="{{ 'country-selector.js' | asset_url }}" defer></script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, height=device-height, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="theme-color" content="{{ settings.header_background }}">
    

    

    <title>{% if page_title == blank %}{{ shop.name }}{% else %}{{ page_title }}{% if current_page != 1 %} &ndash; {{ 'general.page' | t: page: current_page }}{% endif %}{% endif %}</title>

    {%- if page_description -%}
      <meta name="description" content="{{ page_description | escape }}">
    {%- endif -%}

    <link rel="canonical" href="{{ canonical_url }}">

    {%- if settings.favicon -%}
      <link rel="shortcut icon" href="{{ settings.favicon | image_url: width: 96 }}">
      <link rel="apple-touch-icon" href="{{ settings.favicon | image_url: width: 180 }}">
    {%- endif -%}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" integrity="sha512-Kc323vGBEqzTmouAECnVceyQqyqdsSiqLQISBL29aUW4U/M7pSPA/gEUZQqv1cwx4OnYxTxve5UMg5GT6L4JJg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    {%- comment -%}Few prefetch to increase performance on commonly used third-parties{%- endcomment -%}
    <link rel="preconnect" href="https://cdn.shopify.com">
    <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>
    <link rel="dns-prefetch" href="https://productreviews.shopifycdn.com">
    
    {%- unless settings.heading_font.system? -%}
      <link rel="preload" href="{{ settings.heading_font | font_url }}" as="font" type="font/woff2" crossorigin>
    {%- endunless -%}

    {%- unless settings.text_font.system? -%}
      <link rel="preload" href="{{ settings.text_font | font_url }}" as="font" type="font/woff2" crossorigin>
    {%- endunless -%}

    {%- render 'social-meta-tags' -%}
    {%- render 'microdata-schema' -%}
    {%- render 'css-variables' -%}
    {%- render 'js-variables' -%}

    <script type="module" src="{{ 'vendor.min.js' | asset_url }}"></script>
    <script type="module" src="{{ 'theme.js' | asset_url }}"></script>
    <script type="module" src="{{ 'sections.js' | asset_url }}"></script>

    {{ content_for_header }}

    {{- 'theme.css' | asset_url | stylesheet_tag: preload: true -}}
<!-- OpenGraph Meta Tags for SEO & Social Sharing -->
<meta property="og:title" content="{{ page_title | escape }}" />
<meta property="og:type" content="website" />
<meta property="og:url" content="{{ canonical_url }}" />
<meta property="og:image" content="{{ settings.social_share_image | img_url: '1200x630' }}" />
<meta property="og:description" content="{{ page_description | escape }}" />
<meta property="og:site_name" content="{{ shop.name }}" />
<meta property="og:locale" content="en_US" />

<!-- Twitter Card Meta Tags -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{{ page_title | escape }}" />
<meta name="twitter:description" content="{{ page_description | escape }}" />
<meta name="twitter:image" content="{{ settings.social_share_image | img_url: '1200x630' }}" />

<!-- Additional Meta Tags (optional but useful) -->
<meta property="og:image:alt" content="Social sharing image" />
<meta property="og:image:width" content="1200" />
<meta property="og:image:height" content="630" />
  </head>

  <body class="{% if settings.show_page_transition %}page-transition{% endif %} {% if settings.zoom_image_on_hover %}zoom-image--enabled{% endif %}">
    {%- render 'shadow-dom-templates' -%}

    <a href="#main" class="skip-to-content sr-only">{{ 'general.accessibility.skip_to_content' | t }}</a>

    {%- if request.page_type != 'password' -%}
      {%- sections 'header-group' -%}
      {%- sections 'overlay-group' -%}

      {%- if settings.cart_type == 'popover' -%}
        <cart-notification-drawer open-from="bottom" class="quick-buy-drawer drawer"></cart-notification-drawer>
      {%- endif -%}
    {%- endif -%}

    {%- if request.page_type == 'customers/account' or request.page_type == 'customers/order' or request.page_type == 'customers/addresses' -%}
      {%- section 'account-banner' -%}
    {%- endif -%}

    <main role="main" id="main" class="anchor">
      {{ content_for_layout }}

      
      {%- if request.page_type != 'password' -%}
        {%- sections 'footer-group' -%}
      {%- endif -%}
    </main>

<script>
  window.themeSettings = {
    mainProductIds: "{{ settings.main_product_ids }}",
    bundledProductIds: "{{ settings.bundled_product_ids }}"
  };

  window.addEventListener('popstate', function (event) {
    location.reload();
});
</script>    

  </body>
</html>