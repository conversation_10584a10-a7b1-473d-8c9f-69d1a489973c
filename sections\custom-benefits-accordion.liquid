{% comment %}
  Section: custom-benefits-accordion (Flexbox)
  Renamed from: Split Accordion with Right-Aligned Image
{% endcomment %}

{{ 'custom-typography.css' | asset_url | stylesheet_tag }}

<section id="sa-{{ section.id }}"
  class="sa-section"
  style="
    --sa-bg: {{ section.settings.bg | color_extract: 'alpha' | default: 1 | times: 0 | plus: 1 | minus: 1 | default: 0 }};
    --sa-text: {{ section.settings.text_color }};
    --sa-heading: {{ section.settings.heading_color }};
    --sa-card-bg: {{ section.settings.card_bg }};
    --sa-card-border: {{ section.settings.card_border }};
    --sa-card-active-bg: {{ section.settings.card_active_bg }};
    --sa-card-active-text: {{ section.settings.card_active_text }};
    --sa-content-bg: {{ section.settings.content_bg }};
    --sa-chevron: {{ section.settings.chevron_color }};
    --sa-chevron-active: {{ section.settings.chevron_active_color }};
    --sa-container-max: {{ section.settings.content_max_width }}px;
    --sa-pt: {{ section.settings.padding_top }}px;
    --sa-pb: {{ section.settings.padding_bottom }}px;
    --sa-nudge: {{ section.settings.nudge | default: 0 }}px;
    --sa-right-bg: {{ section.settings.right_bg_color }};
    --sa-right-width: {{ section.settings.right_bg_width }}vw;
    --sa-right-max: {{ section.settings.right_bg_max }}px;
  "
>
  <div class="sa-inner">
    <div class="sa-left">
      {% if section.settings.title != blank %}
        <h2 class="sa-title custom-heading-primary custom-benefits">{{ section.settings.title }}</h2>
      {% endif %}

      <div class="sa-accordion" role="region" aria-label="{{ section.settings.title | escape }}">
        {% for block in section.blocks %}
          {% if block.type == 'accordion' %}
            {% assign is_open = block.settings.open_by_default %}
            {% assign panel_id = 'panel-' | append: block.id %}
            {% assign btn_id = 'control-' | append: block.id %}

            <div class="sa-item {% if is_open %}is-open{% endif %}" data-accordion-item {{ block.shopify_attributes }}>
              <button type="button" class="sa-trigger" id="{{ btn_id }}" aria-expanded="{% if is_open %}true{% else %}false{% endif %}" aria-controls="{{ panel_id }}">
                <span class="sa-trigger__content">
                  {% if block.settings.icon != blank %}
                    <img class="sa-icon" src="{{ block.settings.icon | image_url: width: 64 }}" alt="" width="{{ block.settings.icon.width }}" height="{{ block.settings.icon.height }}" loading="lazy" />
                  {% endif %}
                  <span class="sa-trigger__text">{{ block.settings.title }}</span>
                </span>
                <span class="sa-trigger__chevron" aria-hidden="true">
                  <svg viewBox="0 0 24 24" width="20" height="20" focusable="false"><path d="M6 9l6 6 6-6" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
                </span>
              </button>
              <div id="{{ panel_id }}" class="sa-panel" role="region" aria-labelledby="{{ btn_id }}" {% unless is_open %}style="display: none;"{% endunless %}>
                <div class="sa-panel__inner rte">{{ block.settings.content }}</div>
              </div>
            </div>
          {% endif %}
        {% endfor %}
      </div>

      <script>
        (function() {
          var accordions = document.querySelectorAll('#sa-{{ section.id }} [data-accordion-item]');

          accordions.forEach(function(item) {
            var trigger = item.querySelector('.sa-trigger');
            var panel = item.querySelector('.sa-panel');
            var chevron = item.querySelector('.sa-trigger__chevron');

            if (!trigger || !panel) return;

            trigger.addEventListener('click', function(e) {
              e.preventDefault();
              e.stopPropagation();

              var isOpen = item.classList.contains('is-open');

              if (isOpen) {
                // Close
                item.classList.remove('is-open');
                trigger.setAttribute('aria-expanded', 'false');
                panel.style.display = 'none';
                if (chevron) chevron.style.transform = 'rotate(0deg)';
              } else {
                // Open
                item.classList.add('is-open');
                trigger.setAttribute('aria-expanded', 'true');
                panel.style.display = 'block';
                if (chevron) chevron.style.transform = 'rotate(180deg)';
              }
            });
          });
        })();
      </script>
    </div>

    <div class="sa-right">
      {% for block in section.blocks %}
        {% if block.type == 'right_image' and block.settings.image != blank %}
          <figure class="sa-figure" {{ block.shopify_attributes }}>
            <img class="sa-image" src="{{ block.settings.image | image_url: width: 1400 }}" width="{{ block.settings.image.width }}" height="{{ block.settings.image.height }}"
                 alt="{{ block.settings.alt | escape }}" loading="lazy" />
          </figure>
        {% endif %}
      {% endfor %}
    </div>
  </div>

  <style>
    /* Base resets */
    #sa-{{ section.id }} {
      position: relative;
      background: {{ section.settings.section_bg }};
      color: var(--sa-text);
      padding-top: var(--sa-pt);
      padding-bottom: var(--sa-pb);
      overflow-x: hidden;
    }
    #sa-{{ section.id }} .sa-inner {
      position: relative;
      z-index: 1;
      max-width: var(--sa-container-max);
      margin: 0 auto;
      display: flex;
      align-items: center;
      gap: 48px;
    }
    #sa-{{ section.id }} .sa-left {
      flex: 0 0 560px;
      max-width: 560px;
      min-width: 560px;
      width: 560px;
      overflow: hidden; /* Prevent content from expanding beyond container */
    }
    #sa-{{ section.id }} .sa-right {

    /* Ensure consistent sizing model for all descendants */
    #sa-{{ section.id }} *,
    #sa-{{ section.id }} *::before,
    #sa-{{ section.id }} *::after { box-sizing: border-box; }

    /* Keep accordion width consistent closed/open */
    #sa-{{ section.id }} .sa-accordion {
      width: 100%;
      max-width: 100%;
      overflow: hidden;
      box-sizing: border-box;
    }

      flex: 1 1 auto;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-right: calc((100vw - var(--sa-container-max)) / -2);
    }

    /* Right half background overlay with curved edge */
    #sa-{{ section.id }}::after { content: ""; position: absolute; top: 0; right: 0; bottom: 0; width: min(var(--sa-right-width), var(--sa-right-max)); background: var(--sa-right-bg); border-top-left-radius: 600px; border-bottom-left-radius: 600px; z-index: 0; }

    #sa-{{ section.id }} .sa-title { color: var(--sa-heading); letter-spacing: .02em; /* Typography handled by .custom-heading-primary class */ }

    /* Mobile tap highlight removal */
    #sa-{{ section.id }} * {
      -webkit-tap-highlight-color: transparent;
      -webkit-tap-highlight-color: rgba(0,0,0,0);
      tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
    }

    /* Accordion cards - dynamic height, absolutely fixed width */
    #sa-{{ section.id }} .sa-item {
      display: block;
      width: 100% !important;
      max-width: 100% !important;
      min-width: 100% !important;
      background: var(--sa-card-bg);
      border: 1px solid var(--sa-card-border);
      border-radius: 10px;
      margin-bottom: 12px;
      overflow: hidden !important;
      box-sizing: border-box !important;
      height: auto;
      position: relative;
      /* Absolutely prevent any width changes during interaction */
      transform: none !important;
      transition: none !important;
      flex-shrink: 0 !important;
      flex-grow: 0 !important;
      contain: layout !important; /* CSS containment to prevent layout shifts */
    }

    /* Active state background and text color */
    #sa-{{ section.id }} .sa-item.is-open {
      background: var(--sa-card-active-bg);
      color: var(--sa-card-active-text);
    }

    /* Closed accordion - just trigger height */
    #sa-{{ section.id }} .sa-item:not(.is-open) {
      height: 58px;
    }

    /* Open accordion - auto height for content */
    #sa-{{ section.id }} .sa-item.is-open {
      height: auto;
      min-height: 58px;
    }

    /* Trigger button - clean, no visual effects, fixed width */
    #sa-{{ section.id }} .sa-trigger {
      width: 100% !important;
      max-width: 100% !important;
      min-width: 100% !important;
      text-align: left;
      background: none;
      border: none;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px;
      cursor: pointer;
      color: inherit;
      font-family: inherit;
      font-size: inherit;
      border-radius: 0;
      -webkit-appearance: none;
      appearance: none;
      outline: none;
      box-shadow: none;
      margin: 0;
      height: 58px;
      flex-shrink: 0;
      /* Prevent any width changes */
      transform: none !important;
      transition: none !important;
      box-sizing: border-box;
    }

    /* Remove all interaction effects */
    #sa-{{ section.id }} .sa-trigger:hover,
    #sa-{{ section.id }} .sa-trigger:focus,
    #sa-{{ section.id }} .sa-trigger:active,
    #sa-{{ section.id }} .sa-trigger:focus-visible {
      background: none;
      border: none;
      outline: none;
      box-shadow: none;
      transform: none;
    }

    #sa-{{ section.id }} .sa-trigger__content {
      display: flex;
      align-items: center;
      gap: 12px;
      font-weight: 600;
      pointer-events: none;
    }

    #sa-{{ section.id }} .sa-icon {
      height: 26px;
      width: auto;
      object-fit: contain;
      pointer-events: none;
    }

    #sa-{{ section.id }} .sa-trigger__chevron {
      color: var(--sa-chevron);
      display: flex;
      transition: transform 0.2s ease;
      pointer-events: none;
    }

    #sa-{{ section.id }} .sa-item.is-open .sa-trigger__chevron {
      transform: rotate(180deg);
      color: var(--sa-chevron-active);
    }

    #sa-{{ section.id }} .sa-panel {
      border-top: 1px solid var(--sa-card-border);
      padding: 16px 16px 16px 58px;
      background: var(--sa-content-bg);
      box-sizing: border-box !important;
      width: 100% !important;
      max-width: 100% !important;
      min-width: 100% !important;
      overflow: hidden !important;
      /* Absolutely prevent any width changes */
      transform: none !important;
      transition: none !important;
      flex-shrink: 0 !important;
      flex-grow: 0 !important;
      contain: layout !important;
    }

    /* Show accessible focus ring only for keyboard/desktop (not touch) */
    @media (hover: hover) and (pointer: fine) {
      #sa-{{ section.id }} .sa-item:focus-within { box-shadow: 0 1px 0 rgba(0,0,0,.04), 0 0 0 2px var(--sa-heading) inset; }
    }

    #sa-{{ section.id }} .sa-panel__inner {
      color: var(--sa-text);
      font-size: 14px;
      line-height: 1.5;
      width: 100% !important;
      max-width: 100% !important;
      overflow-wrap: break-word;
      word-wrap: break-word;
      box-sizing: border-box !important;
    }
    #sa-{{ section.id }} .sa-item[open] .sa-trigger__chevron { transform: rotate(180deg); }

    /* Right image */
    #sa-{{ section.id }} .sa-figure { display: block; margin: 0; }
    #sa-{{ section.id }} .sa-image { max-width: min(600px, 48vw); width: auto; height: auto; max-height: 520px; object-fit: contain; }

    /* Mobile-first adjustments */

    @media (max-width: 990px) {
      #sa-{{ section.id }}::after { display: none; }
      #sa-{{ section.id }} .sa-inner { flex-direction: column; gap: 24px; padding: 0 16px; }
      #sa-{{ section.id }} .sa-left {
        flex: 1 1 auto;
        max-width: 100% !important;
        min-width: auto !important;
        width: 100% !important;
        overflow: visible !important;
      }
      #sa-{{ section.id }} .sa-right { margin-right: 0; transform: none; justify-content: center; }
      #sa-{{ section.id }} .sa-image { width: 100%; max-width: 520px; }
      {% if section.settings.use_mobile_bg and section.settings.mobile_bg != blank %}
        #sa-{{ section.id }} { position: relative; }
        #sa-{{ section.id }}::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-image: url('{{ section.settings.mobile_bg | image_url: width: 1400 }}');
          background-size: {{ section.settings.mobile_bg_size }};
          background-position: {{ section.settings.mobile_bg_position }};
          background-repeat: no-repeat;
          z-index: 0;
          pointer-events: none;
        }
        #sa-{{ section.id }} .sa-inner {
          position: relative;
          z-index: 1;
        }
        #sa-{{ section.id }} .sa-right { display: none; }
      {% endif %}
    }

    /* Mobile phones and small tablets - prevent horizontal overflow */
    @media (max-width: 768px) {
      #sa-{{ section.id }} .sa-inner {
        padding: 0 12px;
        max-width: 100vw;
        overflow-x: hidden;
      }
      #sa-{{ section.id }} .sa-left {
        width: 100% !important;
        max-width: calc(100vw - 24px) !important;
        min-width: auto !important;
        flex: none !important;
        overflow: visible !important;
      }
      #sa-{{ section.id }} .sa-accordion {
        width: 100% !important;
        max-width: 100% !important;
        overflow: visible !important;
      }
      #sa-{{ section.id }} .sa-item {
        width: 100% !important;
        max-width: 100% !important;
        min-width: auto !important;
        overflow: visible !important;
        contain: none !important;
      }
      #sa-{{ section.id }} .sa-panel {
        overflow: visible !important;
        contain: none !important;
      }
    }

    /* Utilities */
    #sa-{{ section.id }} .visually-hidden { position: absolute !important; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0,0,0,0); white-space: nowrap; border: 0; }
  </style>



  {% schema %}
  {
    "name": "custom-benefits-accordion",
    "tag": "section",
    "max_blocks": 12,
    "settings": [
      {"type": "text", "id": "title", "label": "Heading", "default": "MUSHROOM BENEFITS"},
      {"type": "color", "id": "section_bg", "label": "Section background", "default": "#F7F2EC"},
      {"type": "color", "id": "right_bg_color", "label": "Right column background", "default": "#FFFFFF"},
      {"type": "range", "id": "right_bg_width", "label": "Right curved width (vw)", "min": 30, "max": 70, "step": 1, "default": 48},
      {"type": "range", "id": "right_bg_max", "label": "Right curved max width (px)", "min": 400, "max": 900, "step": 10, "default": 760},
      {"type": "color", "id": "heading_color", "label": "Heading color", "default": "#1b1b1b"},
      {"type": "color", "id": "text_color", "label": "Text color", "default": "#1b1b1b"},
      {"type": "color", "id": "card_bg", "label": "Card background", "default": "#ffffff"},
      {"type": "color", "id": "card_border", "label": "Card border", "default": "#E7E2DB"},
      {"type": "color", "id": "card_active_bg", "label": "Active card background", "default": "#f8f5f1"},
      {"type": "color", "id": "card_active_text", "label": "Active card text color", "default": "#1b1b1b"},
      {"type": "color", "id": "content_bg", "label": "Content background", "default": "#ffffff"},
      {"type": "color", "id": "chevron_color", "label": "Chevron color", "default": "#000000"},
      {"type": "color", "id": "chevron_active_color", "label": "Active chevron color", "default": "#000000"},

      {"type": "range", "id": "content_max_width", "label": "Content max width (px)", "min": 900, "max": 1600, "step": 10, "default": 1200},
      {"type": "range", "id": "padding_top", "label": "Padding top (px)", "min": 0, "max": 120, "step": 4, "default": 56},
      {"type": "range", "id": "padding_bottom", "label": "Padding bottom (px)", "min": 0, "max": 120, "step": 4, "default": 56},
      {"type": "range", "id": "nudge", "label": "Right image nudge (px)", "min": -200, "max": 200, "step": 4, "default": 0},

      {"type": "checkbox", "id": "use_mobile_bg", "label": "On mobile, use a background image and hide the right image", "default": false},
      {"type": "image_picker", "id": "mobile_bg", "label": "Mobile/Tablet background image"},
      {"type": "select", "id": "mobile_bg_size", "label": "Mobile background size", "default": "cover", "options": [
        {"value": "cover", "label": "Cover"},
        {"value": "contain", "label": "Contain"}
      ]},
      {"type": "select", "id": "mobile_bg_position", "label": "Mobile background position", "default": "center right", "options": [
        {"value": "center", "label": "Center"},
        {"value": "top", "label": "Top"},
        {"value": "bottom", "label": "Bottom"},
        {"value": "left", "label": "Left"},
        {"value": "right", "label": "Right"},
        {"value": "center left", "label": "Center left"},
        {"value": "center right", "label": "Center right"}
      ]}
    ],
    "blocks": [
      {
        "type": "accordion",
        "name": "Accordion item",
        "settings": [
          {"type": "image_picker", "id": "icon", "label": "Icon"},
          {"type": "text", "id": "title", "label": "Title", "default": "Support Focus & Memory*"},
          {"type": "richtext", "id": "content", "label": "Content", "default": "<p>Example description text for this benefit. Replace via theme editor.</p>"},
          {"type": "checkbox", "id": "open_by_default", "label": "Open by default", "default": false}
        ]
      },
      {
        "type": "right_image",
        "name": "Right column image",
        "limit": 1,
        "settings": [
          {"type": "image_picker", "id": "image", "label": "Image"},
          {"type": "text", "id": "alt", "label": "Alt text"}
        ]
      }
    ],
    "presets": [
      {
        "name": "custom-benefits-accordion",
        "category": "FAQ",
        "blocks": [
          {"type": "accordion", "settings": {"title": "Support Focus & Memory*", "open_by_default": true}},
          {"type": "accordion", "settings": {"title": "Support Inflammatory Response*"}},
          {"type": "accordion", "settings": {"title": "Improve Gut Health*"}},
          {"type": "accordion", "settings": {"title": "Immune Support*"}},
          {"type": "right_image"}
        ]
      }
    ]
  }
  {% endschema %}
</section>

