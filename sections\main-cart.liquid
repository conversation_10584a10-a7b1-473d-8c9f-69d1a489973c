
<div class="container">
  {%- if cart.item_count == 0 -%}
    <div class="empty-state">
      <div class="empty-state__icon-wrapper">
        {%- render 'icon' with 'cart', width: 32, height: 32, stroke_width: 1 -%}
        <span class="count-bubble count-bubble--lg">0</span>
      </div>

      <div class="prose">
        <p class="h4">{{ 'cart.general.empty' | t }}</p>

        {%- assign button_content = 'cart.general.continue_shopping' | t -%}
        {%- render 'button', href: settings.cart_empty_button_link, size: 'xl', content: button_content -%}
      </div>
    </div>
  {%- else -%}
    <div class="page-spacer">
      <div class="cart">
        <div class="cart-header">
          <h1 class="h2">{{ 'cart.general.title' | t }}</h1>

          {%- if settings.cart_show_free_shipping_threshold -%}
            {%- render 'free-shipping-bar' -%}
          {%- endif -%}
        </div>

        <div class="cart-order">
          <div class="cart-order__summary">
            {%- comment -%}
            ----------------------------------------------------------------------------------------------------------------
            ORDER SUMMARY
            ----------------------------------------------------------------------------------------------------------------
            {%- endcomment -%}
            <table class="order-summary">
              <thead class="order-summary__header">
                <tr>
                  <th>{{ 'customer.order.product' | t }}</th>
                  <th class="w-0">{{ 'customer.order.quantity' | t }}</th>
                  <th class="text-end">{{ 'customer.order.total' | t }}</th>
                </tr>
              </thead>

              <tbody class="order-summary__body">
                {%- for line_item in cart.items -%}
                  {%- assign line_max_quantity = '' -%}

                  {%- if line_item.variant.inventory_management != blank and line_item.variant.inventory_policy == 'deny' -%}
                    {%- assign line_max_quantity = line_item.variant.inventory_quantity -%}
                  {%- endif -%}

                  <tr>
                    <td>{%- render 'line-item', line_item: line_item -%}</td>

                    <td class="hidden align-center text-center text-subdued sm:table-cell">
                      <line-item-quantity class="v-stack justify-center gap-2">
                        <div class="quantity-section">
          <button class="quantity-button minus" onclick="updateQuantity('{{ line_item.key }}', -1)"><span class="minus-ico">-</span></button>
          <input class="quantity-input" type="text" is="quantity-input" inputmode="numeric" {% if line_max_quantity %}max="{{ line_max_quantity }}"{% endif %} data-line-key="{{ line_item.key }}" aria-label="{{ 'cart.order.change_quantity' | t | escape }}" value="{{ line_item.quantity }}" id="quantity-{{ line_item.key | escape }}">
          <button class="quantity-button plus" onclick="updateQuantity('{{ line_item.key }}', 1)"><span class="plus-ico">+</span></button>
          </div>
                        <span class="text-xs">
                          <a href="{{ line_item.url_to_remove }}" class="link remove-link" id="remove-item-link">{{ 'cart.order.remove' | t }}</a>
                        </span>
                      </line-item-quantity>
                    </td>

                    <td class="hidden align-center text-subdued text-end sm:table-cell">{{ line_item.final_line_price | money }}</td>
                  </tr>
                {%- endfor -%}
              </tbody>
            </table>
{%- comment -%} script for quantity button  {%- endcomment -%}
<script>

document.addEventListener('DOMContentLoaded', function() {
    const removeButton = document.getElementById('remove-item-link');
    
    if (removeButton) {
        removeButton.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent the default action (removal)
            
            // Optional: You can add some feedback or animation here before the reload

            // Set a delay (e.g., 1000ms = 1 second)
            setTimeout(function() {
                location.reload(); // Reload the page after the delay
            }, 1000); // Adjust the delay time as needed
        });
    }
});










  
  function updateQuantity(lineKey, change) {
    // Add gray overlay and disable cart-drawer interaction
    showLoadingOverlay();

    // Find the input element using the data-line-key attribute
    const quantityInput = document.querySelector(`input[data-line-key="${lineKey}"]`);
    if (quantityInput) {
      let newQuantity = parseInt(quantityInput.value) + change;

      // Ensure quantity is within valid range (e.g., 1 to max quantity)
      newQuantity = Math.max(1, newQuantity); // You can adjust this to be more restrictive based on max inventory
      quantityInput.value = newQuantity;

      // Optionally update the cart via AJAX or your platform's cart update function
      updateCart(lineKey, newQuantity);
    } else {
      console.error(`Quantity input with data-line-key "${lineKey}" not found.`);
    }
  }

  async function updateCart(lineKey, targetQuantity) {
    if (window.themeVariables.settings.pageType === "cart") {
      // If the user is on the cart page, update the cart directly via URL
      window.location.href = `${Shopify.routes.root}cart/change?id=${lineKey}&quantity=${targetQuantity}`;
    } else {
      // If the user is not on the cart page, handle it with JavaScript
      const lineItem = document.querySelector(`.cart-item[data-line-key="${lineKey}"]`);
      lineItem?.dispatchEvent(new CustomEvent("line-item:will-change", { bubbles: true, detail: { targetQuantity } }));

      let sectionsToBundle = [];
      document.documentElement.dispatchEvent(new CustomEvent("cart:prepare-bundled-sections", { bubbles: true, detail: { sections: sectionsToBundle } }));

      // Send the AJAX request to update the cart on the server
      try {
        const cartContent = await (await fetch(`${Shopify.routes.root}cart/change.js`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({
            id: lineKey,
            quantity: targetQuantity,
            sections: sectionsToBundle
          })
        })).json();

        // Filter out the updated line item
        const lineItemAfterChange = cartContent["items"].filter((item) => item["key"] === lineKey);

        // Dispatch the event to notify that the line item has changed
        lineItem?.dispatchEvent(new CustomEvent("line-item:change", {
          bubbles: true,
          detail: {
            quantity: lineItemAfterChange.length === 0 ? 0 : lineItemAfterChange[0]["quantity"],
            cart: cartContent
          }
        }));

        // Dispatch the cart change event to update the entire cart
        document.documentElement.dispatchEvent(new CustomEvent("cart:change", {
          bubbles: true,
          detail: {
            baseEvent: "line-item:change",
            cart: cartContent
          }
        }));

        // Optionally, update the cart UI
        updateCartUI(cartContent);

        // Refresh the page after a successful update
        location.reload(); // This reloads the entire page

      } catch (error) {
        console.error("Error updating cart:", error);
      }
    }
  }

  function showLoadingOverlay() {
    // Find the cart-drawer element
    const cartDrawer = document.querySelector('.cart-drawer__inner');
    if (cartDrawer) {
      // Create the overlay if it doesn't exist
      let overlay = cartDrawer.querySelector('#loading-overlay');
      if (!overlay) {
        overlay = document.createElement('div');
        overlay.id = 'loading-overlay';
        overlay.style.position = 'absolute';
        overlay.style.top = 0;
        overlay.style.left = 0;
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.backgroundColor = '';
        overlay.style.zIndex = 9999;
        overlay.style.pointerEvents = 'auto'; // Ensure overlay captures clicks

        // Create a loading spinner element
        const spinner = document.createElement('div');
        spinner.classList.add('loading-spinner');
        spinner.style.position = 'absolute';
        spinner.style.top = '40%';
        spinner.style.left = '45%';
        spinner.style.transform = 'translate(-50%, -50%)';
        spinner.style.border = '7px solid #f3f3f3'; /* Light gray */
        spinner.style.borderTop = '7px solid #d6684b'; 
        spinner.style.borderRadius = '50%';
        spinner.style.width = '80px';
        spinner.style.height = '80px';
        spinner.style.animation = 'spin 3s linear infinite';
        overlay.appendChild(spinner);

        // Add the overlay to the cart-drawer
        cartDrawer.appendChild(overlay);
      }

      // Disable interactions with the cart drawer
      cartDrawer.style.pointerEvents = 'none';

      // Show the overlay
      overlay.style.display = 'block';
    }
  }

  function hideLoadingOverlay() {
    // Find the cart-drawer element and hide the overlay after a 2-second delay
    setTimeout(() => {
      const cartDrawer = document.querySelector('.cart-drawer__inner');
      if (cartDrawer) {
        const overlay = cartDrawer.querySelector('#loading-overlay');
        if (overlay) {
          overlay.style.display = 'none'; // Hide the overlay
        }

        // Re-enable interactions with the cart drawer
        cartDrawer.style.pointerEvents = 'auto';
      }
    }, 1000);
  }

  function updateCartUI(cartData) {
    // Update the total item count in the cart
    const cartQuantityElement = document.querySelector('.cart-quantity');
    if (cartQuantityElement) {
      cartQuantityElement.textContent = cartData.item_count; // Update the total item count in the cart
    }

    // Optionally, reload only the cart drawer or specific sections (like cart items)
    const cartDrawer = document.querySelector('.cart-drawer__inner');
    if (cartDrawer) {
      fetch(`${Shopify.routes.root}cart?view=drawer`)
        .then(response => response.text())
        .then(html => {
          const parser = new DOMParser();
          const doc = parser.parseFromString(html, 'text/html');
          const newCartDrawerContent = doc.querySelector('.cart-drawer__inner');
          if (newCartDrawerContent) {
            cartDrawer.innerHTML = newCartDrawerContent.innerHTML; // Update only the cart drawer content
          }
        })
        .catch(error => console.error('Error updating cart drawer:', error));
    }

    // Hide the loading overlay after a 2-second delay
    hideLoadingOverlay();

    // Optionally, update the cart items dynamically
    //console.log(cartData);
  }
</script>
 

            {%- comment -%}
            ----------------------------------------------------------------------------------------------------------------
            SHIPPING ESTIMATOR
            ----------------------------------------------------------------------------------------------------------------
            {%- endcomment -%}

            {%- if section.settings.show_shipping_estimator and cart.requires_shipping -%}
              {%- assign accordion_title = 'cart.shipping_estimator.estimate_shipping' | t -%}
              {%- capture accordion_content -%}{%- render 'shipping-estimator' -%}{%- endcapture -%}

              {%- render 'accordion', title: accordion_title, icon: 'picto-box', content: accordion_content, size: 'lg' -%}
            {%- endif -%}
          </div>

          <safe-sticky class="cart-order__recap v-stack gap-6">
            <form action="{{ routes.cart_url }}" method="POST" class="cart-form rounded">
              {%- for block in section.blocks -%}
                {%- case block.type -%}
                  {%- when '@app' -%}
                    {%- render block -%}

                  {%- when 'totals' -%}
                    <div class="cart-form__totals v-stack gap-2" {{ block.shopify_attributes }}>
                      {%- if block.settings.show_order_weight -%}
                        <div class="h-stack gap-4 justify-between">
                          <span class="text-subdued">{{ 'cart.general.weight' | t }}</span>
                          <span class="text-subdued">{{ cart.total_weight | weight_with_unit }}</span>
                        </div>
                      {%- endif -%}

                      <div class="h-stack gap-4 justify-between">
                        <span class="text-subdued">{{ 'cart.general.subtotal' | t }}</span>
                        <span class="text-subdued">{{ cart.items_subtotal_price | money }}</span>
                      </div>

                      {% for discount_application in cart.cart_level_discount_applications %}
                        <div class="h-stack gap-4 justify-between">
                          <div class="badge">
                            {%- render 'icon' with 'discount' -%} {{- discount_application.title -}}
                          </div>

                          <span class="text-subdued">-{{ discount_application.total_allocated_amount | money }}</span>
                        </div>
                      {% endfor %}

                      <div class="h-stack gap-4 justify-between">
                        <span class="h5">{{ 'cart.general.total' | t }}</span>
                        <span class="h5">{{- cart.total_price | money_with_currency -}}</span>
                      </div>

                      {%- if block.settings.show_shipping_text -%}
                        {%- if cart.taxes_included and shop.shipping_policy.body != blank -%}
                          <span class="text-subdued text-sm">{{ 'cart.general.taxes_included_and_shipping_policy_html' | t: link: shop.shipping_policy.url }}</span>
                        {%- elsif cart.taxes_included -%}
                          <span class="text-subdued text-sm">{{ 'cart.general.taxes_included_but_shipping_at_checkout' | t }}</span>
                        {%- elsif shop.shipping_policy.body != blank -%}
                          <span class="text-subdued text-sm">{{ 'cart.general.taxes_and_shipping_policy_at_checkout_html' | t: link: shop.shipping_policy.url }}</span>
                        {%- else -%}
                          <span class="text-subdued text-sm">{{ 'cart.general.taxes_and_shipping_at_checkout' | t }}</span>
                        {%- endif -%}
                      {%- endif -%}
                    </div>

                  {%- when 'cart_note' -%}
                    <cart-note class="cart-form__note block" {{ block.shopify_attributes }}>
                      {%- assign order_note = 'cart.general.order_note' | t -%}
                      {%- render 'input', name: 'note', multiline: 3, label: order_note, value: cart.note, maxlength: block.settings.maxlength -%}
                    </cart-note>

                  {%- when 'text' -%}
                    {%- if block.settings.content != blank -%}
                      <div class="prose text-subdued" {{ block.shopify_attributes }}>
                        {{- block.settings.content -}}
                      </div>
                    {%- endif -%}

                  {%- when 'offer' -%}
                    {%- assign previous_block_index = forloop.index0 | minus: 1 -%}
                    {%- assign next_block_index = forloop.index0 | plus: 1 -%}

                    {%- if section.blocks[next_block_index].type == 'offer' -%}
                      <div class="v-stack gap-4">
                    {%- endif -%}

                    {%- render 'offer', block: block -%}

                    {%- if section.blocks[previous_block_index].type == 'offer' -%}
                      </div>
                    {%- endif -%}

                  {%- when 'accelerated_buttons' -%}
                    {% if additional_checkout_buttons %}
                      <div class="additional-checkout-buttons additional-checkout-buttons--vertical" {{ block.shopify_attributes }}>
                        {{- content_for_additional_checkout_buttons -}}
                      </div>
                    {% endif %}

                  {%- when 'checkout_button' -%}
                    {%- assign checkout_button = 'cart.general.checkout' | t -%}
                    {%- render 'button', type: 'submit', icon: 'picto-lock', content: checkout_button, size: 'xl', name: 'checkout', stretch: true, block: block -%}
                {%- endcase -%}
              {%- endfor -%}
            </form>

            {%- if section.settings.show_payment_icons and shop.enabled_payment_types.size > 0 -%}
              <div class="v-stack gap-4">
                <span class="text-xs text-subdued text-center">{{ 'cart.general.we_accept' | t }}</span>

                <div class="h-stack gap-2 wrap justify-center">
                  {%- for type in shop.enabled_payment_types -%}
                    {{- type | payment_type_svg_tag -}}
                  {%- endfor -%}
                </div>
              </div>
            {%- endif -%}
          </safe-sticky>
        </div>
      </div>
    </div>
  {%- endif -%}
</div>

<script>
// Prevent reinitialization
let isCartScriptInitialized = false;

// Main cart
document.addEventListener('DOMContentLoaded', function() {
    // Check if the script has already run
    if (isCartScriptInitialized) {
        console.log("Main cart script already initialized. Skipping...");
        return;
    }
    isCartScriptInitialized = true; // Mark as initialized

    console.log("Initializing main cart script...");

    if (window.themeSettings) {
        const mainProductIds = window.themeSettings.mainProductIds
            ? window.themeSettings.mainProductIds.split(',').map(id => parseInt(id.trim(), 10))
            : [];
        const bundledProductIds = window.themeSettings.bundledProductIds
            ? window.themeSettings.bundledProductIds.split(',').map(id => parseInt(id.trim(), 10))
            : [];

        // Function to check cart items immediately after DOM load
        function checkCartForBundledProducts() {
            fetch('/cart.js')
                .then(response => response.json())
                .then(cart => {
                    const cartItemIds = cart.items.map(item => item.id);
                    const hasMainProduct = mainProductIds.some(id => cartItemIds.includes(id));
                    const hasBundledProduct = bundledProductIds.some(id => cartItemIds.includes(id));

                    // Only show the overlay if no main product is detected
                    if (!hasMainProduct && hasBundledProduct) {
                        showOverlay(`Accessories cannot be sold separately; they must be bundled with items such as mats, bedsheets, and pillowcases.`);
                    }
                })
                .catch(error => {
                    console.error('Error fetching cart:', error);
                });
        }

        // Function to create the overlay for the cart page
        function showOverlay(message) {
            const mainCartSection = document.querySelector('.cart');
            if (!mainCartSection) {
                console.error('Main cart section not found.');
                return;
            }

            const overlay = document.createElement('div');
            overlay.style.position = 'absolute';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            overlay.style.display = 'flex';
            overlay.style.justifyContent = 'center';
            overlay.style.alignItems = 'center';
            overlay.style.zIndex = '10';

            const modal = document.createElement('div');
            modal.style.backgroundColor = '#fff';
            modal.style.padding = '20px';
            modal.style.borderRadius = '8px';
            modal.style.margin = '20px';
            modal.style.fontSize = '20px';
            modal.style.textAlign = 'center';
            modal.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
            modal.textContent = message;

            const closeButton = document.createElement('button');
            closeButton.textContent = 'X Close';
            closeButton.style.display = 'flex';
            closeButton.style.margin = 'auto';
            closeButton.style.fontWeight = '600';
            closeButton.style.color = '#eb6642';
            closeButton.style.marginTop = '10px';
            closeButton.addEventListener('click', () => {
                // Use history.go(-1) to go back to the previous page
                history.go(-1);
            });

            modal.appendChild(closeButton);
            overlay.appendChild(modal);
            mainCartSection.appendChild(overlay);
        }

        // Check the cart immediately after DOM is loaded
        checkCartForBundledProducts();
    }
});
</script>

{% schema %}
{
  "name": "Cart",
  "class": "shopify-section--main-cart",
  "tag": "section",
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "totals",
      "name": "Totals",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_order_weight",
          "label": "Show order weight",
          "default": false
        },
        {
          "type": "checkbox",
          "id": "show_shipping_text",
          "label": "Show shipping/taxes text",
          "default": true
        }
      ]
    },
    {
      "type": "cart_note",
      "name": "Cart note",
      "limit": 1,
      "settings": [
        {
          "type": "number",
          "id": "max_length",
          "label": "Maximum number of characters"
        }
      ]
    },
    {
      "type": "text",
      "name": "Text",
      "settings": [
        {
          "type": "richtext",
          "id": "content",
          "label": "Content"
        }
      ]
    },
    {
      "type": "checkout_button",
      "name": "Checkout button",
      "limit": 1
    },
    {
      "type": "accelerated_buttons",
      "name": "Accelerated buttons",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "Configure accelerated payment buttons in your [payment settings](https://www.shopify.com/admin/settings/payments)."
        }
      ]
    },
    {
      "type": "offer",
      "name": "Offer",
      "limit": 2,
      "settings": [
        {
          "type": "select",
          "id": "text_alignment",
          "label": "Text alignment",
          "options": [
            {
              "value": "start",
              "label": "Left"
            },
            {
              "value": "center",
              "label": "Center"
            }
          ],
          "default": "start"
        },
        {
          "type": "select",
          "id": "icon_position",
          "label": "Icon position",
          "options": [
            {
              "value": "aligned",
              "label": "Aligned horizontally"
            },
            {
              "value": "stacked",
              "label": "Stacked"
            }
          ],
          "default": "aligned"
        },
        {
          "type": "select",
          "id": "icon",
          "label": "Icon",
          "options": [
            {
              "value": "none",
              "label": "None"
            },
            {
              "value": "picto-coupon",
              "label": "Coupon",
              "group": "Shop"
            },
            {
              "value": "picto-percent",
              "label": "Percent",
              "group": "Shop"
            },
            {
              "value": "picto-gift",
              "label": "Gift",
              "group": "Shop"
            },
            {
              "value": "picto-star",
              "label": "Star",
              "group": "Shop"
            },
            {
              "value": "picto-like",
              "label": "Like",
              "group": "Shop"
            },
            {
              "value": "picto-building",
              "label": "Building",
              "group": "Shop"
            },
            {
              "value": "picto-love",
              "label": "Love",
              "group": "Shop"
            },
            {
              "value": "picto-award-gift",
              "label": "Award gift",
              "group": "Shop"
            },
            {
              "value": "picto-happy",
              "label": "Happy",
              "group": "Shop"
            },
            {
              "value": "picto-box",
              "label": "Box",
              "group": "Shipping"
            },
            {
              "value": "picto-pin",
              "label": "Pin",
              "group": "Shipping"
            },
            {
              "value": "picto-timer",
              "label": "Timer",
              "group": "Shipping"
            },
            {
              "value": "picto-validation",
              "label": "Validation",
              "group": "Shipping"
            },
            {
              "value": "picto-truck",
              "label": "Truck",
              "group": "Shipping"
            },
            {
              "value": "picto-return",
              "label": "Return",
              "group": "Shipping"
            },
            {
              "value": "picto-earth",
              "label": "Earth",
              "group": "Shipping"
            },
            {
              "value": "picto-plane",
              "label": "Plane",
              "group": "Shipping"
            },
            {
              "value": "picto-credit-card",
              "label": "Credit card",
              "group": "Payment & Security"
            },
            {
              "value": "picto-lock",
              "label": "Lock",
              "group": "Payment & Security"
            },
            {
              "value": "picto-shield",
              "label": "Shield",
              "group": "Payment & Security"
            },
            {
              "value": "picto-secure-profile",
              "label": "Secure profile",
              "group": "Payment & Security"
            },
            {
              "value": "picto-money",
              "label": "Money",
              "group": "Payment & Security"
            },
            {
              "value": "picto-recycle",
              "label": "Recycle",
              "group": "Ecology"
            },
            {
              "value": "picto-leaf",
              "label": "Leaf",
              "group": "Ecology"
            },
            {
              "value": "picto-tree",
              "label": "Tree",
              "group": "Ecology"
            },
            {
              "value": "picto-mobile-phone",
              "label": "Mobile phone",
              "group": "Communication"
            },
            {
              "value": "picto-phone",
              "label": "Phone",
              "group": "Communication"
            },
            {
              "value": "picto-chat",
              "label": "Chat",
              "group": "Communication"
            },
            {
              "value": "picto-customer-support",
              "label": "Customer support",
              "group": "Communication"
            },
            {
              "value": "picto-operator",
              "label": "Operator",
              "group": "Communication"
            },
            {
              "value": "picto-mailbox",
              "label": "Mailbox",
              "group": "Communication"
            },
            {
              "value": "picto-envelope",
              "label": "Envelope",
              "group": "Communication"
            },
            {
              "value": "picto-comment",
              "label": "Comment",
              "group": "Communication"
            },
            {
              "value": "picto-question",
              "label": "Question",
              "group": "Communication"
            },
            {
              "value": "picto-send",
              "label": "Send",
              "group": "Communication"
            },
            {
              "value": "picto-at-sign",
              "label": "At sign",
              "group": "Tech"
            },
            {
              "value": "picto-camera",
              "label": "Camera",
              "group": "Tech"
            },
            {
              "value": "picto-wifi",
              "label": "WiFi",
              "group": "Tech"
            },
            {
              "value": "picto-bluetooth",
              "label": "Bluetooth",
              "group": "Tech"
            },
            {
              "value": "picto-printer",
              "label": "Printer",
              "group": "Tech"
            },
            {
              "value": "picto-smart-watch",
              "label": "Smart watch",
              "group": "Tech"
            },
            {
              "value": "picto-coffee",
              "label": "Coffee",
              "group": "Food & Drink"
            },
            {
              "value": "picto-burger",
              "label": "Burger",
              "group": "Food & Drink"
            },
            {
              "value": "picto-beer",
              "label": "Beer",
              "group": "Food & Drink"
            },
            {
              "value": "picto-target",
              "label": "Target",
              "group": "Other"
            },
            {
              "value": "picto-document",
              "label": "Document",
              "group": "Other"
            },
            {
              "value": "picto-jewelry",
              "label": "Jewelry",
              "group": "Other"
            },
            {
              "value": "picto-music",
              "label": "Music",
              "group": "Other"
            },
            {
              "value": "picto-file",
              "label": "File",
              "group": "Other"
            },
            {
              "value": "picto-mask",
              "label": "Mask",
              "group": "Other"
            },
            {
              "value": "picto-stop",
              "label": "Stop",
              "group": "Other"
            }
          ],
          "default": "picto-coupon"
        },
        {
          "type": "image_picker",
          "id": "custom_icon",
          "label": "Custom icon",
          "info": "240 x 240px .png recommended"
        },
        {
          "type": "range",
          "id": "icon_width",
          "min": 20,
          "max": 100,
          "step": 4,
          "unit": "px",
          "label": "Icon width",
          "default": 24
        },
        {
          "type": "text",
          "id": "title",
          "label": "Heading",
          "default": "Shipping"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "Content",
          "default": "<p>Short content about your shipping rates or discounts.</p>"
        },
        {
          "type": "color",
          "id": "background",
          "label": "Background"
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "Text"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "show_shipping_estimator",
      "label": "Show shipping estimator",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_payment_icons",
      "label": "Show payment icons",
      "default": true
    }
  ]
}
{% endschema %}