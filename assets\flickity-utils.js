/**
 * Reusable Flickity Slider Utility
 * Handles initialization, responsive behavior, and common configurations
 */

window.FlickityUtils = (function() {
  'use strict';

  // Default configurations for different slider types
  const defaultConfigs = {
    reviews: {
      cellSelector: '.review',
      cellAlign: 'left',
      contain: true,
      pageDots: true,
      prevNextButtons: true,
      freeScroll: false,
      wrapAround: true,
      draggable: true,
      imagesLoaded: true,
      adaptiveHeight: false, // Disable adaptive height for equal heights
      accessibility: true,
      groupCells: true // Enable grouping for multi-cell slides
    },
    ingredients: {
      cellSelector: '.ic-item',
      cellAlign: 'center',
      contain: true,
      pageDots: true,
      prevNextButtons: true,
      freeScroll: false,
      wrapAround: true,
      draggable: true,
      imagesLoaded: true,
      adaptiveHeight: true,
      accessibility: true,
      percentPosition: false
    }
  };

  /**
   * Initialize Flickity slider with custom options
   * @param {Object} options - Configuration object
   * @param {string} options.containerId - ID of the container element
   * @param {string} options.type - Type of slider ('reviews' or 'ingredients')
   * @param {number} options.breakpoint - Desktop breakpoint (default: 1000)
   * @param {number} options.maxDesktopItems - Max items before slider on desktop (default: 3)
   * @param {Object} options.customConfig - Custom Flickity configuration
   * @param {Function} options.onInit - Callback after initialization
   * @param {Function} options.onDestroy - Callback after destruction
   */
  function initSlider(options) {
    const {
      containerId,
      type = 'reviews',
      breakpoint = 1000,
      maxDesktopItems = 3,
      customConfig = {},
      onInit = null,
      onDestroy = null
    } = options;

    if (typeof Flickity === 'undefined') {
      console.warn('Flickity not loaded, retrying in 100ms...');
      setTimeout(() => initSlider(options), 100);
      return null;
    }

    console.log(`[${type}] Looking for container with ID: "${containerId}"`);
    const container = document.getElementById(containerId);
    if (!container) {
      console.error(`[${type}] Container with ID "${containerId}" not found`);
      return null;
    }
    console.log(`[${type}] Container found:`, container);

    const count = parseInt(container.getAttribute('data-count'), 10) || 0;
    const isDesktop = window.matchMedia(`(min-width: ${breakpoint}px)`).matches;
    // For reviews, always slide. For other types, use original logic
    const shouldSlide = type === 'reviews' ? count > 0 : (!isDesktop || count > maxDesktopItems);

    console.log(`[${type}] Analysis - Count: ${count}, Desktop: ${isDesktop}, MaxDesktopItems: ${maxDesktopItems}, ShouldSlide: ${shouldSlide}`);

    let flkty = null;

    function createSlider() {
      console.log(`[${type}] createSlider called - shouldSlide: ${shouldSlide}, count: ${count}`);

      if (!shouldSlide || count === 0) {
        console.log(`[${type}] slider: Using grid layout (Desktop: ${isDesktop}, Count: ${count})`);
        return null;
      }

      console.log(`[${type}] Initializing Flickity slider on container:`, container);

      // Check if container has items
      const items = container.querySelectorAll(defaultConfigs[type].cellSelector);
      console.log(`[${type}] Found ${items.length} items with selector "${defaultConfigs[type].cellSelector}":`, items);

      // Merge default config with custom config
      const config = { ...defaultConfigs[type], ...customConfig };
      console.log(`[${type}] Using config:`, config);

      // Special handling for single items
      if (count === 1) {
        config.draggable = false;
        config.pageDots = false;
        console.log(`[${type}] Single item detected, updated config:`, config);
      }

      // Destroy any existing Flickity instance first
      if (container.classList.contains('flickity-enabled')) {
        console.log(`[${type}] Destroying existing Flickity instance...`);
        try {
          const existingFlkty = Flickity.data(container);
          if (existingFlkty) {
            existingFlkty.destroy();
          }
        } catch (e) {
          console.log(`[${type}] Error destroying existing instance:`, e);
        }
      }

      console.log(`[${type}] About to create Flickity instance...`);
      flkty = new Flickity(container, config);
      console.log(`[${type}] Flickity instance created:`, flkty);

      // Add event listeners for debugging
      flkty.on('dragStart', function() {
        console.log(`[${type}] Drag started`);
      });
      flkty.on('dragMove', function() {
        console.log(`[${type}] Drag move`);
      });
      flkty.on('dragEnd', function() {
        console.log(`[${type}] Drag ended`);
      });
      flkty.on('settle', function() {
        console.log(`[${type}] Settled on cell:`, flkty.selectedIndex);
      });

      // Force visibility and width for items
      setTimeout(() => {
        console.log(`[${type}] Post-init timeout executing...`);
        if (flkty) {
          console.log(`[${type}] Flickity instance exists, calling resize...`);

          // Ensure all items are visible and have proper width
          const items = container.querySelectorAll(config.cellSelector);
          console.log(`[${type}] Forcing visibility and width on ${items.length} items...`);
          items.forEach((item, index) => {
            console.log(`[${type}] Item ${index} before:`, {
              display: item.style.display,
              opacity: item.style.opacity,
              visibility: item.style.visibility,
              width: item.style.width,
              offsetWidth: item.offsetWidth,
              offsetHeight: item.offsetHeight
            });

            // Force visibility
            item.style.display = 'block';
            item.style.opacity = '1';
            item.style.visibility = 'visible';

            // Force width for ingredients specifically (minimal overrides)
            if (type === 'ingredients') {
              item.style.width = '75%';
              item.style.boxSizing = 'border-box';

              // Force reflow
              item.offsetHeight;
            }

            console.log(`[${type}] Item ${index} after:`, {
              display: item.style.display,
              opacity: item.style.opacity,
              visibility: item.style.visibility,
              width: item.style.width,
              offsetWidth: item.offsetWidth,
              offsetHeight: item.offsetHeight
            });
          });

          // Force Flickity resize after width changes
          flkty.resize();
          console.log(`[${type}] Flickity resized after width changes`);

          console.log(`[${type}] Calling onInit callback...`);
          if (onInit) onInit(flkty);
        } else {
          console.error(`[${type}] Flickity instance not found in timeout!`);
        }
      }, 200);

      return flkty;
    }

    function destroySlider() {
      if (flkty) {
        console.log(`[${type}] Destroying slider...`);
        if (onDestroy) onDestroy(flkty);
        flkty.destroy();
        flkty = null;
        console.log(`[${type}] Slider destroyed`);
      }
    }

    // Initial creation
    console.log(`[${type}] Starting initial slider creation...`);
    flkty = createSlider();
    console.log(`[${type}] Initial creation result:`, flkty);

    // Handle resize
    let resizeTimeout;
    function handleResize() {
      console.log(`[${type}] Resize event triggered`);
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        const newIsDesktop = window.matchMedia(`(min-width: ${breakpoint}px)`).matches;
        const newShouldSlide = !newIsDesktop || count > maxDesktopItems;

        console.log(`[${type}] Resize analysis - NewDesktop: ${newIsDesktop}, NewShouldSlide: ${newShouldSlide}, CurrentShouldSlide: ${shouldSlide}`);

        if (newShouldSlide !== shouldSlide) {
          if (newShouldSlide && !flkty && count > 0) {
            console.log(`[${type}] Creating slider due to resize...`);
            flkty = createSlider();
          } else if (!newShouldSlide && flkty) {
            console.log(`[${type}] Destroying slider due to resize...`);
            destroySlider();
          }
        }
      }, 150);
    }

    window.addEventListener('resize', handleResize);
    console.log(`[${type}] Resize listener added`);

    // Return control object
    return {
      getInstance: () => flkty,
      destroy: destroySlider,
      resize: () => flkty && flkty.resize(),
      removeResizeListener: () => window.removeEventListener('resize', handleResize)
    };
  }

  /**
   * Quick initialization for reviews slider
   */
  function initReviewsSlider(containerId, customOptions = {}) {
    return initSlider({
      containerId,
      type: 'reviews',
      breakpoint: 1000,
      maxDesktopItems: 3,
      ...customOptions
    });
  }

  /**
   * Quick initialization for ingredients slider
   */
  function initIngredientsSlider(containerId, customOptions = {}) {
    return initSlider({
      containerId,
      type: 'ingredients',
      breakpoint: 1000,
      maxDesktopItems: 999, // Always use slider on mobile
      ...customOptions
    });
  }

  // Public API
  return {
    init: initSlider,
    initReviews: initReviewsSlider,
    initIngredients: initIngredientsSlider,
    configs: defaultConfigs
  };
})();

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  console.log('FlickityUtils loaded and ready');
});
