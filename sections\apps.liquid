{%- if section.blocks.size > 0 -%}
  {%- render 'section-spacing-collapsing' -%}

  <style>
    #shopify-section-{{ section.id }} {
      {% if section.settings.remove_vertical_spacing %}--section-spacing-block: 0px;{% endif %}
      {% if section.settings.remove_horizontal_spacing %}--section-spacing-inline: 0px;{% endif %}
    }
  </style>

  <div {% render 'section-properties' %}>
    {%- for block in section.blocks -%}
      {%- render block -%}
    {%- endfor -%}
  </div>
{%- endif -%}

{% schema %}
{
  "name": "Apps",
  "class": "shopify-section--apps",
  "tag": "section",
  "disabled_on": {
    "groups": ["custom.overlay"]
  },
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Full width",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "remove_vertical_spacing",
      "label": "Remove vertical spacing",
      "default": false
    },
    {
      "type": "checkbox",
      "id": "remove_horizontal_spacing",
      "label": "Remove horizontal spacing",
      "default": false
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ],
  "presets": [
    {
      "name": "Apps"
    }
  ]
}
{% endschema %}
