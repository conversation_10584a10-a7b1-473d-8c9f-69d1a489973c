{"sections": {"main": {"type": "main-page", "settings": {"show_title": true, "page_width": "medium"}}, "ingredients_slider_test": {"type": "custom-ingredients-card", "blocks": {"card_1": {"type": "card", "settings": {"image": "shopify://shop_images/Ginger.png", "badge": "Test 1", "badge_bg": "#1f7a4a", "badge_color": "#ffffff", "title": "Slider Test Card 1", "text": "<p>This should be in a slider (no left image).</p>", "button_label": "Read more", "modal_rte": "<p>Test modal content 1.</p>"}}, "card_2": {"type": "card", "settings": {"badge": "Test 2", "badge_bg": "#c3583e", "badge_color": "#ffffff", "title": "Slider Test Card 2", "text": "<p>This should also be in a slider.</p>", "button_label": "Read more", "modal_rte": "<p>Test modal content 2.</p>"}}, "card_3": {"type": "card", "settings": {"badge": "Test 3", "badge_bg": "#506d51", "badge_color": "#ffffff", "title": "Slider Test Card 3", "text": "<p>Third card in slider test.</p>", "button_label": "Read more", "modal_rte": "<p>Test modal content 3.</p>"}}}, "block_order": ["card_1", "card_2", "card_3"], "settings": {"heading": "Ingredients Slider Test", "subheading": "Testing slider functionality without left image", "desc": "<p>This section should use Flickity slider since no left image is present.</p>", "show_dots": true, "show_arrows": true, "enable_shadows": true}}, "ingredients_grid_test": {"type": "custom-ingredients-card", "blocks": {"left_image_test": {"type": "left_image", "settings": {"image": "shopify://shop_images/WHAT_S_INSIDE_SANLAVA.png", "alt": "Test left image"}}, "grid_card_1": {"type": "card", "settings": {"image": "shopify://shop_images/Ingredients_Layout-_Design_1_77b8fbf8-89b9-4e6b-b738-5981bc9e3d8b.png", "badge": "Grid 1", "badge_bg": "#1f7a4a", "badge_color": "#ffffff", "title": "Grid Test Card 1", "text": "<p>This should be in CSS grid with larger size (220px × 160px).</p>", "button_label": "Read more", "modal_rte": "<p>Grid test modal content 1.</p>"}}, "grid_card_2": {"type": "card", "settings": {"badge": "Grid 2", "badge_bg": "#c3583e", "badge_color": "#ffffff", "title": "Grid Test Card 2", "text": "<p>This should also be in CSS grid with larger size.</p>", "button_label": "Read more", "modal_rte": "<p>Grid test modal content 2.</p>"}}}, "block_order": ["left_image_test", "grid_card_1", "grid_card_2"], "settings": {"heading": "Ingredients Grid Test", "subheading": "Testing CSS grid with left image", "desc": "<p>This section should use CSS grid layout with increased card sizes since left image is present.</p>", "left_image_height": 400, "left_image_width": 100, "left_image_fit": "cover", "enable_shadows": true}}}, "order": ["main", "ingredients_slider_test", "ingredients_grid_test"]}