{"general": {"page": "<PERSON><PERSON><PERSON><PERSON> {{ page }}", "home": "Início", "accessibility": {"skip_to_content": "Pular para o conteúdo", "pagination": "Navegação de paginação", "go_to_page": "Ir para página {{ index }}", "go_to_item": "Ir para item {{ index }}", "item_nth_of_count": "Item {{ index }} de {{ count }}", "drag": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "next": "Próximo", "previous": "Anterior", "play_video": "Reproduzir vídeo", "read_more": "Ver mais"}, "label": {"color": "Cor", "white": "Branco", "size": "<PERSON><PERSON><PERSON>"}, "social": {"follow_on": "<PERSON><PERSON><PERSON> em {{ social_media }}", "share": "Compartilhar", "share_on": "Compartilhar em {{ social_media }}", "share_email": "Compartilhar por e-mail"}, "rating": {"info": "{{ rating_value }} de {{ rating_max }} estrelas"}, "newsletter": {"email": "E-mail", "subscribe": "<PERSON><PERSON><PERSON>", "notify_me": "Me avise", "subscribed_successfully": "Você foi inscrito em nossa newsletter."}, "localization": {"country": "<PERSON><PERSON>", "language": "Língua"}, "privacy_bar": {"accept": "Aceitar", "decline": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"max_characters": "<PERSON><PERSON><PERSON><PERSON> de {{ max_chars }} caracteres"}, "on_boarding": {"blog_post_category": "Categoria", "blog_post_title": "Artigo", "blog_post_excerpt": "Escreva sobre os seus posts.", "product_vendor": "Fabricante", "product_title": "Produ<PERSON>", "product_description": "Escreva sobre os seus produtos.", "collection_title": "Coleção"}}, "header": {"general": {"account": "Conta", "open_menu": "Abrir menu de navegação", "open_search": "<PERSON><PERSON><PERSON> pesquisa", "open_account": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>a de conta", "open_cart": "<PERSON><PERSON><PERSON><PERSON>"}}, "product": {"general": {"description": "Descrição", "view_product": "Ver produto", "quick_add": "+ <PERSON><PERSON><PERSON><PERSON>", "add_to_cart_short": "+ <PERSON><PERSON><PERSON><PERSON>", "add_to_cart_button": "Adicionar ao car<PERSON>ho", "pre_order_button": "Pré-venda", "sold_out_button": "Esgotado", "unavailable_button": "Unavailable", "added_to_cart": "Adicionado ao carrinho!", "sold_out_badge": "Esgotado", "on_sale_badge": "Promoção", "discount_badge_html": "Economize {{ savings }}", "sku": "SKU:", "variant": "<PERSON><PERSON><PERSON>", "view_in_space": "Veja em seu ambiente", "taxes_included": "Tributo incluído.", "taxes_excluded": "Tributo excluído.", "shipping_policy_html": "<a href=\"{{ link }}\" class=\"link\">Frete</a> calculado no checkout", "size_chart": "<PERSON><PERSON><PERSON> de medidas", "available_colors_count": {"one": "{{ count }} cor dispon<PERSON><PERSON>", "other": "{{ count }} cores disponíveis"}}, "gallery": {"close": "<PERSON><PERSON><PERSON>", "zoom": "Zoom na imagem", "error": "Imagem não pode ser carregada"}, "price": {"regular_price": "Preço normal", "sale_price": "Preço promocional", "from_price_html": "De {{ price_min }}"}, "quantity": {"label": "Quantidade", "increase_quantity": "Aumentar quantidade", "decrease_quantity": "Di<PERSON><PERSON>r quantidade"}, "rating_count": {"zero": "Sem avaliações", "one": "{{ count }} avaliação", "other": "{{ count }} avaliaç<PERSON>es"}, "inventory": {"in_stock": "Em estoque", "oversell_stock": "Reestoque próximo", "incoming_stock": "Reestoque em {{ next_incoming_date }}", "low_stock_with_quantity_count": {"one": "Apenas {{ count }} unidade disponível", "other": "Apenas {{ count }} unidades disponíveis"}}, "store_availability": {"view_store_info": "<PERSON><PERSON> as informações da loja", "check_other_stores": "Verificar a disponibilidade em outras lojas", "pick_up_available": "Retirada disponível", "pick_up_currently_unavailable": "Re<PERSON>rada indisponível no momento", "pick_up_available_at": "Retirada disponível em {{ location_name }}", "pick_up_unavailable_at": "Re<PERSON>rada em {{ location_name }} indisponível no momento"}}, "collection": {"general": {"empty_collection": "Essa coleção está vazia", "all_collections": "<PERSON><PERSON> as cole<PERSON><PERSON><PERSON>", "no_collections": "Essa loja não tem nenhuma coleção.", "continue_shopping": "Continuar comprando"}, "products_count": {"zero": "Nenhum produto", "one": "{{ count }} produto", "other": "{{ count }} produtos"}, "faceting": {"filters": "<PERSON><PERSON><PERSON>", "filter_and_sort": "Filtro e ordem", "filter_button": "Filtrar", "clear_filters": "Limpar", "apply_filters": "Aplicar", "sort_by": "Ordenar por", "remove_filter": "Remover filtro \"{{ name }}\"", "no_results": "Nenhum produto encontrado para esses filtros.", "price_range_to": "até", "price_filter_from": "De preço", "price_filter_to": "<PERSON><PERSON> pre<PERSON>o", "price_filter": "{{ min_price }} - {{ max_price }}", "availability_label": "Apenas em estoque"}}, "blog": {"general": {"empty_blog": "Esse blog está vazio", "back_to_home": "Voltar ao início", "view": "<PERSON>er", "all_posts": "Todos os posts"}, "post": {"written_by": "Por {{ author }}", "share": "Compartilhar", "tags": "Tags", "continue_reading": "<PERSON><PERSON><PERSON><PERSON> lendo"}, "comments": {"leave_comment": "<PERSON><PERSON><PERSON>", "moderated": "Os comentários precisam ser aprovados antes da publicação.", "name": "Nome", "email": "E-mail", "message": "<PERSON><PERSON><PERSON><PERSON>", "submit": "Publicar comentário", "comment_sent": "Comentário enviado. Será publicado depois que o lojista aprova-lo.", "comment_published": "<PERSON><PERSON> coment<PERSON>rio foi publicado.", "count": {"zero": "<PERSON><PERSON><PERSON>", "one": "{{ count }} coment<PERSON><PERSON>", "other": "{{ count }} coment<PERSON><PERSON>s"}}}, "contact": {"form": {"name": "Nome", "email": "E-mail", "message": "Mensagem", "submit": "Enviar mensagem", "success_message": "Sua mensagem foi enviada."}}, "customer": {"account": {"welcome": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, {{first_name}}", "tagline": "Veja seus pedidos e modifique as informações de sua conta.", "orders": "Pedidos", "addresses": "Endereços", "logout": "<PERSON><PERSON>", "no_orders": "Você ainda não fez nenhum pedido.", "continue_shopping": "Continuar comprando"}, "login": {"title": "Entrar", "email": "E-mail", "password": "<PERSON><PERSON>", "submit": "Entrar", "forgot_password": "Esqueceu sua senha?", "sign_up": "C<PERSON><PERSON> conta"}, "recover_password": {"title": "<PERSON><PERSON><PERSON><PERSON> se<PERSON>a", "email": "E-mail", "submit": "Recuperar", "back_to_login": "Voltar ao login", "success_message": "Enviamos um e-mail com instruções para recuperar sua senha."}, "register": {"title": "C<PERSON><PERSON> conta", "first_name": "Nome", "last_name": "Sobrenome", "email": "E-mail", "password": "<PERSON><PERSON>", "accepts_marketing": "Se inscreva em nossa newsletter", "submit": "C<PERSON><PERSON> conta", "login": "Entrar"}, "activate_account": {"title": "Ativar conta", "instructions": "Escolha uma senha para criar sua conta:", "password": "<PERSON><PERSON>", "password_confirmation": "Confirmar <PERSON><PERSON><PERSON>", "submit": "Ativar", "cancel": "<PERSON><PERSON><PERSON>"}, "reset_password": {"title": "<PERSON><PERSON><PERSON><PERSON>", "password": "<PERSON><PERSON>", "password_confirmation": "Confirmar <PERSON><PERSON><PERSON>", "submit": "Redefinir"}, "order": {"order": "Pedido", "order_name": "Pedido {{name}}", "view_details": "Detalhes do pedido", "back": "Voltar", "date": "Data", "payment_status": "Status do pagamento", "fulfillment_status": "Status de processamento do pedido", "cancelled_on": "Cancelado em {{date}}. Motivo: {{reason}}", "product": "Produ<PERSON>", "quantity": "Quantidade", "fulfillment_with_number": "Seu pedido foi enviado em {{date}}. Rastrear envio com código {{tracking_number}}.", "fulfillment": "Seu pedido foi enviado em {{date}}.", "track_shipment": "<PERSON><PERSON><PERSON><PERSON> en<PERSON>", "subtotal": "Subtotal", "discount": "Desconto", "shipping": "Frete", "taxes_included": "Tributo incluído", "taxes_excluded": "Tributo excluído", "total_duties": "Tributos de importação", "refunded_amount": "Quantia estornado", "total": "Total", "shipping_address": "Endereço de entrega", "billing_address": "Endereço de faturamento"}, "addresses": {"no_addresses": "Você ainda não salvou nenhum endereço.", "add_address": "<PERSON><PERSON><PERSON><PERSON>", "edit_address": "<PERSON><PERSON>", "save_address": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir", "default_address": "Endereço padrão", "address_title": "Endereço {{ position }}", "delete_confirm": "Excluir endereço? Após confirmada, essa ação não pode ser revertida.", "fill_form": "<PERSON><PERSON><PERSON> as informações aba<PERSON>o:", "first_name": "Nome", "last_name": "Sobrenome", "company": "Empresa", "phone": "Telefone", "address1": "Endereço", "address2": "Complemento", "city": "Cidade", "zip": "CEP", "country": "País/Região", "province": "Estado", "set_default": "Usar como endereço padrão"}}, "cart": {"general": {"title": "<PERSON><PERSON><PERSON>", "empty": "<PERSON><PERSON> carrinho está vazio", "continue_shopping": "Continuar comprando", "weight": "Peso", "subtotal": "Subtotal", "total": "Total", "taxes_and_shipping_policy_at_checkout_html": "Tributos e <a href=\"{{ link }}\" class=\"link\">frete</a> calculados na finalização da compra", "taxes_included_but_shipping_at_checkout": "Tributos incluídos e frete calculado na finalização da compra", "taxes_included_and_shipping_policy_html": "Tributos incluídos. <a href=\"{{ link }}\" class=\"link\">Frete</a> calculado na finalização da compra.", "taxes_and_shipping_at_checkout": "Tributos e frete calculados na finalização da compra", "add_order_note": "Adicionar observações sobre o pedido", "edit_order_note": "Editar observações sobre o pedido", "order_note": "Observações", "save_note": "Salvar observações", "view_cart": "<PERSON><PERSON><PERSON><PERSON>", "checkout": "Checkout", "we_accept": "Aceitamos"}, "order": {"product": "Produ<PERSON>", "total": "Total", "quantity": "Quantidade", "change_quantity": "<PERSON><PERSON> quantidade", "increase_quantity": "Aumentar quantidade", "decrease_quantity": "Di<PERSON><PERSON>r quantidade", "remove": "Remover"}, "free_shipping_bar": {"limit_unreached_html": "Gaste mais {{ remaining_amount }} e tenha frete grátis!", "limit_reached_html": "Você está elegível para frete grátis."}, "shipping_estimator": {"estimate_shipping": "Est<PERSON><PERSON> frete", "country": "<PERSON><PERSON>", "province": "Estado", "zip": "CEP", "estimate": "Estimar", "no_results": "Descul<PERSON>, não enviamos para o seu endereço.", "one_result": "Temos um método de envio para o seu endereço:", "multiple_results": "Temos diversos métodos de envio para o seu endereço:", "error": "Um ou mais erros aconteceram durante a estimativa de frete:"}}, "404": {"general": {"title": "Página não encontrada", "continue_shopping": "Continuar comprando"}}, "search": {"general": {"title": "<PERSON><PERSON><PERSON><PERSON>", "terms": "Resultad<PERSON> para \"{{terms}}\"", "search_placeholder": "Pesquisar por...", "products": "<PERSON><PERSON><PERSON>", "suggestions": "Sugestões", "collections": "Coleções", "posts": "Posts de blog", "pages": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Limpar", "view_all": "Ver tudo", "view_all_results": "Ver todos os resultados", "no_results": "Não foram encontrados resultados para sua pesquisa"}, "results_count": {"zero": "Nenhum resultado para \"{{ terms }}\"", "one": "{{ count }} resultado para \"{{ terms }}\"", "other": "{{ count }} resultados para \"{{ terms }}\""}}, "gift_card": {"general": {"title": "Aqui está seu cartão-presente", "copy": "Copiar", "print": "Imprimir", "scan": "ou faça a leitura desse QR Code", "back_to_store": "Voltar à loja"}, "issued": {"remaining_amount": "Quantia restante", "out_of_html": "de {{ initial_value }}", "redeem_instructions": "Use esse código para resgatar seu cartão-presente durante o checkout:", "code": "Código do cartão-presente", "expires_on": "Expira em: {{ expires_on }}", "expired": "<PERSON><PERSON>-presente expirou ou foi desativado.", "add_to_apple_wallet": "Adicionar à Apple Wallet"}, "recipient": {"checkbox": "Quero enviar como presente", "email_label": "E-mail do destinatário", "name_label": "Nome do destinatário (opcional)", "send_on_label": "Enviar em (opcional)", "message_label": "Mensagem (opcional)"}}, "password": {"general": {"follow_us": "Siga-nos", "powered_by": "Esta loja terá a tecnologia da", "store_owner": "Titular da loja?", "login": "Entrar"}, "storefront_access": {"enter_password": "Entrar usando senha", "store_access": "<PERSON><PERSON> da loja", "instructions": "Insira a senha para acessar a loja", "password": "<PERSON><PERSON>", "enter_store": "Entrar na loja"}}, "apps": {"shopify_reviews": {"review_info": "Esse produto é avaliado {{ rating_value }} de {{ rating_max }} estrelas.", "leave_review": "<PERSON><PERSON>ar <PERSON>", "review_count": {"zero": "Esse produto ainda não tem avaliações.", "one": "Recebeu {{ count }} avaliação.", "other": "Recebeu {{ count }} avaliações."}}}}