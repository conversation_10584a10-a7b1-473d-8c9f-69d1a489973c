{%- if section.blocks.size > 0 -%}
  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  CSS
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}
  <style>
    :root {
      --sticky-announcement-bar-enabled: {%- if section.settings.enable_sticky -%}1{% else %}0{% endif %};
    }

    {%- if section.settings.enable_sticky -%}
      #shopify-section-{{ section.id }} {
        position: sticky;
        top: 0;
        z-index: 20;
      }

      .shopify-section--header ~ #shopify-section-{{ section.id }} {
        top: calc(var(--sticky-header-enabled, 0) * var(--header-height, 0px));
      }
    {%- endif -%}
  </style>

  {%- comment -%}
  ------------------------------------------------------------------------------------------------------------------------
  LIQUID
  ------------------------------------------------------------------------------------------------------------------------
  {%- endcomment -%}

  <height-observer variable="announcement-bar">
    <div {% render 'surface', class: 'announcement-bar', background_gradient: section.settings.background_gradient, background: section.settings.background, text_color: section.settings.text_color %}>
      {%- if section.settings.navigation_mode == 'scrolling' -%}
        <marquee-text scrolling-speed="{{ section.settings.scrolling_speed }}" class="announcement-bar__scrolling-list">
          {%- capture content -%}
            {%- for block in section.blocks -%}
              <p class="bold {{ section.settings.text_size }}" {{ block.shopify_attributes }}>
                {%- if block.settings.url != blank -%}
                  <a href="{{ block.settings.url }}">{{ block.settings.text | escape }}</a>
                {%- else -%}
                  {{ block.settings.text | escape }}
                {%- endif -%}
              </p>

              <span class="shape-circle shape--sm"></span>
            {%- endfor -%}
          {%- endcapture -%}

          {%- for i in (1..10) -%}
            <span class="announcement-bar__item" {% unless forloop.first %}aria-hidden="true"{% endunless %}>
              {{- content -}}
            </span>
          {%- endfor -%}
        </marquee-text>
      {%- else -%}
        <div class="container">
          <div class="announcement-bar__wrapper {% if section.blocks.size == 1 %}justify-center{% else %}justify-between{% endif %}">
            {%- if section.blocks.size > 1 -%}
              <button class="tap-area" is="prev-button" aria-controls="announcement-bar" aria-label="{{ 'general.accessibility.previous' | t }}">
                {%- render 'icon' with 'chevron-left', direction_aware: true -%}
              </button>
            {%- endif -%}

            <announcement-bar swipeable id="announcement-bar" class="announcement-bar__static-list">
              {%- for block in section.blocks -%}
                <p class="bold {{ section.settings.text_size }} {% unless forloop.first %}reveal-invisible{% endunless %}" {{ block.shopify_attributes }}>
                  {%- if block.settings.url != blank -%}
                    <a href="{{ block.settings.url }}">{{ block.settings.text | escape }}</a>
                    {%- elsif block.settings.emoji != blank -%}
                    <span class="emoji-ico">{{ block.settings.emoji | escape }}</span>{{ block.settings.text | escape }}
                  {%- else -%}
                    {{ block.settings.text | escape }}
                  {%- endif -%}
                </p>
              {%- endfor -%}
            </announcement-bar>
<style>
@keyframes blink {
  50% { opacity: 0; }
}

.emoji-ico {
  display: inline-block;
  margin-right: 5px;
  animation: blink 3s infinite;
  font-size: 10px;
  vertical-align: middle;
}
</style>

            
            {%- if section.blocks.size > 1 -%}
              <button class="tap-area" is="next-button" aria-controls="announcement-bar" aria-label="{{ 'general.accessibility.next' | t }}">
                {%- render 'icon' with 'chevron-right', direction_aware: true -%}
              </button>
            {%- endif -%}
          </div>
        </div>
      {%- endif -%}
    </div>
  </height-observer>

  <script>
    document.documentElement.style.setProperty('--announcement-bar-height', Math.round(document.getElementById('shopify-section-{{ section.id }}').clientHeight) + 'px');
  </script>
{%- endif -%}

{% schema %}
{
  "name": "Announcement bar",
  "class": "shopify-section--announcement-bar",
  "tag": "aside",
  "max_blocks": 5,
  "blocks": [
    {
      "type": "message",
      "name": "Message",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "label": "Text",
          "default": "Announce something here"
        },
        {
          "type": "text",
          "id": "emoji",
          "label": "Blink/Glowing Emoji",
          "placeholder": "Add your glowing/blinking emoji here"
        },
        {
          "type": "url",
          "id": "url",
          "label": "Link"
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "checkbox",
      "id": "enable_sticky",
      "label": "Enable sticky bar",
      "default": false
    },
    {
      "type": "select",
      "id": "text_size",
      "label": "Text size",
      "options": [
        {
          "value": "text-xxs",
          "label": "X-Small"
        },
        {
          "value": "text-xs",
          "label": "Small"
        },
        {
          "value": "text-base",
          "label": "Medium"
        },
        {
          "value": "text-lg",
          "label": "Large"
        }
      ],
      "default": "text-xs"
    },
    {
      "type": "select",
      "id": "navigation_mode",
      "label": "Multiple message navigation",
      "options": [
        {
          "value": "arrows",
          "label": "Arrows"
        },
        {
          "value": "scrolling",
          "label": "Auto-scrolling"
        }
      ],
      "default": "arrows"
    },
    {
      "type": "range",
      "id": "scrolling_speed",
      "label": "Scrolling speed",
      "min": 10,
      "max": 30,
      "unit": "s",
      "default": 20
    },
    {
      "type": "header",
      "content": "Colors",
      "info": "Gradient replaces solid colors when set."
    },
    {
      "type": "color",
      "id": "background",
      "label": "Background"
    },
    {
      "type": "color_background",
      "id": "background_gradient",
      "label": "Background gradient"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text"
    }
  ]
}
{% endschema %}