{%- comment -%}
Custom Image + Text (Swap)
- Two-column layout on desktop; stack on mobile
- Image column can be left or right via section setting
- On mobile, order follows block arrangement (like Custom Aspects)
- Button is a link (with URL and option to open in new tab)
{%- endcomment -%}

{{ 'custom-typography.css' | asset_url | stylesheet_tag }}

<section class="cits{% if section.settings.image_position == 'right' %} is-image-right{% endif %}" aria-label="Image and text">
  <style>
    #shopify-section-{{ section.id }} {
      --cits-gap: 28px;
      --cits-max: 1200px;
      --cits-radius: 12px;
      background-color: {{ section.settings.section_bg | default: 'transparent' }};
    }

    #shopify-section-{{ section.id }} .cits-wrapper { max-width: var(--cits-max); margin: 0 auto; }

    /* Desktop two-column */
    @media (min-width: 1000px) {
      #shopify-section-{{ section.id }} .cits-desktop { display: grid; grid-template-columns: 1fr 1fr; gap: var(--cits-gap); align-items: center; }
      #shopify-section-{{ section.id }} .cits-mobile { display: none; }
      #shopify-section-{{ section.id }} .cits.is-image-right .cits-desktop { direction: rtl; }
      #shopify-section-{{ section.id }} .cits.is-image-right .cits-desktop > * { direction: ltr; }
    }

    /* Mobile stack follows block order */
    @media (max-width: 999px) {
      #shopify-section-{{ section.id }} .cits-desktop { display: none; }
      #shopify-section-{{ section.id }} .cits-mobile { display: grid; gap: var(--cits-gap); }

      /* Mobile-specific RTE paragraph adjustments */
      #shopify-section-{{ section.id }} .cits-text.rte p {
        margin: 0 0 14px 0;
        line-height: 1.5;
      }
      #shopify-section-{{ section.id }} .cits-text.rte ul,
      #shopify-section-{{ section.id }} .cits-text.rte ol {
        margin: 14px 0;
      }
      #shopify-section-{{ section.id }} .cits-text.rte li {
        margin: 6px 0;
      }
    }

    #shopify-section-{{ section.id }} .cits-image img { width: 100%; height: auto; border-radius: var(--cits-radius); display: block; }
    #shopify-section-{{ section.id }} .cits-heading { font-weight: 800; margin: 0; /* Typography handled by .custom-heading-secondary class */ }
    #shopify-section-{{ section.id }} .cits-text { color: rgb(var(--text-primary) / .9); }

    /* RTE paragraph spacing and formatting for Custom Image + Text */
    #shopify-section-{{ section.id }} .cits-text.rte p {
      margin: 0 0 16px 0;
      line-height: 1.6;
    }
    #shopify-section-{{ section.id }} .cits-text.rte p:last-child {
      margin-bottom: 0;
    }
    #shopify-section-{{ section.id }} .cits-text.rte ul,
    #shopify-section-{{ section.id }} .cits-text.rte ol {
      margin: 16px 0;
      padding-left: 1.25rem;
    }
    #shopify-section-{{ section.id }} .cits-text.rte li {
      margin: 8px 0;
      line-height: 1.6;
    }
    #shopify-section-{{ section.id }} .cits-text.rte strong {
      font-weight: 700;
    }
    #shopify-section-{{ section.id }} .cits-text.rte em {
      font-style: italic;
    }
    #shopify-section-{{ section.id }} .cits-text.rte a {
      color: rgb(var(--accent-color, var(--text-primary)));
      text-decoration: underline;
    }
    #shopify-section-{{ section.id }} .cits-text.rte a:hover {
      text-decoration: none;
    }

    /* Copy CTA container styling from custom-hero */
    #shopify-section-{{ section.id }} .cits-cta { margin-block: 8px 10px; }

    /* Copy button styling from custom-hero */
    #shopify-section-{{ section.id }} .cits-button {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      padding: 14px 22px;
      border-radius: 10px;
      text-decoration: none;
      font-weight: 700;
      box-shadow: 0 2px 0 rgb(0 0 0 / .08) inset, 0 2px 10px rgb(0 0 0 / .12);
    }
    #shopify-section-{{ section.id }} .cits-button__icon { width: 20px; height: 20px; fill: currentColor; }
    #shopify-section-{{ section.id }} .cits-button:hover { filter: brightness(0.98); transform: translateY(-1px); }

    /* Copy guarantee styling from custom-hero */
    #shopify-section-{{ section.id }} .cits-guarantee {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      margin-top: 8px;
      font-weight: 500;
    }
    #shopify-section-{{ section.id }} .cits-guarantee__icon { width: 18px; height: 18px; fill: currentColor; }
  </style>

  <div class="cits-wrapper">
    {%- comment -%} Mobile: render blocks in authored order {%- endcomment -%}
    <div class="cits-mobile">
      {%- for block in section.blocks -%}
        {%- case block.type -%}
          {%- when 'image' -%}
            <div class="cits-image" {{ block.shopify_attributes }}>
              {%- if block.settings.image != blank -%}
                <img src="{{ block.settings.image | image_url: width: 1600 }}" width="{{ block.settings.image.width }}" height="{{ block.settings.image.height }}" alt="{{ block.settings.image.alt | escape }}" loading="lazy">
              {%- endif -%}
            </div>

          {%- when 'heading' -%}
            <h2 class="cits-heading custom-heading-secondary custom-image-text" {{ block.shopify_attributes }}>{{ block.settings.title | escape }}</h2>

          {%- when 'text' -%}
            <div class="cits-text rte custom-body-text" {{ block.shopify_attributes }}>{{ block.settings.body }}</div>

          {%- when 'button' -%}
            <div class="cits-cta" {{ block.shopify_attributes }}>
              {%- if block.settings.label != blank -%}
                {% assign link_href = block.settings.url | default: routes.root_url %}
                <a class="cits-button" href="{{ link_href }}"{% if block.settings.new_window %} target="_blank" rel="noopener"{% endif %} style="background: {{ block.settings.button_bg | default: '#c3583e' }}; color: {{ block.settings.button_text | default: '#ffffff' }};">
                  <span>{{ block.settings.label }}</span>
                  <svg class="cits-button__icon" viewBox="0 0 24 24" aria-hidden="true"><path d="M13.172 12l-4.95-4.95 1.414-1.414L16 12l-6.364 6.364-1.414-1.414z"/></svg>
                </a>
              {%- endif -%}
            </div>
            {%- if block.settings.guarantee_text != blank -%}
              <div class="cits-guarantee" {{ block.shopify_attributes }}>
                <svg viewBox="0 0 24 24" class="cits-guarantee__icon" aria-hidden="true" style="fill: {{ block.settings.button_bg | default: '#c3583e' }};"><path d="M12 2l7 4v6c0 5-3.5 9.5-7 10-3.5-.5-7-5-7-10V6l7-4zm-1 13l6-6-1.41-1.41L11 12.17l-2.59-2.58L7 11l4 4z"/></svg>
                <span>{{ block.settings.guarantee_text }}</span>
              </div>
            {%- endif -%}
        {%- endcase -%}
      {%- endfor -%}
    </div>

    {%- comment -%} Desktop: two columns with image position toggle {%- endcomment -%}
    <div class="cits-desktop">
      <div class="cits-image">
        {%- for block in section.blocks -%}
          {%- if block.type == 'image' -%}
            {%- if block.settings.image != blank -%}
              <img src="{{ block.settings.image | image_url: width: 1800 }}" width="{{ block.settings.image.width }}" height="{{ block.settings.image.height }}" alt="{{ block.settings.image.alt | escape }}" loading="lazy">
            {%- endif -%}
          {%- endif -%}
        {%- endfor -%}
      </div>
      <div class="cits-content">
        {%- for block in section.blocks -%}
          {%- case block.type -%}
            {%- when 'image' -%}{% comment %}skip in content column{% endcomment %}
            {%- when 'heading' -%}
              <h2 class="cits-heading custom-heading-secondary custom-image-text" {{ block.shopify_attributes }}>{{ block.settings.title | escape }}</h2>
            {%- when 'text' -%}
              <div class="cits-text rte custom-body-text" {{ block.shopify_attributes }}>{{ block.settings.body }}</div>
            {%- when 'button' -%}
              <div class="cits-cta" {{ block.shopify_attributes }}>
                {%- if block.settings.label != blank -%}
                  {% assign link_href = block.settings.url | default: routes.root_url %}
                  <a class="cits-button" href="{{ link_href }}"{% if block.settings.new_window %} target="_blank" rel="noopener"{% endif %} style="background: {{ block.settings.button_bg | default: '#c3583e' }}; color: {{ block.settings.button_text | default: '#ffffff' }};">
                    <span>{{ block.settings.label }}</span>
                    <svg class="cits-button__icon" viewBox="0 0 24 24" aria-hidden="true"><path d="M13.172 12l-4.95-4.95 1.414-1.414L16 12l-6.364 6.364-1.414-1.414z"/></svg>
                  </a>
                {%- endif -%}
              </div>
              {%- if block.settings.guarantee_text != blank -%}
                <div class="cits-guarantee" {{ block.shopify_attributes }}>
                  <svg viewBox="0 0 24 24" class="cits-guarantee__icon" aria-hidden="true" style="fill: {{ block.settings.button_bg | default: '#c3583e' }};"><path d="M12 2l7 4v6c0 5-3.5 9.5-7 10-3.5-.5-7-5-7-10V6l7-4zm-1 13l6-6-1.41-1.41L11 12.17l-2.59-2.58L7 11l4 4z"/></svg>
                  <span>{{ block.settings.guarantee_text }}</span>
                </div>
              {%- endif -%}
          {%- endcase -%}
        {%- endfor -%}
      </div>
    </div>
  </div>

  {% schema %}
  {
    "name": "Custom Image + Text",
    "tag": "section",
    "class": "section cits-section",
    "settings": [
      {"type": "select", "id": "image_position", "label": "Image position (desktop)", "default": "left", "options": [
        {"value": "left", "label": "Left"},
        {"value": "right", "label": "Right"}
      ]},
{"type": "color", "id": "section_bg", "label": "Section background", "default": "transparent"}
    ],
    "blocks": [
      {"type": "image", "name": "Image", "settings": [
        {"type": "image_picker", "id": "image", "label": "Image"}
      ]},
      {"type": "heading", "name": "Heading", "settings": [
        {"type": "text", "id": "title", "label": "Title", "default": "Title"}
      ]},
      {"type": "text", "name": "Text", "settings": [
        {"type": "richtext", "id": "body", "label": "Text field", "default": "<p>Text field</p>"}
      ]},
      {"type": "button", "name": "Button", "settings": [
        {"type": "text", "id": "label", "label": "Button label", "default": "Try RYZE today"},
        {"type": "url", "id": "url", "label": "Button link"},
        {"type": "checkbox", "id": "new_window", "label": "Open in new tab", "default": false},
        {"type": "color", "id": "button_bg", "label": "Button background", "default": "#c3583e"},
        {"type": "color", "id": "button_text", "label": "Button text", "default": "#ffffff"},
        {"type": "text", "id": "guarantee_text", "label": "Guarantee text", "default": "30-Day Money Back Guarantee"}
      ]}
    ],
    "presets": [
      {
        "name": "Custom Image + Text",
        "blocks": [
          {"type": "image"},
          {"type": "heading"},
          {"type": "text"},
          {"type": "button"}
        ]
      }
    ]
  }
  {% endschema %}
</section>

