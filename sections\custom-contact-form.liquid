<style>
.contact-form{
    max-width:850px;
    margin: auto;
}
.contact-input{
    display: flex;
    flex-direction: column;
}
.contact-form h1{
   justify-self: center;
}
.contact-form input, textarea{
    margin-top: 5px;
    margin-bottom: 5px;
    padding: 10px;
    font-size: 16px;
}

.contact-form label {
    font-size: 18px;
}

.contact-form textarea {
    height:200px;
}
.input-grid{
    display: flex;
    flex-direction: row;
    width: 100%;
    gap: 20px;
}
.name-field, .email-field{
    display:flex;
    flex-direction: column;
    width: 50%;
}
  input, textarea{
    border: 1px solid gray;
    border-radius: 5px;
    background-color:#f3f3f3;
  }

@media only screen and (max-width: 768px){
    .input-grid{
    flex-direction: column;
    gap: 0;
}
.name-field, .email-field{
    width: 100%;
}
}
.send-message{
    font-size: 20px;
    color: white;
    background-color: #d6684b;
    padding:10px;
    border:1px solid;
    border-radius: 10px;
    margin-top: 10px;
}

.send-message:hover {
  background-color: #d28b86;
  
}
.contact-form-alert{
    width:100%;
    text-align: center;
    padding-top: 15px;
    padding-bottom: 15px;
    font-size: 20px;
    margin-bottom:10px;
    background-color:#ade1ad;
    display:none;
}
</style>

<div class="contact-form">
<h1 style="font-size: 40px; font-weight: 600">{{ section.settings.header }} </h1>

<h2 style="font-size: 18px; margin-bottom: 10px; margin-top: 10px;">{{ section.settings.sub_header }} </h2>

<form id="contactForm">
<div>📧 <span>{{ section.settings.receiver_email }} </span>
<input id="store_email" name="store_email" value="{{ section.settings.receiver_email }}" hidden>

</div>
    <div id="responseMessage" class="contact-form-alert"></div>
    
<div class="input-grid">
    
<div class="name-field">
{% assign name_label = 'contact-us.form.name' | t %}
<label for="name">
    {{name_label}}
</label>
  <input class="input-name" type="text" id="name" name="name" required>
  </div>
  <div class="email-field">
  <label for="email">{{ 'contact-us.form.email' | t }}</label>
  <input class="input-email" type="email" id="email" name="email" required>
  </div>
</div>
  
 <div class="contact-input"> 
<label for="phone">{{ 'contact-us.form.phone' | t}}</label>
  <input class="input-phone" type="text" id="phone" name="phone">
  
  <label for="message">{{ 'contact-us.form.message' | t }}</label>
  <textarea class="input-message" id="message" name="message" required></textarea>
</div>
  <button type="submit" class="send-message">{{ 'contact-us.form.submit' | t }}</button>
</form>


</div>

<script>
document.getElementById("contactForm").addEventListener("submit", async function(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);

    // Get the current date and time
    const currentDate = new Date();
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric', 
        hour: 'numeric', 
        minute: 'numeric', 
        hour12: true 
    };
    const dateTime = new Intl.DateTimeFormat('en-US', options).format(currentDate);

    // Create a subject that includes the current date and time
    const subject = `New customer message on ${dateTime}`;

    const requestBody = {
        shop: "{{ shop.domain }}", // Ensure this is correctly populated in your templating engine
        name: formData.get("name"),
        email: formData.get("email"),
        phone: formData.get("phone"),
        message: formData.get("message"),
        store_email: formData.get("store_email"),
        subject: subject
    };

    try {
        // Make the API request to the Vercel endpoint
        const response = await fetch("https://flowground-api.vercel.app/api/send-email", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(requestBody)
        });

        const result = await response.json();

        // Get response message element
        const responseMessage = document.getElementById("responseMessage");

        // Update response message text
        responseMessage.textContent = result.success ? "{{ 'contact-us.form.success_message' | t }}" : "Error sending message.";
        
        if (result.success) {
            responseMessage.style.display = "block"; // Show the message

            // Clear form inputs
            form.querySelectorAll("input, textarea").forEach(field => field.value = "");
        }
    } catch (error) {
        console.error("Error submitting form:", error);
        document.getElementById("responseMessage").textContent = "{{ 'contact-us.form.error_message' | t }}";
        document.getElementById("responseMessage").style.display = "block"; // Show error message
    }
});

</script>


{% schema %}
  {
  "name": "Custom Contact Form",
  "settings": [
    {
      "type": "text",
      "id": "header",
      "label": "Header"
    },
    {
      "type": "text",
      "id": "sub_header",
      "label": "Sub Header"
    },
    {
      "type": "text",
      "id": "receiver_email",
      "label": "Receiver Email"
    }
  ],
  "presets": [
    {
      "name": "Custom Contact Form"
    }
  ]
}  
{% endschema %}

