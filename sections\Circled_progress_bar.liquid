<!-- Real Impact, Real Numbers Section -->
<section class="real-impact-section">
  <div class="container">
    <div class="content">
      <div class="image">
        <img src="{{ section.settings.image | img_url: 'master' }}" alt="Product Image" />
      </div>
      <div class="text-content">
        <h2 class="section-title h2">{{ section.settings.title }}</h2>
        <div class="numbers-grid">
          {% for block in section.blocks %}
            <div class="number-item">
              <div class="circle-progress">
                <svg class="progress-circle" width="100" height="100">
                  <circle cx="50" cy="50" r="45"></circle>
                  <circle cx="50" cy="50" r="45" class="progress" data-percentage="{{ block.settings.percentage }}"></circle>
                </svg>
                <div class="number">{{ block.settings.percentage }}%</div>
              </div>
              <p class="description">{{ block.settings.description }}</p>
            </div>
          {% endfor %}
        </div>
        <p class="note">{{ section.settings.note }}</p>
      </div>
    </div>
  </div>
  
  <!-- CSS Styling -->
  <style>
    .real-impact-section {
      padding: 40px 0;
    }
    
    .real-impact-section .content {
      display: grid;
    grid-template-columns: repeat(2, 1fr);
    place-items: center;
    column-gap: 25px;
    }

    .real-impact-section .image img {
      max-width: 100%;
      border-radius: 10px;
    }

    .real-impact-section .text-content {
      
    }

    .real-impact-section .section-title {
      margin-bottom: 25px;
      text-align: center;
    }

    .real-impact-section .numbers-grid {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    .real-impact-section .number-item {
      display: flex;
      align-items: center;
    }

    .real-impact-section .circle-progress {
      position: relative;
      width: 100px;
      height: 100px;
      margin-right: 20px;
    }

    .real-impact-section .progress-circle {
      transform: rotate(-90deg);
    }

    .real-impact-section .progress-circle circle {
      fill: none;
      stroke-width: 10;
    }

    .real-impact-section .progress-circle circle:first-child {
      stroke: #e6e6e6;
    }

    .real-impact-section .progress-circle circle.progress {
      stroke: #000000;
      stroke-dasharray: 283;
      stroke-dashoffset: 283;
      transition: stroke-dashoffset 1s ease-out;
    }

    .real-impact-section .number {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .real-impact-section .description {    }

    .real-impact-section .note {
      margin-top: 20px;
      font-size: 14px;
    }

    @media screen and (max-width: 750px) {

      .real-impact-section .content {
      display: grid;
    grid-template-columns: 1fr;
    place-items: center;
    row-gap: 25px;
    }    
      .real-impact-section .circle-progress {
      position: relative;
      width: 100px;
      height: 100px;
      margin-right: 20px;
    }
    }
  </style>

  <!-- JavaScript for Progress Animation -->
  <script>
    document.addEventListener("DOMContentLoaded", function() {
      const circles = document.querySelectorAll('.progress-circle .progress');
      
      circles.forEach(circle => {
        const radius = circle.r.baseVal.value;
        const circumference = 2 * Math.PI * radius;
        circle.style.strokeDasharray = `${circumference}`;
        circle.style.strokeDashoffset = `${circumference}`;
        
        const percentage = circle.getAttribute('data-percentage');
        const offset = circumference - (percentage / 100 * circumference);
        circle.style.strokeDashoffset = offset;
      });
    });
  </script>
</section>

<!-- Shopify Section Schema -->
{% schema %}
{
  "name": "Circled Progress Bar",
  "settings": [
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Real Impact, Real Numbers"
    },
    {
      "type": "text",
      "id": "note",
      "label": "Note",
      "default": "Based on 200 customer responses after one month of using our Mencore Shaper Tank."
    }
  ],
  
  "presets": [
    {
      "name": "Circled Progress Bar"
    }
  ],
  "blocks": [
    {
      "type": "number_block",
      "name": "Number Block",
      "settings": [
        {
          "type": "number",
          "id": "percentage",
          "label": "Percentage",
          "default": 95
        },
        {
          "type": "text",
          "id": "description",
          "label": "Description",
          "default": "See a significant reduction in man boobs, boosting their confidence and allowing them to stylishly wear their favorite shirts and outfits."
        }
      ]
    }
  ],
}
{% endschema %}
