{{ 'custom-typography.css' | asset_url | stylesheet_tag }}

<section class="ingredient-cards" data-section-id="{{ section.id }}">
  <style>
    #shopify-section-{{ section.id }} {
      --ic-gap: 24px;
      --ic-radius: 16px;
      --ic-card-bg: rgb(var(--background-primary));
      --ic-card-text: rgb(var(--text-primary));
      --ic-btn-bg: #c3583e;
      --ic-btn-color: #fff;
      --ic-shadow: {% if section.settings.enable_shadows %}0 8px 22px rgba(0,0,0,.06){% else %}none{% endif %};
      --ic-left-h: {{ section.settings.left_image_height | default: 360 }}px;
      --ic-left-w: {{ section.settings.left_image_width | default: 100 }}%;
      --ic-left-fit: {{ section.settings.left_image_fit | default: 'contain' }};
      background-color: {{ section.settings.section_bg | default: 'transparent' }};
      --ic-eyebrow-weight: {{ section.settings.eyebrow_weight | default: '800' }};
      --ic-eyebrow-style: {{ section.settings.eyebrow_style | default: 'normal' }};
      --ic-heading-weight: {{ section.settings.heading_weight | default: '800' }};
      --ic-heading-style: {{ section.settings.heading_style | default: 'normal' }};
      --ic-subheading-weight: {{ section.settings.subheading_weight | default: '800' }};
      --ic-subheading-style: {{ section.settings.subheading_style | default: 'normal' }};
      {% if section.settings.eyebrow_color != blank %}--ic-eyebrow-color: {{ section.settings.eyebrow_color }};{% endif %}
      {% if section.settings.heading_color != blank %}--ic-heading-color: {{ section.settings.heading_color }};{% endif %}
      {% if section.settings.subheading_color != blank %}--ic-subheading-color: {{ section.settings.subheading_color }};{% endif %}
      {% if section.settings.desc_color != blank %}--ic-desc-color: {{ section.settings.desc_color }};{% endif %}
      {% if section.settings.card_bg != blank %}--ic-card-bg: {{ section.settings.card_bg }};{% endif %}
      {% if section.settings.card_text != blank %}--ic-card-text: {{ section.settings.card_text }};{% endif %}
      {% if section.settings.button_bg != blank %}--ic-btn-bg: {{ section.settings.button_bg }};{% endif %}
      {% if section.settings.button_text != blank %}--ic-btn-color: {{ section.settings.button_text }};{% endif %}
    }

    #shopify-section-{{ section.id }} .ic-header{
      text-align: center;
      margin: 0 auto var(--spacing-10, 40px);
      max-width: min(920px, 92vw);
    }
    #shopify-section-{{ section.id }} .ic-eyebrow{
      font-weight: var(--ic-eyebrow-weight, 800);
      font-style: var(--ic-eyebrow-style, normal);
      letter-spacing: .08em;
      text-transform: uppercase;
      color: var(--ic-eyebrow-color, rgb(var(--text-primary) / .7));
      margin: 0 0 6px;
    }

    #shopify-section-{{ section.id }} .ic-heading{
      font-weight: var(--ic-heading-weight, 800);
      font-style: var(--ic-heading-style, normal);
      color: var(--ic-heading-color, rgb(var(--text-primary)));
    }

    #shopify-section-{{ section.id }} .ic-subheading{
      font-weight: var(--ic-subheading-weight, 800);
      font-style: var(--ic-subheading-style, normal);
      color: var(--ic-subheading-color, rgb(var(--text-primary)));
    }

    #shopify-section-{{ section.id }} .ic-desc{
      color: var(--ic-desc-color, rgb(var(--text-primary) / .8));
      max-width: 720px;
      margin: 0 auto;
    }

    #shopify-section-{{ section.id }} .ic-layout{
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--ic-gap);
      align-items: start;
    }

    #shopify-section-{{ section.id }} .ic-left-media{
      height: var(--ic-left-h);
      width: var(--ic-left-w);
      overflow: hidden;
      border-radius: var(--ic-radius);
      background: #fff;
    }

    #shopify-section-{{ section.id }} .ic-left-img{
      width: 100%;
      height: 100%;
      object-fit: var(--ic-left-fit);
      display: block;
    }

    @media(max-width: 999px){
      #shopify-section-{{ section.id }} .ic-layout.has-image{
        grid-template-columns: 1fr;
        gap: 20px;
      }

      #shopify-section-{{ section.id }} .ic-layout.has-image .ic-left-media{
        height: 100%;
      }

      #shopify-section-{{ section.id }} .ic-layout.has-image .ic-list{
        display: grid !important;
        grid-template-columns: repeat(2, 1fr);
        gap: 8px;
        overflow: visible !important;
        transform: none !important;
        padding: 0 8px;
      }

      #shopify-section-{{ section.id }} .ic-layout.has-image .ic-card{
        min-width: 160px;
      }
    }

    @media(min-width: 1000px){
      #shopify-section-{{ section.id }} .ic-layout.has-image{
        grid-template-columns: 50% 50%;
      }

      #shopify-section-{{ section.id }} .ic-layout.has-image .ic-list{
        display: grid !important;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px;
        overflow: visible !important;
        transform: none !important;
        height: 100%;
        align-content: start;
      }

      #shopify-section-{{ section.id }} .ic-layout.has-image .flickity-viewport{
        overflow: visible !important;
        height: auto !important;
      }

      #shopify-section-{{ section.id }} .ic-layout.has-image .ic-item{
        display: block !important;
        margin: 0 !important;
        position: static !important;
        transform: none !important;
      }

      #shopify-section-{{ section.id }} .ic-layout.has-image .ic-card{
        width: 100% !important;
        height: auto !important;
      }
    }

    #shopify-section-{{ section.id }} .ic-list{
      display: flex;
      gap: 14px;
      padding: 4px 16px 12px;
      margin: 0 auto;
      overflow: hidden;
      max-width: 100vw;
      box-sizing: border-box;
    }

    #shopify-section-{{ section.id }} .ic-item{
      flex: 0 0 85%;
      width: 85%;
      box-sizing: border-box;
    }

    #shopify-section-{{ section.id }} .ic-list.flickity-enabled{
      display: block !important;
      position: relative;
      overflow: hidden;
      width: 100% !important;
      padding-left: 0 !important;
      padding-right: 0 !important;
      padding-bottom: 60px;
    }

    @media (max-width: 999px){
      #shopify-section-{{ section.id }} .ic-list.flickity-enabled{
        display: block !important;
        grid-template-columns: none !important;
        flex-direction: row !important;
      }

      #shopify-section-{{ section.id }} .ic-list.flickity-enabled .ic-item{
        grid-column: unset !important;
        grid-row: unset !important;
      }

      #shopify-section-{{ section.id }} .section-stack{
        padding-left: 0 !important;
        padding-right: 0 !important;
      }

      #shopify-section-{{ section.id }} .ic-header{
        padding-left: 16px;
        padding-right: 16px;
      }

      #shopify-section-{{ section.id }} .ic-list{
        padding-left: 0 !important;
        padding-right: 0 !important;
      }

      #shopify-section-{{ section.id }} .ic-list.flickity-enabled .flickity-viewport{
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
      }

      #shopify-section-{{ section.id }} .ic-layout.has-image .ic-item {
        width: 100%;
      }
    }

    #shopify-section-{{ section.id }} .flickity-enabled .ic-item,
    #shopify-section-{{ section.id }} .ic-list.flickity-enabled .ic-item,
    #shopify-section-{{ section.id }} .flickity-enabled .flickity-cell{
      width: 85% !important;
      margin-right: 16px !important;
      box-sizing: border-box !important;
    }

    #shopify-section-{{ section.id }} .ic-list.flickity-enabled .flickity-slider{
      position: absolute;
      left: 0;
      top: 0;
      will-change: transform;
    }

    #shopify-section-{{ section.id }} .flickity-viewport{
      height: auto !important;
    }

    #shopify-section-{{ section.id }} .flickity-slider{
      height: auto !important;
    }

    @media (min-width: 480px){
      #shopify-section-{{ section.id }} .flickity-enabled .ic-item,
      #shopify-section-{{ section.id }} .ic-list.flickity-enabled .ic-item,
      #shopify-section-{{ section.id }} .flickity-enabled .flickity-cell{
        width: 75% !important;
        margin-right: 20px !important;
        box-sizing: border-box !important;
      }
    }

    @media (min-width: 768px){
      #shopify-section-{{ section.id }} .flickity-enabled .ic-item,
      #shopify-section-{{ section.id }} .ic-list.flickity-enabled .ic-item,
      #shopify-section-{{ section.id }} .flickity-enabled .flickity-cell{
        width: 70% !important;
        margin-right: 24px !important;
        box-sizing: border-box !important;
      }
    }

    #shopify-section-{{ section.id }} .ic-list.flickity-enabled .flickity-viewport{
      position: relative;
      overflow: hidden;
      height: auto !important;
      min-height: 400px;
      max-width: 100vw;
      box-sizing: border-box;
      touch-action: pan-y !important;
      cursor: grab !important;
    }

    #shopify-section-{{ section.id }} .ic-list.flickity-enabled .flickity-viewport.is-pointer-down{
      cursor: grabbing !important;
    }

    #shopify-section-{{ section.id }} .ic-list.flickity-enabled .ic-item{
      display: block !important;
    }

    #shopify-section-{{ section.id }} .ic-list.flickity-enabled .ic-card{
      height: auto !important;
    }

    #shopify-section-{{ section.id }} .flickity-page-dots{
      position: relative;
      bottom: 0;
      margin-top: 12px;
      text-align: center;
      line-height: 1;
    }

    #shopify-section-{{ section.id }} .flickity-page-dots .dot{
      display: inline-block;
      width: 10px;
      height: 10px;
      margin: 0 8px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      opacity: 0.25;
      cursor: pointer;
      transition: opacity 0.2s ease, background-color 0.2s ease;
    }

    #shopify-section-{{ section.id }} .flickity-page-dots .dot.is-selected{
      opacity: 1;
      background: #000;
    }

    #shopify-section-{{ section.id }} .flickity-page-dots .dot:hover{
      opacity: 0.7;
    }

    #shopify-section-{{ section.id }} .flickity-prev-next-button{
      width: 36px;
      height: 36px;
      border-radius: 9999px;
      background: rgba(0,0,0,0.55);
      top: 50%;
      transform: translateY(-50%);
      z-index: 2;
    }

    #shopify-section-{{ section.id }} .flickity-prev-next-button.previous{
      left: 8px;
    }

    #shopify-section-{{ section.id }} .flickity-prev-next-button.next{
      right: 8px;
    }

    #shopify-section-{{ section.id }} .flickity-prev-next-button .flickity-button-icon{
      fill: #fff;
    }

    {% unless section.settings.show_dots %}
    #shopify-section-{{ section.id }} .flickity-page-dots{
      display: none !important;
    }
    {% endunless %}

    {% unless section.settings.show_arrows %}
    #shopify-section-{{ section.id }} .flickity-prev-next-button{
      display: none !important;
    }
    {% endunless %}

    @media (min-width: 768px) and (max-width: 999px){
      #shopify-section-{{ section.id }} .flickity-enabled .ic-item{
        width: 65% !important;
        margin-right: 20px !important;
        max-width: 400px;
      }
    }

    #shopify-section-{{ section.id }}{
      overflow-x: hidden;
      max-width: 100vw;
      box-sizing: border-box;
    }

    #shopify-section-{{ section.id }} .ic-card{
      border: 1px solid rgba(0,0,0,.06);
      border-radius: var(--ic-radius);
      box-shadow: var(--ic-shadow);
      overflow: hidden;
      position: relative;
      height: 100%;
      min-height: 180px;
      min-width: 120px;
      display: flex;
      flex-direction: column;
    }

    #shopify-section-{{ section.id }} .ic-layout.has-image .ic-card{
      min-height: 200px;
      width: 100%;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      padding: 5px 2px;
    }

    #shopify-section-{{ section.id }} .ic-media{
      position: absolute;
      inset: 0;
      background-position: 75% 65%;
      background-repeat: no-repeat;
      z-index: 1;
      transition: transform .45s cubic-bezier(.22,.61,.36,1);
    }

    #shopify-section-{{ section.id }} .ic-layout.has-image .ic-media{
      background-position: center;
    }

    #shopify-section-{{ section.id }} .ic-card:hover .ic-media,
    #shopify-section-{{ section.id }} .ic-card:focus-within .ic-media{
      transform: scale(1.06);
    }

    #shopify-section-{{ section.id }} .ic-badge{
      position: absolute;
      top: 12px;
      left: 12px;
      padding: 6px 12px;
      border-radius: 999px;
      font-weight: 700;
      font-size: 14px;
      background: var(--ic-badge-bg);
      color: var(--ic-badge-color);
      z-index: 3;
    }

    #shopify-section-{{ section.id }} .ic-layout.has-image .ic-badge{
      top: 6px;
      left: 6px;
      padding: 3px 8px;
      font-size: 10px;
    }

    #shopify-section-{{ section.id }} .ic-card{
      display: grid;
      grid-template-rows: auto 1fr auto;
      min-height: 300px;
    }

    #shopify-section-{{ section.id }} .ic-badge-area{
      position: relative;
      z-index: 3;
      padding: 12px;
      display: flex;
      justify-content: flex-start;
    }

    #shopify-section-{{ section.id }} .ic-content-area{
      position: relative;
      z-index: 2;
      padding: 15px 10px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      text-align: left;
    }

    #shopify-section-{{ section.id }} .flickity-enabled .ic-content-area{
      padding: 12px 8px;
    }

    body[class*="template-product"] #shopify-section-{{ section.id }} .flickity-enabled .ic-content-area,
    .template-product #shopify-section-{{ section.id }} .flickity-enabled .ic-content-area,
    #shopify-section-{{ section.id }}.is-product-page .flickity-enabled .ic-content-area{
      padding: 18px 12px;
    }

    #shopify-section-{{ section.id }} .ic-actions-area{
      position: relative;
      z-index: 2;
      padding: 10px;
      display: flex;
      justify-content: flex-start;
      align-items: flex-end;
    }

    #shopify-section-{{ section.id }} .ic-title{
      font-weight: 900;
      font-size: 22px;
      text-transform: uppercase;
      margin: 0;
      color: var(--ic-card-text);
      line-height: 1.15;
      letter-spacing: .01em;
    }

    #shopify-section-{{ section.id }} .ic-layout.has-image .ic-title{
      font-size: 14px;
      line-height: 1.2;
      margin-bottom: 3px;
    }
    #shopify-section-{{ section.id }} .ic-text{
      color: var(--ic-card-text, rgb(var(--text-primary) / .85));
      font-size: 13px;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    #shopify-section-{{ section.id }} .ic-layout.has-image .ic-text{
      font-size: 11px;
      line-height: 1.3;
      -webkit-line-clamp: 2;
    }

    #shopify-section-{{ section.id }} .ic-button{
      background: transparent;
      color: rgb(var(--text-primary));
      border: none;
      padding: 0;
      border-radius: 0;
      font-weight: 800;
      font-size: 14px;
      letter-spacing: .04em;
      text-transform: uppercase;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: flex-start;
      text-decoration: none;
    }

    #shopify-section-{{ section.id }} .ic-layout.has-image .ic-button{
      font-size: 10px;
      letter-spacing: .02em;
      min-height: 24px;
    }

    #shopify-section-{{ section.id }} .ic-button::after{
      content: ' +';
    }

    #shopify-section-{{ section.id }} .ic-layout.has-image .ic-card:hover{
      transform: translateY(-2px);
      {% if section.settings.enable_shadows %}box-shadow: 0 12px 28px rgba(0,0,0,.12);{% endif %}
    }

    #shopify-section-{{ section.id }} .ic-layout.has-image .ic-card:hover .ic-media{
      transform: scale(1.02);
    }

    @media(min-width: 1000px){
      #shopify-section-{{ section.id }} .ic-title{
        font-size: 24px;
      }
    }

    #shopify-section-{{ section.id }} .ic-modal-backdrop{
      position: fixed;
      inset: 0;
      background: rgba(0,0,0,.5);
      display: none;
      align-items: center;
      justify-content: center;
      padding: 24px;
      z-index: 9999;
    }

    #shopify-section-{{ section.id }} .ic-modal-backdrop[aria-hidden="false"]{
      display: flex;
    }

    #shopify-section-{{ section.id }} .ic-modal{
      background: #fff;
      color: #1a1a1a;
      border-radius: 16px;
      max-width: min(900px, 92vw);
      max-height: 90vh;
      overflow: auto;
      position: relative;
      padding: 22px;
    }

    #shopify-section-{{ section.id }} .ic-modal .rte{
      line-height: 1.6;
    }

    #shopify-section-{{ section.id }} .ic-modal-close{
      position: absolute;
      top: 8px;
      right: 8px;
      background: #fff;
      border: 1px solid #e5e5e5;
      border-radius: 999px;
      width: 36px;
      height: 36px;
      display: grid;
      place-items: center;
      cursor: pointer;
    }

    #shopify-section-{{ section.id }} .ic-modal-close svg{
      pointer-events: none;
    }

    @media(min-width: 1000px){
      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled){
        display: grid;
        grid-template-columns: repeat(3, minmax(300px, 1fr));
        gap: var(--ic-gap);
        overflow: visible;
        padding: 0;
        justify-items: center;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled) .ic-item{
        flex: unset;
        width: 100%;
        max-width: none;
      }

      #shopify-section-{{ section.id }} .ic-card{
        height: 100%;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="1"]{
        grid-template-columns: 1fr;
        justify-content: center;
        max-width: 400px;
        margin: 0 auto;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="1"] .ic-item:first-child{
        grid-column: 1;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="2"]{
        grid-template-columns: repeat(2, minmax(300px, 1fr));
        justify-content: center;
        max-width: 800px;
        margin: 0 auto;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="2"] .ic-item:nth-child(1){
        grid-column: 1;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="2"] .ic-item:nth-child(2){
        grid-column: 2;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="4"]{
        grid-template-columns: repeat(3, minmax(300px, 1fr));
        justify-items: center;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="4"] .ic-item:nth-child(4){
        grid-column: 2;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="5"]{
        grid-template-columns: repeat(3, minmax(300px, 1fr));
        justify-items: center;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="5"] .ic-item:nth-child(4){
        grid-column: 1;
      }

      #shopify-section-{{ section.id }} .ic-list:not(.flickity-enabled)[data-count="5"] .ic-item:nth-child(5){
        grid-column: 3;
      }
    }

    #shopify-section-{{ section.id }} .ic-list.pdp-grid-layout{
      display: grid !important;
      grid-template-columns: repeat(2, 1fr) !important;
      gap: var(--ic-gap) !important;
      overflow: visible !important;
      transform: none !important;
      padding: 0 !important;
      width: 100% !important;
    }

    @media(min-width: 1000px){
      #shopify-section-{{ section.id }} .ic-list.pdp-grid-layout{
        grid-template-columns: repeat(3, 1fr) !important;
      }
    }

    #shopify-section-{{ section.id }} .ic-list.pdp-grid-layout .ic-item{
      width: 100% !important;
      margin-right: 0 !important;
      flex: unset !important;
    }

    #shopify-section-{{ section.id }} .ic-list.pdp-grid-layout .flickity-viewport{
      overflow: visible !important;
      height: auto !important;
    }
  </style>

  <div>
    {% assign left_image = blank %}
    {% assign left_alt = '' %}
    {% for block in section.blocks %}
      {% if block.type == 'left_image' and block.settings.image != blank %}
        {% assign left_image = block.settings.image %}
        {% assign left_alt = block.settings.alt | default: section.settings.heading %}
        {% break %}
      {% endif %}
    {% endfor %}

    <div class="ic-header">
      {%- if section.settings.eyebrow != blank -%}
        <div class="ic-eyebrow">{{ section.settings.eyebrow | escape }}</div>
      {%- endif -%}
      {%- if section.settings.heading != blank -%}
        <h2 class="ic-heading custom-heading-primary custom-ingredients">{{ section.settings.heading | escape }}</h2>
      {%- endif -%}
      {%- if section.settings.subheading != blank -%}
        <h3 class="ic-subheading custom-heading-tertiary">{{ section.settings.subheading | escape }}</h3>
      {%- endif -%}
      {%- if section.settings.desc != blank -%}
        <div class="ic-desc rte custom-desc-text">{{ section.settings.desc }}</div>
      {%- endif -%}
    </div>

    <div class="ic-layout {% if left_image != blank %}has-image{% endif %}">
      {% if left_image != blank %}
        <div class="ic-left">
          <div class="ic-left-media">
            {{ left_image | image_url: width: 1600 | image_tag: class: 'ic-left-img', alt: left_alt, loading: 'lazy' }}
          </div>
        </div>
      {% endif %}
      <div class="ic-right">

    {% assign card_blocks = section.blocks | where: 'type', 'card' %}
    <div class="ic-list" data-count="{{ card_blocks.size }}">
      {%- for block in card_blocks -%}
        {%- assign badge_bg = block.settings.badge_bg | default: section.settings.badge_bg_default -%}
        {%- assign badge_color = block.settings.badge_color | default: section.settings.badge_text_default -%}
        <div class="ic-item" {{ block.shopify_attributes }}>
          <article class="ic-card" style="--ic-badge-bg: {{ badge_bg }}; --ic-badge-color: {{ badge_color }};">
            <!-- Background image covers entire card -->
            <div class="ic-media" style="{% if block.settings.image != blank %}background-image:url('{{ block.settings.image | image_url: width: 800 }}'); background-size: {{ block.settings.image_fit | default: 'cover' }};{% endif %}"></div>

            <!-- Badge positioned in top area -->
            <div class="ic-badge-area">
              {%- if block.settings.badge != blank -%}
                <span class="ic-badge">{{ block.settings.badge | escape }}</span>
              {%- endif -%}
            </div>

            <!-- Content area for title and description -->
            <div class="ic-content-area">
              {%- if block.settings.title != blank -%}
                <h3 class="ic-title">{{ block.settings.title | escape }}</h3>
              {%- endif -%}
              {%- if block.settings.text != blank -%}
                <div class="ic-text rte custom-card-text">{{ block.settings.text }}</div>
              {%- endif -%}
            </div>

            <!-- Actions area for button -->
            <div class="ic-actions-area">
              <button type="button" class="ic-button" data-ic-open data-modal-id="modal-{{ section.id }}-{{ forloop.index }}">{{ block.settings.button_label | default: 'Read more' }}</button>
            </div>
          </article>
        </div>
      {%- endfor -%}
    </div>
    </div>
    </div>

    <!-- Modals outside card structure to prevent flickering -->
    {% assign card_blocks = section.blocks | where: 'type', 'card' %}
    {%- for block in card_blocks -%}
      <div class="ic-modal-backdrop" aria-hidden="true" data-ic-backdrop id="modal-{{ section.id }}-{{ forloop.index }}">
        <div class="ic-modal" role="dialog" aria-modal="true" aria-label="{{ block.settings.title | escape }} details">
          <button class="ic-modal-close" aria-label="Close" type="button" data-ic-close>
            <svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true"><path d="M18.3 5.71 12 12l6.3 6.29-1.41 1.41L10.59 13.41 4.29 19.71 2.88 18.3 9.17 12 2.88 5.71 4.29 4.3 10.59 10.59 16.89 4.3z"/></svg>
          </button>
          <div class="rte">{{ block.settings.modal_rte }}</div>
        </div>
      </div>
    {%- endfor -%}
  </div>

  <script src="{{ 'flickity-utils.js' | asset_url }}" defer></script>

  <script>
    function initIngredientsSlider{{ section.id | replace: '-', '' }}(){
      if(typeof FlickityUtils === 'undefined'){
        setTimeout(initIngredientsSlider{{ section.id | replace: '-', '' }}, 100);
        return;
      }

      const root = document.querySelector('#shopify-section-{{ section.id }}');
      if(!root) return;

      // Add product page class for styling
      const isProductPage = document.body.classList.contains('template-product') ||
                           document.body.className.includes('template-product') ||
                           window.location.pathname.includes('/products/');
      if(isProductPage) {
        root.classList.add('is-product-page');
      }

      // Modal interactions
      root.querySelectorAll('[data-ic-open]').forEach((openBtn)=>{
        const modalId = openBtn.getAttribute('data-modal-id');
        const backdrop = document.getElementById(modalId);
        const closeBtn = backdrop?.querySelector('[data-ic-close]');
        if(!backdrop || !closeBtn) return;

        const open = ()=>{
          backdrop.setAttribute('aria-hidden','false');
          closeBtn.focus({preventScroll:true});
          document.body.style.overflow = 'hidden';
        };
        const close = ()=>{
          backdrop.setAttribute('aria-hidden','true');
          openBtn.focus({preventScroll:true});
          document.body.style.overflow = '';
        };

        openBtn.addEventListener('click', open);
        closeBtn.addEventListener('click', close);
        backdrop.addEventListener('click', (e)=>{ if(e.target === backdrop) close(); });
        document.addEventListener('keydown', (e)=>{ if(e.key === 'Escape' && backdrop.getAttribute('aria-hidden') === 'false') close(); });
      });

      // Check if layout has left image - if so, skip Flickity and use CSS grid
      const layout = root.querySelector('.ic-layout');
      if(layout && layout.classList.contains('has-image')){
        return;
      }

      // Initialize slider using shared FlickityUtils
      const list = root.querySelector('.ic-list');
      if(!list) return;

      if(!list.id) {
        list.id = 'ingredients-{{ section.id }}-track';
      }

      FlickityUtils.initIngredients(list.id, {
        customConfig: {
          pageDots: {{ section.settings.show_dots | default: true | json }},
          prevNextButtons: {{ section.settings.show_arrows | default: true | json }}
        }
      });
    }

    if(document.readyState === 'loading'){
      document.addEventListener('DOMContentLoaded', initIngredientsSlider{{ section.id | replace: '-', '' }}, {once:true});
    } else {
      initIngredientsSlider{{ section.id | replace: '-', '' }}();
    }
  </script>

  {%- schema -%}
  {
    "name": "Custom Ingredients Card",
    "class": "section",
    "settings": [
      {
        "type": "header",
        "content": "Content"
      },
      {
        "type": "text",
        "id": "eyebrow",
        "label": "Eyebrow"
      },
      {
        "type": "text",
        "id": "heading",
        "label": "Heading",
        "default": "Our 100% Organic"
      },
      {
        "type": "text",
        "id": "subheading",
        "label": "Subheading",
        "default": "6\u2013Mushroom Blend"
      },
      {
        "type": "richtext",
        "id": "desc",
        "label": "Description",
        "default": "<p>Describe your blend or ingredient benefits here.<\/p>"
      },
      {
        "type": "header",
        "content": "Typography"
      },
      {
        "type": "select",
        "id": "eyebrow_weight",
        "label": "Eyebrow weight",
        "options": [
          {"value": "400", "label": "Normal (400)"},
          {"value": "600", "label": "Semibold (600)"},
          {"value": "700", "label": "Bold (700)"},
          {"value": "800", "label": "Extra bold (800)"}
        ],
        "default": "800"
      },
      {
        "type": "select",
        "id": "eyebrow_style",
        "label": "Eyebrow style",
        "options": [
          {"value": "normal", "label": "Normal"},
          {"value": "italic", "label": "Italic"}
        ],
        "default": "normal"
      },
      {
        "type": "select",
        "id": "heading_weight",
        "label": "Heading weight",
        "options": [
          {"value": "400", "label": "Normal (400)"},
          {"value": "600", "label": "Semibold (600)"},
          {"value": "700", "label": "Bold (700)"},
          {"value": "800", "label": "Extra bold (800)"}
        ],
        "default": "800"
      },
      {
        "type": "select",
        "id": "heading_style",
        "label": "Heading style",
        "options": [
          {"value": "normal", "label": "Normal"},
          {"value": "italic", "label": "Italic"}
        ],
        "default": "normal"
      },
      {
        "type": "select",
        "id": "subheading_weight",
        "label": "Subheading weight",
        "options": [
          {"value": "400", "label": "Normal (400)"},
          {"value": "600", "label": "Semibold (600)"},
          {"value": "700", "label": "Bold (700)"},
          {"value": "800", "label": "Extra bold (800)"}
        ],
        "default": "800"
      },
      {
        "type": "select",
        "id": "subheading_style",
        "label": "Subheading style",
        "options": [
          {"value": "normal", "label": "Normal"},
          {"value": "italic", "label": "Italic"}
        ],
        "default": "normal"
      },
      {
        "type": "header",
        "content": "Colors"
      },
      {
        "type": "color",
        "id": "section_bg",
        "label": "Section background",
        "default": "transparent"
      },
      {
        "type": "color",
        "id": "eyebrow_color",
        "label": "Eyebrow color"
      },
      {
        "type": "color",
        "id": "heading_color",
        "label": "Heading color"
      },
      {
        "type": "color",
        "id": "subheading_color",
        "label": "Subheading color"
      },
      {
        "type": "color",
        "id": "desc_color",
        "label": "Description color"
      },
      {
        "type": "color",
        "id": "card_bg",
        "label": "Card background color"
      },
      {
        "type": "color",
        "id": "card_text",
        "label": "Card text color"
      },
      {
        "type": "color",
        "id": "button_bg",
        "label": "Button background color",
        "default": "#c3583e"
      },
      {
        "type": "color",
        "id": "button_text",
        "label": "Button text color",
        "default": "#ffffff"
      },
      {
        "type": "color",
        "id": "badge_bg_default",
        "label": "Default badge background",
        "default": "#1f7a4a"
      },
      {
        "type": "color",
        "id": "badge_text_default",
        "label": "Default badge text",
        "default": "#ffffff"
      },
      {
        "type": "checkbox",
        "id": "enable_shadows",
        "label": "Enable card shadows",
        "default": true
      },
      {
        "type": "header",
        "content": "Slider controls"
      },
      {
        "type": "checkbox",
        "id": "show_dots",
        "label": "Show dots",
        "default": true
      },
      {
        "type": "header",
        "content": "Layout"
      },
      {
        "type": "range",
        "id": "left_image_height",
        "label": "Left image height (px)",
        "min": 200,
        "max": 800,
        "step": 10,
        "default": 360
      },
      {
        "type": "range",
        "id": "left_image_width",
        "label": "Left image width (%)",
        "min": 50,
        "max": 100,
        "step": 5,
        "default": 100
      },
      {
        "type": "select",
        "id": "left_image_fit",
        "label": "Left image fit",
        "options": [
          {"value": "contain", "label": "Contain (fit entire image)"},
          {"value": "cover", "label": "Cover (fill container)"},
          {"value": "fill", "label": "Fill (stretch to fit)"}
        ],
        "default": "contain"
      },
      {
        "type": "checkbox",
        "id": "show_arrows",
        "label": "Show arrows",
        "default": true
      }
    ],
    "blocks": [
      {
        "type": "left_image",
        "name": "Left image (optional)",
        "settings": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Image"
          },
          {
            "type": "text",
            "id": "alt",
            "label": "Alt text"
          }
        ]
      },
      {
        "type": "card",
        "name": "Ingredient card",
        "settings": [
          {
            "type": "image_picker",
            "id": "image",
            "label": "Background image"
          },
          {
            "type": "select",
            "id": "image_fit",
            "label": "Background image fit",
            "options": [
              {"value": "contain", "label": "Contain (fit entire image)"},
              {"value": "cover", "label": "Cover (fill container)"}
            ],
            "default": "cover"
          },
          {
            "type": "text",
            "id": "badge",
            "label": "Badge text",
            "default": "Growth"
          },
          {
            "type": "color",
            "id": "badge_bg",
            "label": "Badge background",
            "default": "#1f7a4a"
          },
          {
            "type": "color",
            "id": "badge_color",
            "label": "Badge text color",
            "default": "#ffffff"
          },
          {
            "type": "text",
            "id": "title",
            "label": "Title",
            "default": "Ingredient name"
          },
          {
            "type": "richtext",
            "id": "text",
            "label": "Short description",
            "default": "<p>Short description of the ingredient and its benefits.<\/p>"
          },
          {
            "type": "text",
            "id": "button_label",
            "label": "Button label",
            "default": "Read more"
          },
          {
            "type": "richtext",
            "id": "modal_rte",
            "label": "Modal content (RTE)",
            "default": "<p>Full description, sourcing info, and additional benefits.<\/p>"
          }
        ]
      }
    ],
    "max_blocks": 12,
    "presets": [
      {
        "name": "Custom Ingredients Card",
        "category": "Custom",
        "blocks": [
          {"type": "card"},
          {"type": "card"},
          {"type": "card"}
        ]
      }
    ]
  }
  {%- endschema -%}
</section>

