(function () {
    // Function to reload the page if not freshly loaded
    function ensureFreshPage() {
        // Check if the page was reloaded recently
        if (!sessionStorage.getItem('pageReloaded')) {
            sessionStorage.setItem('pageReloaded', 'true');
            window.location.reload(); // Force reload from server
        } else {
            // Remove the flag after the first reload
            sessionStorage.removeItem('pageReloaded');
        }
    }

    // Listen for the `pageshow` event, which triggers when the page is shown
    window.addEventListener('pageshow', function (event) {
        if (event.persisted) {
            // If the page is served from the cache, ensure it's reloaded
            ensureFreshPage();
        }
    });

    // Force reload on initial load
    ensureFreshPage();
})();

// Flag to prevent reinitialization
let isInitialized = false;

// Main initialization function
function initializeScripts() {
    if (isInitialized) {
        //console.log("Scripts are already initialized. Skipping112...");
        return;
    }
    isInitialized = true; // Mark as initialized

    //console.log("Initializing scripts...");

    // Example: Main product ID(s) and bundled product ID(s)
    if (window.themeSettings) {
        const mainProductIds = window.themeSettings.mainProductIds
            ? window.themeSettings.mainProductIds.split(',').map(id => parseInt(id.trim(), 10))
            : [];
        const bundledProductIds = window.themeSettings.bundledProductIds
            ? window.themeSettings.bundledProductIds.split(',').map(id => parseInt(id.trim(), 10))
            : [];

        // Function to check cart items
        function checkCartForBundledProducts() {
            return fetch('/cart.js')
                .then(response => response.json())
                .then(cart => {
                    const cartItemIds = cart.items.map(item => item.id);
                    const hasMainProduct = mainProductIds.some(id => cartItemIds.includes(id));
                    const hasBundledProduct = bundledProductIds.some(id => cartItemIds.includes(id));
                    return { hasMainProduct, hasBundledProduct };
                })
                .catch(error => {
                    console.error('Error fetching cart:', error);
                });
        }

        // Function to create the overlay inside cart-drawer
        function showOverlay(message) {
            const cartDrawer = document.querySelector('#cart-drawer');
            if (!cartDrawer) {
                console.error('Cart drawer not found.');
                return;
            }

            const overlay = document.createElement('div');
            overlay.style.position = 'absolute';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            overlay.style.display = 'flex';
            overlay.style.justifyContent = 'center';
            overlay.style.alignItems = 'center';
            overlay.style.zIndex = '10';

            const modal = document.createElement('div');
            modal.style.backgroundColor = '#fff';
            modal.style.padding = '20px';
            modal.style.borderRadius = '8px';
            modal.style.margin = '20px';
            modal.style.fontSize = '20px';
            modal.style.textAlign = 'center';
            modal.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)';
            modal.textContent = message;

            const closeButton = document.createElement('button');
            closeButton.textContent = 'X Close';
            closeButton.style.display = 'flex';
            closeButton.style.margin = 'auto';
            closeButton.style.fontWeight = '600';
            closeButton.style.color = '#eb6642';
            closeButton.style.marginTop = '10px';
            closeButton.addEventListener('click', () => {
                cartDrawer.removeChild(overlay);
            });

            modal.appendChild(closeButton);
            overlay.appendChild(modal);
            cartDrawer.appendChild(overlay);
        }

        // Intercept form submission
        const checkoutForm = document.querySelector('form.buy-buttons--compact[method="POST"]');
        const checkoutButton = checkoutForm?.querySelector('button[name="checkout"]');
     //console.log(checkoutForm);
      
        if (checkoutForm && checkoutButton) {
            checkoutForm.addEventListener('submit', function (event) {
                event.preventDefault(); // Prevent the form from submitting immediately
                checkoutButton.disabled = true;

                checkCartForBundledProducts().then(({ hasMainProduct, hasBundledProduct }) => {
                    if (hasBundledProduct && !hasMainProduct) {
                        showOverlay('Accessories cannot be sold separately; they must be bundled with items such as mats, bedsheets, and pillowcases.');
                        checkoutButton.disabled = false;
                    } else {
                        window.location.href = '/checkout';
                    }
                });
            });
        }
    }

    // Add your other scripts below, like remove-link handling, add-to-cart, etc.
    //console.log('Scripts for Quick Buy, remove-link, and other features initialized...');
}


// Reset initialization flag to allow reinitialization when needed
function resetInitializationFlag() {
    isInitialized = false;
}

// Initialize scripts and reinitialize on back/forward navigation (pageshow)
document.addEventListener('DOMContentLoaded', initializeScripts);
window.addEventListener('pageshow', function (event) {
    if (event.persisted) {
        //console.log("Page was restored from the cache.");
        resetInitializationFlag(); // Reset to reinitialize on cache restoration
    }
    initializeScripts();
});

// Optional: Reset flag on popstate (user manually navigates using the browser buttons)
window.addEventListener('popstate', function () {
    resetInitializationFlag(); // Reset to reinitialize on forward/back navigation
    initializeScripts();
});

// Cart drawer and Quick Buy button listeners
function initializeRemoveLinks() {
  const removeLinks = document.querySelectorAll('.remove-link');

  //console.log('Number of remove buttons detected:', removeLinks.length);

  if (removeLinks.length > 0) {
    removeLinks.forEach(function (removeLink, index) {
      //console.log('Attaching event to remove button ' + index);
      removeLink.removeEventListener('click', handleRemoveLinkClick); // Prevent duplicates
      removeLink.addEventListener('click', handleRemoveLinkClick); // Reattach event
    });
  }
}

function handleRemoveLinkClick(event) {
  event.preventDefault();
  //console.log('Remove button clicked!');
  setTimeout(() => {
    location.reload();
  }, 1500); // Reload the page after a delay
}


function initializeAddToCartForms() {
  const addToCartForms = document.querySelectorAll('form[action*="/cart/add"]');
  
  addToCartForms.forEach(function (form) {
    form.addEventListener('submit', function () {
      //console.log('submitted successfully!!');
      setTimeout(() => {
        location.reload();
      }, 1500);
    });
  });
}

function attachQuickBuyListeners(container = document) {
  const quickBuyButtons = container.querySelectorAll('.product-card__mobile-quick-buy-button, .button[aria-controls^="quick-buy-horizontal-sections"]');

  //console.log(`Detected ${quickBuyButtons.length} Quick Buy button(s) in the container.`);

  quickBuyButtons.forEach(function (button) {
    button.addEventListener('click', function () {
      //console.log('Quick Buy button clicked!');
      const formId = button.getAttribute('aria-controls');

      const interval = setInterval(() => {
        const quickBuyForm = document.querySelector(`#${formId} form.shopify-product-form`);

        if (quickBuyForm) {
          //console.log(`Form found for Quick Buy button: ${formId}`);
          clearInterval(interval);

          quickBuyForm.addEventListener('submit', function (event) {
            event.preventDefault();
            const formData = new FormData(quickBuyForm);

            fetch('/cart/add.js', {
              method: 'POST',
              body: formData,
            })
              .then((response) => response.json())
              .then(() => {
                setTimeout(() => {
                  location.reload();
                }, 1500);
              })
              .catch((error) => {
                console.error('Error adding product to cart:', error);
              });
          });
        }
      }, 100);
    });
  });
}

function observeCartDrawer() {
  const cartDrawer = document.getElementById('cart-drawer');

  if (cartDrawer) {
    const observer = new MutationObserver(function (mutationsList) {
      mutationsList.forEach(function (mutation) {
        if (mutation.type === 'childList' && mutation.target === cartDrawer) {
          //console.log('Cart drawer content updated, reattaching listeners.');
          setTimeout(() => {
            attachQuickBuyListeners(cartDrawer);
          }, 1500);
        }
      });
    });

    observer.observe(cartDrawer, { childList: true, subtree: true });
  }
}

function initializeAllScripts() {
  initializeRemoveLinks();
  initializeAddToCartForms();
  attachQuickBuyListeners();
  observeCartDrawer();
}

// Run on DOMContentLoaded and pageshow (for back/forward navigation)
document.addEventListener('DOMContentLoaded', initializeAllScripts);
window.addEventListener('pageshow', initializeAllScripts);

// Prevent forward navigation after going back (history manipulation)
window.addEventListener('popstate', function (event) {
  history.pushState(null, null, location.href); // Stay on the same page
});

// Add an initial state to the history stack to prevent forward navigation
history.pushState(null, null, location.href);
