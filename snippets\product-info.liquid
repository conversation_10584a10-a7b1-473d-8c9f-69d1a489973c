{%- capture product_form_id -%}product-form-{{ product.id }}-{{ section.id }}{%- endcapture -%}

<safe-sticky class="product-info">
  {%- for block in section.blocks -%}
    {%- assign previous_block_index = forloop.index0 | minus: 1 -%}
    {%- assign previous_block = section.blocks[previous_block_index] -%}
    {%- assign next_block = section.blocks[forloop.index] -%}

    {%- case block.type -%}
      {%- when '@app' -%}
        {%- render block -%}

      {%- when 'vendor' -%}
        {%- if product.vendor != blank -%}
          <div class="product-info__vendor" {{ block.shopify_attributes }}>
            {%- render 'vendor' with product.vendor -%}
          </div>
        {%- endif -%}

      {%- when 'title' -%}
      <!-- <div class="liking-badge">
<p class="custom-recommended-by-followers">
  <img class=" lazyloaded" data-src="https://cdn.shopify.com/s/files/1/0601/4297/7098/files/3_fa76ecf6-a659-4951-a03f-dbe6631a2ef0.png?v=1724218761" style="width:22px; margin-right: 4px;margin-bottom:0px;" loading="lazy" src="https://cdn.shopify.com/s/files/1/0601/4297/7098/files/3_fa76ecf6-a659-4951-a03f-dbe6631a2ef0.png?v=1724218761">
  <span><b>Michael P.</b><img class=" lazyloaded" data-src="https://cdn.shopify.com/s/files/1/0589/9999/6469/t/4/assets/PngItem_3024199.png?v=1666086203" style="width: 10px; margin-bottom: -1px; margin-left: 1px;" loading="lazy" src="https://cdn.shopify.com/s/files/1/0589/9999/6469/t/4/assets/PngItem_3024199.png?v=1666086203">
   and <b>235,000+ others</b> loves our clothing</span></p>
        </div> -->
<div class="liking-badge">
<p class="custom-recommended-by-followers">
  <img class=" lazyloaded" data-src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/like-by-others.webp?v=1733833273" style="width:22px; margin-right: 4px;" loading="lazy" src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/like-by-others.webp?v=1733833273">
  <span><b>Victoria A.</b><img class=" lazyloaded" data-src="https://cdn.shopify.com/s/files/1/0589/9999/6469/t/4/assets/PngItem_3024199.png?v=1666086203" style="width: 10px; margin-bottom: -1px; margin-left: 1px;" loading="lazy" src="https://cdn.shopify.com/s/files/1/0589/9999/6469/t/4/assets/PngItem_3024199.png?v=1666086203">
    {{ 'product.general.and' | t }} <b>{{ product.metafields.custom.likers }}</b> {{ product.metafields.custom.recommended_text }}</span></p>
        </div>


        {%- if request.page_type == 'product' -%}

          <h1 class="product-info__title {{ block.settings.heading_tag }}" {{ block.shopify_attributes }}>{{ product.title }}</h1>
        {%- else -%}
          <h2 class="product-info__title {{ block.settings.heading_tag }}" {{ block.shopify_attributes }}>
            <a href="{{ product.url }}">{{ product.title }}</a>
          </h2>

 <!-- <div class="loox-rating" data-color-star="#EBBF1F" data-pattern="[count] Reviews" data-id="{{ product.id }}" data-rating="{{ product.metafields.loox.avg_rating }}" data-raters="{{ product.metafields.loox.num_reviews }}"></div> -->

        {%- endif -%}
<!-- promo box -->


          {% if product.metafields.custom.review_count %}
              <div class="promoBox review">
                     <img src="https://cdn.instant.so/sites/MCixpD4Qk3tQYIQs/assets/oSopK4DagRW18jcX/5bfbd91939f6b831a45f1431b3d502d2166cf297.svg" alt="" width="97" height="17"> <font size="3" > <a href="#review-section" /><u><b>{{ product.metafields.custom.review_count }} Reviews</b></u> </a> </font>
              </div>
              {% endif %}
  <!-- promo box 1 -->
{% if product.metafields.custom.p_detail_1_icon %}
        {% if product.metafields.custom.p_details_1 %}
              <div class="promoBox">
                     <img src="{{ product.metafields.custom.p_detail_1_icon }}" alt="" style="padding:1px;max-width:20px;"> <font size="3" > {{ product.metafields.custom.p_details_1 }}  </font>
              </div>
              {% endif %}
      {% else %}
         {% if product.metafields.custom.p_details_1 %}
        <div class="promoBox">
                     <img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/default-promobox.webp?v=1733732776" alt="" style="padding:1px;max-width:20px;"> <font size="3" > {{ product.metafields.custom.p_details_1 }}  </font>
              </div>
     {% endif %}
      {% endif %}


<!-- promo box 2-->
      {% if product.metafields.custom.p_detail_2_icon %}
        {% if product.metafields.custom.p_details_2 %}
              <div class="promoBox">
                     <img src="{{ product.metafields.custom.p_detail_2_icon }}" alt="" style="padding:1px;max-width:20px;"> <font size="3" > {{ product.metafields.custom.p_details_2 }}  </font>
              </div>
              {% endif %}
        {% else %}
        {% if product.metafields.custom.p_details_2 %}
        <div class="promoBox">
                     <img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/default-promobox.webp?v=1733732776" alt="" style="padding:1px;max-width:20px;"> <font size="3" > {{ product.metafields.custom.p_details_2 }}  </font>
              </div>
          {% endif %}
      {% endif %}

<!-- promo box 3-->
      {% if product.metafields.custom.p_detail_3_icon %}
      {% if product.metafields.custom.p_details_3 %}
              <div class="promoBox">
                     <img src="{{ product.metafields.custom.p_detail_3_icon }}" alt="" style="padding:1px;max-width:20px;"> <font size="3" > {{ product.metafields.custom.p_details_3 }}  </font>
              </div>
              {% endif %}
      {% else %}
        {% if product.metafields.custom.p_details_3 %}
        <div class="promoBox">
                     <img src="https://cdn.shopify.com/s/files/1/0912/3145/3467/files/default-promobox.webp?v=1733732776" alt="" style="padding:1px;max-width:20px;"> <font size="3" > {{ product.metafields.custom.p_details_3 }}  </font>
              </div>
           {% endif %}
      {% endif %}


      {%- when 'title_custom' -%}
      {%- liquid
        assign main_color = block.settings.primary_color
        assign accent_color = block.settings.secondary_color
        if block.settings.swap_colors
          assign main_color = block.settings.secondary_color
          assign accent_color = block.settings.primary_color
        endif
      -%}
      {%- if request.page_type == 'product' -%}
      <h1 class="product-info__title pdp-title" style="--title-main: {{ main_color }}; --title-accent: {{ accent_color }};" {{ block.shopify_attributes }}>
        <span class="pdp-title__main h4">{{ product.title }}</span>
        {%- if block.settings.accent_text != blank -%}
          <span class="pdp-title__accent">{{ block.settings.accent_text }}</span>
        {%- endif -%}
      </h1>
      {%- else -%}
      <h2 class="product-info__title pdp-title" style="--title-main: {{ main_color }}; --title-accent: {{ accent_color }};" {{ block.shopify_attributes }}>
        <a href="{{ product.url }}">
          <span class="pdp-title__main h4">{{ product.title }}</span>
          {%- if block.settings.accent_text != blank -%}
            <span class="pdp-title__accent">{{ block.settings.accent_text }}</span>
          {%- endif -%}
        </a>
      </h2>
      {%- endif -%}

      {%- when 'badges' -%}
        {%- render 'product-badges', product: product, types: 'custom', form_id: product_form_id, class: 'product-info__badge-list', block: block -%}

      {%- when 'sku' -%}
        <variant-sku form="{{ product_form_id }}" class="product-info__sku text-xs text-subdued" {% if product.selected_or_first_available_variant.sku == blank %}hidden{% endif %} {{ block.shopify_attributes }}>
          {{- 'product.general.sku' | t }} {{ product.selected_or_first_available_variant.sku -}}
        </variant-sku>

      {%- when 'price' -%}
        <div class="product-info__price" style="margin: 10px 0; --price-color: {{ block.settings.price_color }}; --price-compare: {{ block.settings.compare_color }}; --badge-bg: {{ block.settings.sale_badge_bg }}; --badge-text: {{ block.settings.sale_badge_text }};">
          <div class="rating-with-text">
            {%- render 'price-list', variant: product.selected_or_first_available_variant, form_id: product_form_id, size: 'lg', block: block -%}
            {%- render 'product-badges', types: 'sold_out, discount', product: product, form_id: product_form_id, class: 'product-info__badge-list' -%}

            {%- comment -%}If the next block is a rating block, then we add it as part of this one to have it inline{%- endcomment -%}
            {%- if next_block.type == 'rating' -%}
              {%- render 'product-rating', product: product, show_empty: next_block.settings.show_empty, block: next_block -%}
            {%- endif -%}
            {%- if block.settings.show_discount_badge -%}
            <p>{{ block.settings.discount_badge_text }}</p>
            {% endif %}
          </div>

          {%- if block.settings.show_taxes_notice -%}
            <p class="text-sm text-subdued">
              {%- if cart.taxes_included -%}
                {{ 'product.general.taxes_included' | t }}
              {%- else -%}
                {{ 'product.general.taxes_excluded' | t }}
              {%- endif -%}

              {%- if shop.shipping_policy.body != blank -%}
                {{ 'product.general.shipping_policy_html' | t: link: shop.shipping_policy.url }}
              {%- endif -%}
            </p>
          {%- endif -%}
        </div>
{%- if product.metafields.custom.spring_sale != blank -%}
              {% comment %}Custom Info{% endcomment %}
              <div class="custom-spring-sale-info">
                {%- if product.metafields.custom.spring_sale_image.value != blank -%}
                  <img src="{{ product.metafields.custom.spring_sale_image.value | img_url: 'master' }}" class="img-emoji">
                {%- endif -%}
                {%- if product.metafields.custom.spring_sale != blank -%}
                  <span>{{ product.metafields.custom.spring_sale | metafield_tag }}</span>
                {%- endif -%}
              </div>
                {%- endif -%}

<style>
@keyframes blink {
  50% { opacity: 0; }
}

.custom-spring-sale-info .metafield-rich_text_field p::before {
  content: "🟠"; /* Change emoji as needed */
  display: inline-block;
  margin-right: 5px;
  animation: blink 3s infinite;
  font-size: 10px;
  vertical-align: middle;
  line-height: 1;
}
</style>




      {%- when 'rating' -%}
        {%- comment -%}If the previous block is of type price, then the rating has been rendered inside it so we do not render it twice{%- endcomment -%}

        {%- if previous_block.type != 'price' -%}
          <div class="product-info__rating">
            {%- render 'product-rating', product: product, show_empty: block.settings.show_empty, block: block -%}
          </div>
        {%- endif -%}

      {%- when 'payment_terms' -%}
        <payment-terms class="product-info__payment-terms" form="{{ product_form_id }}" {{ block.shopify_attributes }}>
          {%- capture product_installment_form_id -%}product-installment-form-{{ section.id }}-{{ product.id }}{%- endcapture -%}

          {%- form 'product', product, id: product_installment_form_id -%}
            <input type="hidden" name="id" value="{{ product.selected_or_first_available_variant.id }}">
            {{- form | payment_terms -}}
          {%- endform -%}
        </payment-terms>

      {%- when 'separator' -%}
        <hr class="product-info__separator" {{ block.shopify_attributes }}>

      {%- when 'description' -%}
        {%- if block.settings.collapse_content -%}
          {%- if product.description != blank -%}
            {%- capture accordion_title -%}{{ 'product.general.description' | t }}{%- endcapture -%}
            {%- capture accordion_content -%}<div class="prose">{{ product.description }}</div>{%- endcapture -%}

            {%- render 'accordion', title: accordion_title, content: accordion_content, class: 'product-info__accordion', block: block -%}
          {%- endif -%}
        {%- else -%}
          {%- if product.description != blank -%}
            <div class="product-info__description" {{ block.shopify_attributes }}>
              <div class="prose">
                {{- product.description -}}
              </div>
            </div>
          {%- endif -%}
        {%- endif -%}

      {%- when 'variant_picker' -%}
        <div class="product-info__variant-picker" {{ block.shopify_attributes }}>
          {%- render 'variant-picker',
            product: product,
            form_id: product_form_id,
            update_url: update_url,
            hide_sold_out_variants: block.settings.hide_sold_out_variants,
            block: block
          -%}
        </div>
{%- when 'custom_variant_picker'  -%}
        <div class="product-info__variant-picker" {{ block.shopify_attributes }}>
          {%- render 'custom-variant-picker',
            product: product,
            form_id: product_form_id,
            update_url: update_url,
            hide_sold_out_variants: block.settings.hide_sold_out_variants,
            block: block
          -%}
        </div>



      {%- when 'product_variations' -%}
        {%- assign contains_product = false -%}

        {%- for product_variation in block.settings.products -%}
          {%- if product_variation == product -%}
            {%- assign contains_product = true -%}
          {%- endif -%}
        {%- endfor -%}

        {%- if contains_product -%}
          {%- comment -%}
            IMPLEMENTATION NOTE: this feature allows to connect different products on the same page. It is therefore
            visually similar to a variant picker, although it does not share the functionality
          {%- endcomment -%}

          <div class="product-info__product-picker" {{ block.shopify_attributes }}>
            <div class="variant-picker">
              <fieldset class="variant-picker__option">
                {%- assign metafield_parts = block.settings.option_value_metafield | split: '.' -%}
                {%- assign metafield_namespace = metafield_parts | first -%}
                {%- assign metafield_key = metafield_parts | last -%}

                {%- if block.settings.option_name != blank -%}
                  <div class="variant-picker__option-info">
                    <div class="h-stack gap-2">
                      <legend class="text-subdued">{{ block.settings.option_name | escape }}:</legend>
                      <span>{{ product.metafields[metafield_namespace][metafield_key].value | escape }}</span>
                    </div>
                  </div>
                {%- endif -%}
                <div class="variant-picker__option-values wrap gap-2">

                  {%- for product_variation in block.settings.products -%}
                    {%- assign value = product_variation.metafields[metafield_namespace][metafield_key].value -%}

                    {%- if product_variation == product -%}
                      {%- assign selected = true -%}
                    {%- else -%}
                      {%- assign selected = false -%}
                    {%- endif -%}

                    {%- case block.settings.selector_style -%}
                      {%- when 'swatch' -%}
                      <p>test</p>
                        {%- render 'option-value', type: 'swatch', href: product_variation.url, value: value, selected: selected -%}

                      {%- when 'variant_image' -%}
                        {%- render 'option-value', type: 'thumbnail', href: product_variation.url, value: value, image: product_variation.featured_media, selected: selected, bordered: true -%}

                      {%- when 'block' -%}
                        {%- render 'option-value', type: 'block', href: product_variation.url, value: value, selected: selected -%}

                      {%- when 'block_swatch' -%}
                        {%- render 'option-value', type: 'block', href: product_variation.url, value: value, selected: selected, show_swatch: true -%}
                    {%- endcase -%}
                  {%- endfor -%}
                </div>
              </fieldset>
            </div>
          </div>
        {%- endif -%}

      {%- when 'line_item_property' -%}
        {%- if block.settings.label != blank -%}
          {%- capture name -%}properties[{{ block.settings.label | escape }}]{%- endcapture -%}

          <div class="product-info__property" {{ block.shopify_attributes }}>
            {%- if block.settings.type == 'text' -%}
              {%- if block.settings.allow_long_text -%}
                {%- render 'input', label: block.settings.label, name: name, form: product_form_id, multiline: 4, required: block.settings.required, maxlength: block.settings.max_length -%}
              {%- else -%}
                {%- render 'input', type: 'text', label: block.settings.label, name: name, form: product_form_id, required: block.settings.required, maxlength: block.settings.max_length -%}
              {%- endif -%}
            {%- else -%}
              {%- render 'checkbox', label: block.settings.label, name: name, required: block.settings.required, form: product_form_id -%}
            {%- endif -%}
          </div>
        {%- endif -%}

      {%- when 'quantity_selector' -%}
        {%- if product.available -%}
          <div class="product-info__quantity-selector {{ block.shopify_attributes }}">
            <div class="form-control">
              <label for="{{ product_form_id }}-quantity" class="block-label text-subdued">{{- 'product.quantity.label' | t -}}:</label>

              <quantity-selector class="quantity-selector">
                <button type="button" class="quantity-selector__button" aria-label="{{ 'product.quantity.decrease_quantity' | t }}">{% render 'icon' with 'minus', width: 10, height: 2 %}</button>
                <input id="{{ product_form_id }}-quantity" type="text" is="quantity-input" inputmode="numeric" class="quantity-selector__input" name="quantity" form="{{ product_form_id }}" size="2" value="1" autocomplete="off">
                <button type="button" class="quantity-selector__button" aria-label="{{ 'product.quantity.increase_quantity' | t }}">{% render 'icon' with 'plus', width: 10, height: 10 %}</button>
              </quantity-selector>
            </div>
          </div>
        {%- endif -%}





      {%- when 'inventory' -%}
        <div class="product-info__inventory {{ block.shopify_attributes }}">
          {%- render 'inventory', product: product, low_threshold: block.settings.low_inventory_threshold, form_id: product_form_id -%}
        </div>

      {%- when 'buy_buttons' -%}


        {%- assign main_form_exists = true -%}
        {%- if request.page_type != 'password' -%}
          {%- liquid
            assign atc_radius_val = '8px'
            case block.settings.atc_corner_style
              when 'square'
                assign atc_radius_val = '0px'
              when 'pill'
                assign atc_radius_val = '9999px'
              else
                assign atc_radius_val = '8px'
            endcase
          -%}

          <div class="product-info__buy-buttons" style="--btn-radius: {{ atc_radius_val }};" {{ block.shopify_attributes }}>
            {%- render 'buy-buttons', product: product, form_id: product_form_id, show_payment_button: block.settings.show_payment_button, show_gift_card_recipient: block.settings.show_gift_card_recipient, atc_button_background: block.settings.atc_button_background, atc_button_text_color: block.settings.atc_button_text_color, payment_button_background: block.settings.payment_button_background, payment_button_text_color: block.settings.payment_button_text_color, atc_label: block.settings.atc_label, show_price_in_atc: block.settings.show_price_in_atc, atc_icon: block.settings.atc_icon, atc_price_color: block.settings.atc_price_color, atc_compare_color: block.settings.atc_compare_color -%}
          </div>
        {%- endif -%}

 <!-- Free shipping text -->
     {%- if section.settings.free_shipping_text != blank -%}
<div class="shipping-banner" style="background-color:{{ section.settings.shipping_text_background }}; border: 1px dashed {{ section.settings.shipping_text_border | default: '#c6d4bd' }}; color: {{ section.settings.shipping_text_color }}">
    <span class="left-text">{{ section.settings.free_shipping_text }}</span>
    <span class="right-text">{{ section.settings.shipping_to_text }}</span>
</div>
    {% endif %}

<!-- free order section -->
 {%- if section.settings.custom_order_header != blank -%}
<div class="custom-free-order-section">
    <div class="custom-order-header" style="background-color:{{ section.settings.custom_header_color }}; color:{{ section.settings.header_text_color  }}">{{ section.settings.custom_order_header }}</div>
    <div class="custom-order-content" style="background-color: {{ section.settings.custom_order_background }}">
      <div class="custom-order-image">
        <img class="" src="{{ section.settings.custom_order_image | img_url: 'large' }}" alt="image" />
      </div>
        <div class="custom-order-description">
            <p>{{ section.settings.custom_order_description }}</p>
        </div>
      <div class="custom-order-offer">
        <div class="offer-text">
            <span class="offer-1">{{ section.settings.custom_order_text1 }}</span><span class="offer-2" style="color:{{ section.settings.custom_text2_color }};"> {{ section.settings.custom_order_text2 }}</span>
        </div>
        <div class="offer-badge">
            <span class="custom-worth-badge" style="background-color:{{ section.settings.custom_badge_color }}; color:{{ section.settings.custom_worth_color }};">{{ section.settings.custom_worth_text }}</span>
        </div>
      </div>
    </div>
</div>
{% endif %}













      {% comment %}
      {% render 'payment-icons' %}
      {% endcomment %}

      {%- when 'pickup_availability' -%}
        <div class="product-info__pickup-availability" {{ block.shopify_attributes }}>
          {%- render 'pickup-availability', product_variant: product.selected_or_first_available_variant, form_id: product_form_id -%}
        </div>

      {%- when 'text' -%}
        {%- if block.settings.text != blank -%}
          <div class="product-info__text" {{ block.shopify_attributes }}>
            <div class="prose">
              {{- block.settings.text -}}
            </div>
          </div>
        {%- endif -%}

      {%- when 'collapsible_text' -%}
        {%- if block.settings.title != blank and block.settings.content != blank or block.settings.page.content != blank -%}
          {%- capture accordion_content -%}<div class="prose">{{ block.settings.page.content | default: block.settings.content }}</div>{%- endcapture -%}
          {%- render 'accordion', title: block.settings.title, content: accordion_content, class: 'product-info__accordion', block: block -%}
        {%- endif -%}

      {%- when 'image' -%}
        {%- if block.settings.image != blank -%}
          <div class="product-info__image" {{ block.shopify_attributes }}>
            {%- capture sizes -%}{{ block.settings.max_width }}px{%- endcapture -%}
            {%- capture widths -%}{{ block.settings.max_width }}, {{ block.settings.max_width | times: 2 | at_most: block.settings.image.width }}{%- endcapture -%}
            {%- capture style -%}width: {{ block.settings.max_width }}px; {% if block.settings.alignment == 'center' %}margin-inline: auto{% elsif block.settings.alignment == 'end' %}margin-inline-start: auto;{% endif %}{%- endcapture -%}
            {{- block.settings.image | image_url: width: block.settings.image.width | image_tag: loading: 'lazy', style: style, sizes: sizes, widths: widths -}}
          </div>
        {%- endif -%}

      {%- when 'button' -%}
        {%- if block.settings.text != blank -%}
          <div class="product-info__button" {{ block.shopify_attributes }}>
            {%- render 'button', content: block.settings.text, href: block.settings.link, size: block.settings.size, style: block.settings.style, stretch: block.settings.stretch, background: block.settings.background, text_color: block.settings.text_color -%}
          </div>
        {%- endif -%}

      {%- when 'liquid' -%}
        {%- if block.settings.liquid != blank -%}
          <div class="product-info__liquid" {{ block.shopify_attributes }}>
            {{ block.settings.liquid }}
          </div>
        {%- endif -%}

      {%- when 'associated_products' -%}
        <product-recommendations class="block" product="{{ product.id }}" limit="{{ block.settings.products_count }}" intent="complementary" {{ block.shopify_attributes }}>
          {%- if recommendations.performed and recommendations.products_count > 0 -%}
            <div class="product-info__complementary-products v-stack gap-3 sm:gap-4">
              {%- assign carousel_id = 'carousel-' | append: block.id -%}

              {%- if block.settings.title != blank or block.settings.stack_products == false and recommendations.products_count > 1 -%}
                <div class="h-stack justify-between gap-4">
                  {%- if block.settings.title != blank -%}
                    <p>{{ block.settings.title | escape }}</p>
                  {%- endif -%}

                  {%- if block.settings.stack_products == false and recommendations.products_count > 1 -%}
                    <div class="h-stack gap-2 hidden sm:flex">
                      <button is="prev-button" class="circle-chevron hover:colors" aria-controls="{{ carousel_id }}" aria-label="{{ 'general.accessibility.previous' | t | escape }}" disabled>{%- render 'icon' with 'chevron-left-small', direction_aware: true -%}</button>
                      <button is="next-button" class="circle-chevron hover:colors" aria-controls="{{ carousel_id }}" aria-label="{{ 'general.accessibility.next' | t | escape }}">{%- render 'icon' with 'chevron-right-small', direction_aware: true -%}</button>
                    </div>
                  {%- endif -%}
                </div>
              {%- endif -%}

              {%- capture horizontal_products -%}
                {%- for associated_product in recommendations.products -%}
                  {%- render 'horizontal-product', product: associated_product, show_add_to_cart: true, background: block.settings.background, text_color: block.settings.text_color -%}
                {%- endfor -%}
              {%- endcapture -%}

              {%- assign horizontal_products_blends = true -%}
              {%- assign section_background = section.settings.background_gradient | default: section.settings.background | default: settings.background -%}

              {%- if block.settings.background != 'rgba(0,0,0,0)' and block.settings.background != blank and block.settings.background != section_background -%}
                {%- assign horizontal_products_blends = false -%}
              {%- endif -%}

              {%- if block.settings.stack_products -%}
                <div class="horizontal-product-list {% if horizontal_products_blends %}border divide-y rounded-xs{% else %}separate{% endif %}">
                  {{- horizontal_products -}}
                </div>
              {%- else -%}
                <scroll-carousel selector=".horizontal-product" id="{{ carousel_id }}" class="horizontal-product-list-carousel {% unless horizontal_products_blends %}separate{% endunless %} scroll-area bleed sm:unbleed">
                  <div class="horizontal-product-list {% if horizontal_products_blends %}divide-x{% else %}separate{% endif %}">
                    {{- horizontal_products -}}
                  </div>
                </scroll-carousel>
              {%- endif -%}
            </div>
          {%- endif -%}
        </product-recommendations>

      {%- when 'offer' -%}
        {%- if previous_block.type != 'offer' and next_block.type == 'offer' -%}
          <div class="product-info__offer-list">
        {%- endif -%}

        {%- if block.settings.title != blank or block.settings.content != blank -%}
          {%- render 'offer', block: block -%}
        {%- endif -%}

        {%- if forloop.last or previous_block.type == 'offer' and next_block.type != 'offer' -%}
          </div>
        {%- endif -%}

      {%- when 'benefit_check' -%}
        <div class="pdp-checklist__item" style="--check-icon-color: {{ block.settings.icon_color }}; --check-icon-bg: {{ block.settings.icon_bg }}; --check-text-color: {{ block.settings.text_color }}; --check-row-bg: rgba(0,0,0,0);" {{ block.shopify_attributes }}>
          <span class="pdp-checklist__icon" aria-hidden="true">
            <svg viewBox="0 0 20 20" width="18" height="18" focusable="false" role="img">
              <circle cx="10" cy="10" r="9" fill="var(--check-icon-bg)"></circle>
              <path d="M6 10.5l2.2 2.3 5-5.4" fill="none" stroke="var(--check-icon-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </svg>
          </span>
          {%- if block.settings.text != blank -%}
            <span class="pdp-checklist__text">{{ block.settings.text }}</span>
          {%- endif -%}
        </div>

      {%- when 'benefit_checklist' -%}
        <div class="pdp-checklist" style="--check-icon-color: {{ block.settings.icon_color }}; --check-icon-bg: {{ block.settings.icon_bg }}; --check-text-color: {{ block.settings.text_color }}; --check-row-bg: {{ block.settings.row_bg }};" {{ block.shopify_attributes }}>
          {%- if block.settings.item1 != blank -%}
            <div class="pdp-checklist__item">
              <span class="pdp-checklist__icon" aria-hidden="true">
                <svg viewBox="0 0 20 20" width="18" height="18" focusable="false" role="img">
                  <circle cx="10" cy="10" r="9" fill="var(--check-icon-bg)"></circle>
                  <path d="M6 10.5l2.2 2.3 5-5.4" fill="none" stroke="var(--check-icon-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
              </span>
              <span class="pdp-checklist__text">{{ block.settings.item1 }}</span>
            </div>
          {%- endif -%}
          {%- if block.settings.item2 != blank -%}
            <div class="pdp-checklist__item">
              <span class="pdp-checklist__icon" aria-hidden="true">
                <svg viewBox="0 0 20 20" width="18" height="18" focusable="false" role="img">
                  <circle cx="10" cy="10" r="9" fill="var(--check-icon-bg)"></circle>
                  <path d="M6 10.5l2.2 2.3 5-5.4" fill="none" stroke="var(--check-icon-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
              </span>
              <span class="pdp-checklist__text">{{ block.settings.item2 }}</span>
            </div>
          {%- endif -%}
          {%- if block.settings.item3 != blank -%}
            <div class="pdp-checklist__item">
              <span class="pdp-checklist__icon" aria-hidden="true">
                <svg viewBox="0 0 20 20" width="18" height="18" focusable="false" role="img">
                  <circle cx="10" cy="10" r="9" fill="var(--check-icon-bg)"></circle>
                  <path d="M6 10.5l2.2 2.3 5-5.4" fill="none" stroke="var(--check-icon-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
              </span>
              <span class="pdp-checklist__text">{{ block.settings.item3 }}</span>
            </div>
          {%- endif -%}
          {%- if block.settings.item4 != blank -%}
            <div class="pdp-checklist__item">
              <span class="pdp-checklist__icon" aria-hidden="true">
                <svg viewBox="0 0 20 20" width="18" height="18" focusable="false" role="img">
                  <circle cx="10" cy="10" r="9" fill="var(--check-icon-bg)"></circle>
                  <path d="M6 10.5l2.2 2.3 5-5.4" fill="none" stroke="var(--check-icon-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
              </span>
              <span class="pdp-checklist__text">{{ block.settings.item4 }}</span>
            </div>
          {%- endif -%}
          {%- if block.settings.item5 != blank -%}
            <div class="pdp-checklist__item">
              <span class="pdp-checklist__icon" aria-hidden="true">
                <svg viewBox="0 0 20 20" width="18" height="18" focusable="false" role="img">
                  <circle cx="10" cy="10" r="9" fill="var(--check-icon-bg)"></circle>
                  <path d="M6 10.5l2.2 2.3 5-5.4" fill="none" stroke="var(--check-icon-color)" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
              </span>
              <span class="pdp-checklist__text">{{ block.settings.item5 }}</span>
            </div>
          {%- endif -%}
        </div>

      {%- when 'feature_badge_grid' -%}
        <div class="pdp-feature-badges" style="--fb-badge-bg: {{ block.settings.badge_bg }}; --fb-text: {{ block.settings.badge_text }}; margin-top: {{ block.settings.margin_top | default: 0 }}px; margin-bottom: {{ block.settings.margin_bottom | default: 0 }}px;" {{ block.shopify_attributes }}>
          {%- if block.settings.text1 != blank or block.settings.icon1 != blank -%}
            <div class="pdp-feature-badge">
              {%- if block.settings.icon1 != blank -%}
                {{ block.settings.icon1 | image_url: width: 64 | image_tag: class: 'pdp-feature-badge__icon', loading: 'lazy', widths: '32,64' }}
              {%- endif -%}
              {%- if block.settings.text1 != blank -%}<span class="pdp-feature-badge__text">{{ block.settings.text1 }}</span>{%- endif -%}
            </div>
          {%- endif -%}
          {%- if block.settings.text2 != blank or block.settings.icon2 != blank -%}
            <div class="pdp-feature-badge">
              {%- if block.settings.icon2 != blank -%}
                {{ block.settings.icon2 | image_url: width: 64 | image_tag: class: 'pdp-feature-badge__icon', loading: 'lazy', widths: '32,64' }}
              {%- endif -%}
              {%- if block.settings.text2 != blank -%}<span class="pdp-feature-badge__text">{{ block.settings.text2 }}</span>{%- endif -%}
            </div>
          {%- endif -%}
          {%- if block.settings.text3 != blank or block.settings.icon3 != blank -%}
            <div class="pdp-feature-badge">
              {%- if block.settings.icon3 != blank -%}
                {{ block.settings.icon3 | image_url: width: 64 | image_tag: class: 'pdp-feature-badge__icon', loading: 'lazy', widths: '32,64' }}
              {%- endif -%}
              {%- if block.settings.text3 != blank -%}<span class="pdp-feature-badge__text">{{ block.settings.text3 }}</span>{%- endif -%}
            </div>
          {%- endif -%}
          {%- if block.settings.text4 != blank or block.settings.icon4 != blank -%}
            <div class="pdp-feature-badge">
              {%- if block.settings.icon4 != blank -%}
                {{ block.settings.icon4 | image_url: width: 64 | image_tag: class: 'pdp-feature-badge__icon', loading: 'lazy', widths: '32,64' }}
              {%- endif -%}
              {%- if block.settings.text4 != blank -%}<span class="pdp-feature-badge__text">{{ block.settings.text4 }}</span>{%- endif -%}
            </div>
          {%- endif -%}
        </div>

      {%- when 'rating_banner' -%}
        {%- liquid
          assign rating_value_str = block.settings.rating_value | default: "4.8"
          assign rating_value = rating_value_str | plus: 0.0
          assign full_stars = rating_value | floor
          assign empty_stars = 5 | minus: full_stars
          if rating_value != full_stars
            assign has_partial = true
            assign empty_stars = empty_stars | minus: 1
          else
            assign has_partial = false
          endif
        -%}
        <div class="pdp-rating-strip" style="--rs-star: {{ block.settings.star_color }}; --rs-label: {{ block.settings.label_color }}; --rs-badge-bg: {{ block.settings.badge_bg }}; --rs-badge-text: {{ block.settings.badge_text_color }}; margin-top: {{ block.settings.margin_top | default: 0 }}px; margin-bottom: {{ block.settings.margin_bottom | default: 0 }}px;" {{ block.shopify_attributes }}>
          <div class="pdp-rating-strip__content">
            <span class="pdp-rating-strip__stars" aria-hidden="true">
              {%- comment -%}Full stars{%- endcomment -%}
              {%- for i in (1..full_stars) -%}
                <svg class="pdp-star pdp-star--full" viewBox="0 0 24 24" width="18" height="18" focusable="false" role="img"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" fill="var(--rs-star)"/></svg>
              {%- endfor -%}
              {%- comment -%}Partial star{%- endcomment -%}
              {%- if has_partial -%}
                <svg class="pdp-star pdp-star--partial" viewBox="0 0 24 24" width="18" height="18" focusable="false" role="img"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" fill="var(--rs-star)" opacity="0.3"/></svg>
              {%- endif -%}
              {%- comment -%}Empty stars{%- endcomment -%}
              {%- for i in (1..empty_stars) -%}
                <svg class="pdp-star pdp-star--empty" viewBox="0 0 24 24" width="18" height="18" focusable="false" role="img"><path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" fill="var(--rs-star)" opacity="0.2"/></svg>
              {%- endfor -%}
            </span>
            <span class="pdp-rating-strip__label">
              <span class="pdp-rating-strip__desktop">Rating {{ rating_value }}/5</span>
              <span class="pdp-rating-strip__mobile">({{ rating_value }}/5)</span>
            </span>
          </div>
          {%- if block.settings.badge_text != blank -%}<span class="pdp-rating-strip__badge">{{ block.settings.badge_text }}</span>{%- endif -%}
        </div>



      {%- when 'review_block' -%}
        {%- liquid
          assign rv_radius = '8px'
          case block.settings.corner_style
            when 'square'
              assign rv_radius = '0px'
            when 'pill'
              assign rv_radius = '9999px'
            else
              assign rv_radius = '8px'
          endcase
        -%}

        <style>
          #review-{{ block.id }} .pdp-review__row {
            display: flex; gap: 12px; align-items: flex-start;
            flex-direction: {% if block.settings.image_position == 'right' %}row-reverse{% else %}row{% endif %};
            flex-wrap: nowrap;
          }
          @media (max-width: 640px) {
            #review-{{ block.id }} .pdp-review__row {
              {% if block.settings.stack_on_mobile %}flex-direction: column;{% endif %}
            }
          }
          #review-{{ block.id }} .pdp-review__icon-img { width: var(--rv-icon-width); height: auto; border-radius: 6px; }
          #review-{{ block.id }} .pdp-review__check { width: {{ block.settings.check_size | default: 14 }}px; height: {{ block.settings.check_size | default: 14 }}px; }
          #review-{{ block.id }} .pdp-review__star { width: {{ block.settings.star_size | default: 14 }}px; height: {{ block.settings.star_size | default: 14 }}px; }
        </style>


        <div id="review-{{ block.id }}" class="pdp-review" style="--rv-icon-width: {{ block.settings.image_width | default: 28 }}px; --rv-bg: {{ block.settings.background_color }}; --rv-border-color: {{ block.settings.border_color }}; --rv-border-style: {{ block.settings.border_style }}; --rv-name: {{ block.settings.name_color }}; --rv-text: {{ block.settings.text_color }}; --rv-star: {{ block.settings.star_color }}; --rv-check: {{ block.settings.check_color }}; --rv-radius: {{ rv_radius }}; margin-top: {{ block.settings.margin_top | default: 0 }}px; margin-bottom: {{ block.settings.margin_bottom | default: 0 }}px; {% if block.settings.border_style == 'dotted' %}border: 2px dotted var(--rv-border-color); padding: {{ block.settings.dotted_gap | default: 10 }}px;{% else %}border: 1px solid var(--rv-border-color); padding: 0;{% endif %} border-radius: var(--rv-radius);" {{ block.shopify_attributes }}>
          <div class="pdp-review__box" style="width: 100%; border: none; background: var(--rv-bg); padding: 12px; border-radius: var(--rv-radius);">
            <div class="pdp-review__row">
              {%- if block.settings.icon_image != blank -%}
                <div class="pdp-review__icon">
                  {{ block.settings.icon_image | image_url: width: 160 | image_tag: loading: 'lazy', widths: '40,80,160', sizes: '40px', class: 'pdp-review__icon-img', alt: block.settings.reviewer_name | default: 'Reviewer image' }}
                </div>

              {%- endif -%}

              <div class="v-stack gap-1" style="flex: 1;">
                <div class="h-stack gap-2 items-center">
                  {%- if block.settings.reviewer_name != blank -%}
                    <span class="bold" style="color: var(--rv-name);">{{ block.settings.reviewer_name }}</span>
                  {%- endif -%}

                  {%- if block.settings.show_verified -%}
                    <span class="pdp-review__verified" aria-label="Verified reviewer" title="Verified reviewer" style="color: var(--rv-check); display: inline-flex; align-items: center;">
                      {%- render 'icon' with 'success', class: 'pdp-review__check' -%}
                    </span>
                  {%- endif -%}
                      {%- if block.settings.verified_text != blank -%}
                        <span class="pdp-review__verified-label" style="color: var(--rv-name); font-size: 0.9em; margin-inline-start: 4px;">{{ block.settings.verified_text }}</span>
                      {%- endif -%}


                  <span class="pdp-review__stars" role="img" aria-label="{{ block.settings.stars }} out of 5 stars" style="display: inline-flex; gap: 2px; color: var(--rv-star);">
                    {%- for i in (1..block.settings.stars) -%}
                      {%- render 'icon' with 'rating-star', class: 'pdp-review__star' -%}
                    {%- endfor -%}
                  </span>
                </div>

                {%- if block.settings.review_text != blank -%}
                  <div class="pdp-review__text rte" style="color: var(--rv-text);">
                    {{ block.settings.review_text }}
                  </div>
                {%- endif -%}
              </div>
            </div>
          </div>
        </div>

      {%- when 'guarantee_box' -%}
        <style>
          #guarantee-{{ block.id }} .pdp-guarantee__icon-img { width: var(--g-icon-w); height: auto; }
        </style>
        <div id="guarantee-{{ block.id }}" class="pdp-guarantee" style="--g-bg: {{ block.settings.bg }}; --g-border: {{ block.settings.border }}; --g-color: {{ block.settings.color }}; --g-icon-w: {{ block.settings.icon_width | default: 28 }}px;" {{ block.shopify_attributes }}>
          {%- if block.settings.icon != blank -%}
            <div class="pdp-guarantee__icon">{{ block.settings.icon | image_url: width: 160 | image_tag: class: 'pdp-guarantee__icon-img', loading: 'lazy', widths: '40,80,120,160' }}</div>
          {%- endif -%}
          <div class="pdp-guarantee__content">
            {%- if block.settings.title != blank -%}<div class="pdp-guarantee__title">{{ block.settings.title }}</div>{%- endif -%}
            {%- if block.settings.text != blank -%}<div class="pdp-guarantee__text">{{ block.settings.text }}</div>{%- endif -%}
          </div>
        </div>

      {%- when 'stock_left' -%}
        <div class="stock-left" {{ block.shopify_attributes }}>
          {%- if block.settings.icon != blank -%}
            {{ block.settings.icon | image_url: width: 26 | image_tag: class: 'timer-img', loading: 'lazy', widths: '22,26', height: '22' }}
          {%- endif -%}
          <i>
            {{ block.settings.prefix | default: 'We only have' }}
            <span class="getviditors"><strong><span id="timer-{{ block.id }}" data-start="{{ block.settings.min_random | default: 4 }}" data-end="{{ block.settings.max_random | default: 8 }}"></span></strong></span>
            {{ block.settings.suffix | default: 'left in stock' }}
          </i>
        </div>
        <script>
          (function(){
            var el = document.getElementById('timer-{{ block.id }}');
            if(!el) return;
            var min = parseInt(el.getAttribute('data-start'),10)||4;
            var max = parseInt(el.getAttribute('data-end'),10)||8;
            function rand(min,max){ return Math.floor(Math.random()*(max-min+1))+min; }
            function interval(minMs,maxMs){ return Math.floor(Math.random()*(maxMs-minMs+1))+minMs; }
            function run(){
              var n = rand(min,max); el.textContent = n;
              (function tick(){
                if(n>4){ n--; el.textContent = n; setTimeout(tick, interval(45000,60000)); }
                else { setTimeout(function(){ n=rand(min,max); el.textContent=n; run(); }, 120000); }
              })();
            }
            run();
          })();
        </script>

      {%- when 'pdp_accordions' -%}
        <div class="pdp-accordions" style="--acc-icon: {{ block.settings.icon_color }}; --acc-icon-active: {{ block.settings.icon_active_color }}; --acc-border: {{ block.settings.border_color }}; --acc-border-active: {{ block.settings.border_active_color }}; --acc-bg: {{ block.settings.background_color }}; --acc-bg-active: {{ block.settings.background_active_color }}; --acc-head: {{ block.settings.heading_color }}; --acc-head-active: {{ block.settings.heading_active_color }}; --acc-body: {{ block.settings.content_color }}; --acc-body-active: {{ block.settings.content_active_color }};" {{ block.shopify_attributes }}>
          {%- for i in (1..5) -%}
            {%- assign title_key = 'title' | append: i -%}
            {%- assign content_key = 'content' | append: i -%}
            {%- assign t = block.settings[title_key] -%}
            {%- assign c = block.settings[content_key] -%}
            {%- if t != blank -%}
              <details class="pdp-acc">
                <summary class="pdp-acc__summary">{{ t }}</summary>
                <div class="pdp-acc__content rte">{{ c }}</div>
              </details>
            {%- endif -%}
          {%- endfor -%}
        </div>


      {%- when 'share_buttons' -%}
        <div class="product-form__share {% if block.settings.alignment == 'center' %}justify-self-center{% elsif block.settings.alignment == 'end' %}justify-self-end{% endif %}" {{ block.shopify_attributes }}>
          <div class="product-info__share-buttons">
            <div class="share-buttons">
              <span class="text-subdued">{{- 'general.social.share' | t -}}</span>

              <ul class="h-stack" role="list">
                <li><a href="{% render 'share-link', host: 'facebook', title: product.title, description: product.description, url: product.url %}" class="share-buttons__item" aria-label="{{ 'general.social.share_on' | t: social_media: 'Facebook' }}">{%- render 'icon' with 'facebook', width: 20, height: 20 -%}</a></li>
                <li><a href="{% render 'share-link', host: 'twitter', title: product.title, description: product.description, url: product.url %}" class="share-buttons__item" aria-label="{{ 'general.social.share_on' | t: social_media: 'Twitter' }}">{%- render 'icon' with 'twitter', width: 20, height: 20 -%}</a></li>
                <li><a href="{% render 'share-link', host: 'pinterest', title: product.title, description: product.description, url: product.url %}" class="share-buttons__item" aria-label="{{ 'general.social.share_on' | t: social_media: 'Pinterest' }}">{%- render 'icon' with 'pinterest', width: 20, height: 20 -%}</a></li>
                <li><a href="{% render 'share-link', host: 'email', title: product.title, description: product.description, url: product.url %}" class="share-buttons__item" aria-label="{{ 'general.social.share_email' | t }}">{%- render 'icon' with 'email' -%}</a></li>
              </ul>
            </div>
          </div>

          <button is="share-button" class="product-info__native-share">
            {%- render 'icon' with 'share' -%} {{- 'general.social.share' | t -}}
          </button>
        </div>
    {%- endcase -%}
  {%- endfor -%}

  {%- comment -%}
    IMPLEMENTATION NOTE: under rare circumstances, merchant may want to show selectors to allow variant selection, but hide
    the add to cart button. This is however problematic as product info is changed based on the form, so we create a default
    one if no buy buttons exists
  {%- endcomment -%}

  {%- unless main_form_exists -%}
    {%- form 'product', product, id: product_form_id, hidden: true -%}
      <input type="hidden" disabled name="id" value="{{ product.selected_or_first_available_variant.id }}">
    {%- endform -%}
  {%- endunless -%}
</safe-sticky>