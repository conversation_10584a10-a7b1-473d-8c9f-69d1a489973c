var Fi=Object.create;var Ce=Object.defineProperty;var Li=Object.getOwnPropertyDescriptor;var Ci=Object.getOwnPropertyNames;var Ri=Object.getPrototypeOf,Mi=Object.prototype.hasOwnProperty;var ki=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),zi=(e,t)=>{for(var r in t)Ce(e,r,{get:t[r],enumerable:!0})},ji=(e,t,r,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let n of Ci(t))!Mi.call(e,n)&&n!==r&&Ce(e,n,{get:()=>t[n],enumerable:!(i=Li(t,n))||i.enumerable});return e};var $i=(e,t,r)=>(r=e!=null?Fi(Ri(e)):{},ji(t||!e||!e.__esModule?Ce(r,"default",{value:e,enumerable:!0}):r,e));var Rr=ki(()=>{(function(){"use strict";var e=function(h,c){var p=function(N){for(var x=0,A=N.length;x<A;x++)w(N[x])},w=function(N){var x=N.target,A=N.attributeName,K=N.oldValue;x.attributeChangedCallback(A,K,x.getAttribute(A))};return function(E,N){var x=E.constructor.observedAttributes;return x&&h(N).then(function(){new c(p).observe(E,{attributes:!0,attributeOldValue:!0,attributeFilter:x});for(var A=0,K=x.length;A<K;A++)E.hasAttribute(x[A])&&w({target:E,attributeName:x[A],oldValue:null})}),E}};function t(h,c){if(h){if(typeof h=="string")return r(h,c);var p=Object.prototype.toString.call(h).slice(8,-1);if(p==="Object"&&h.constructor&&(p=h.constructor.name),p==="Map"||p==="Set")return Array.from(h);if(p==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(p))return r(h,c)}}function r(h,c){(c==null||c>h.length)&&(c=h.length);for(var p=0,w=new Array(c);p<c;p++)w[p]=h[p];return w}function i(h,c){var p=typeof Symbol<"u"&&h[Symbol.iterator]||h["@@iterator"];if(!p){if(Array.isArray(h)||(p=t(h))||c&&h&&typeof h.length=="number"){p&&(h=p);var w=0,E=function(){};return{s:E,n:function(){return w>=h.length?{done:!0}:{done:!1,value:h[w++]}},e:function(K){throw K},f:E}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var N=!0,x=!1,A;return{s:function(){p=p.call(h)},n:function(){var K=p.next();return N=K.done,K},e:function(K){x=!0,A=K},f:function(){try{!N&&p.return!=null&&p.return()}finally{if(x)throw A}}}}var n=!0,s=!1,o="querySelectorAll",u=function(c){var p=arguments.length>1&&arguments[1]!==void 0?arguments[1]:document,w=arguments.length>2&&arguments[2]!==void 0?arguments[2]:MutationObserver,E=arguments.length>3&&arguments[3]!==void 0?arguments[3]:["*"],N=function K(pt,mt,tt,O,j,U){var et=i(pt),Dt;try{for(et.s();!(Dt=et.n()).done;){var Y=Dt.value;(U||o in Y)&&(j?tt.has(Y)||(tt.add(Y),O.delete(Y),c(Y,j)):O.has(Y)||(O.add(Y),tt.delete(Y),c(Y,j)),U||K(Y[o](mt),mt,tt,O,j,n))}}catch(Le){et.e(Le)}finally{et.f()}},x=new w(function(K){if(E.length){var pt=E.join(","),mt=new Set,tt=new Set,O=i(K),j;try{for(O.s();!(j=O.n()).done;){var U=j.value,et=U.addedNodes,Dt=U.removedNodes;N(Dt,pt,mt,tt,s,s),N(et,pt,mt,tt,n,s)}}catch(Y){O.e(Y)}finally{O.f()}}}),A=x.observe;return(x.observe=function(K){return A.call(x,K,{subtree:n,childList:n})})(p),x},d="querySelectorAll",f=self,y=f.document,g=f.Element,m=f.MutationObserver,L=f.Set,I=f.WeakMap,M=function(c){return d in c},k=[].filter,R=function(h){var c=new I,p=function(O){for(var j=0,U=O.length;j<U;j++)c.delete(O[j])},w=function(){for(var O=pt.takeRecords(),j=0,U=O.length;j<U;j++)x(k.call(O[j].removedNodes,M),!1),x(k.call(O[j].addedNodes,M),!0)},E=function(O){return O.matches||O.webkitMatchesSelector||O.msMatchesSelector},N=function(O,j){var U;if(j)for(var et,Dt=E(O),Y=0,Le=A.length;Y<Le;Y++)Dt.call(O,et=A[Y])&&(c.has(O)||c.set(O,new L),U=c.get(O),U.has(et)||(U.add(et),h.handle(O,j,et)));else c.has(O)&&(U=c.get(O),c.delete(O),U.forEach(function(Pi){h.handle(O,j,Pi)}))},x=function(O){for(var j=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,U=0,et=O.length;U<et;U++)N(O[U],j)},A=h.query,K=h.root||y,pt=u(N,K,m,A),mt=g.prototype.attachShadow;return mt&&(g.prototype.attachShadow=function(tt){var O=mt.call(this,tt);return pt.observe(O),O}),A.length&&x(K[d](A)),{drop:p,flush:w,observer:pt,parse:x}},P=self,F=P.document,z=P.Map,$=P.MutationObserver,B=P.Object,X=P.Set,q=P.WeakMap,_=P.Element,J=P.HTMLElement,b=P.Node,l=P.Error,a=P.TypeError,v=P.Reflect,S=B.defineProperty,T=B.keys,D=B.getOwnPropertyNames,C=B.setPrototypeOf,W=!self.customElements,H=function(c){for(var p=T(c),w=[],E=new X,N=p.length,x=0;x<N;x++){w[x]=c[p[x]];try{delete c[p[x]]}catch{E.add(x)}}return function(){for(var A=0;A<N;A++)E.has(A)||(c[p[A]]=w[A])}};if(W){var nt=function(){var c=this.constructor;if(!rt.has(c))throw new a("Illegal constructor");var p=rt.get(c);if(Qt)return ir(Qt,p);var w=ht.call(F,p);return ir(C(w,c.prototype),p)},ht=F.createElement,rt=new z,st=new z,Lt=new z,ct=new z,Nt=[],gi=function(c,p,w){var E=Lt.get(w);if(p&&!E.isPrototypeOf(c)){var N=H(c);Qt=C(c,E);try{new E.constructor}finally{Qt=null,N()}}var x="".concat(p?"":"dis","connectedCallback");x in E&&c[x]()},yi=R({query:Nt,handle:gi}),bi=yi.parse,Qt=null,_e=function(c){if(!st.has(c)){var p,w=new Promise(function(E){p=E});st.set(c,{$:w,_:p})}return st.get(c).$},ir=e(_e,$);self.customElements={define:function(c,p){if(ct.has(c))throw new l('the name "'.concat(c,'" has already been used with this registry'));rt.set(p,c),Lt.set(c,p.prototype),ct.set(c,p),Nt.push(c),_e(c).then(function(){bi(F.querySelectorAll(c))}),st.get(c)._(p)},get:function(c){return ct.get(c)},whenDefined:_e},S(nt.prototype=J.prototype,"constructor",{value:nt}),self.HTMLElement=nt,F.createElement=function(h,c){var p=c&&c.is,w=p?ct.get(p):ct.get(h);return w?new w:ht.call(F,h)},"isConnected"in b.prototype||S(b.prototype,"isConnected",{configurable:!0,get:function(){return!(this.ownerDocument.compareDocumentPosition(this)&this.DOCUMENT_POSITION_DISCONNECTED)}})}else if(W=!self.customElements.get("extends-br"),W)try{var nr=function h(){return self.Reflect.construct(HTMLBRElement,[],h)};nr.prototype=HTMLLIElement.prototype;var or="extends-br";self.customElements.define("extends-br",nr,{extends:"br"}),W=F.createElement("br",{is:or}).outerHTML.indexOf(or)<0;var sr=self.customElements,wi=sr.get,Ei=sr.whenDefined;self.customElements.whenDefined=function(h){var c=this;return Ei.call(this,h).then(function(p){return p||wi.call(c,h)})}}catch{}if(W){var ar=function(c){var p=Ne.get(c);dr(p.querySelectorAll(this),c.isConnected)},at=self.customElements,lr=F.createElement,xi=at.define,Si=at.get,Oi=at.upgrade,Ti=v||{construct:function(c){return c.call(this)}},Ai=Ti.construct,Ne=new q,De=new X,Jt=new z,te=new z,cr=new z,ee=new z,ur=[],re=[],fr=function(c){return ee.get(c)||Si.call(at,c)},Ii=function(c,p,w){var E=cr.get(w);if(p&&!E.isPrototypeOf(c)){var N=H(c);ie=C(c,E);try{new E.constructor}finally{ie=null,N()}}var x="".concat(p?"":"dis","connectedCallback");x in E&&c[x]()},_i=R({query:re,handle:Ii}),dr=_i.parse,Ni=R({query:ur,handle:function(c,p){Ne.has(c)&&(p?De.add(c):De.delete(c),re.length&&ar.call(re,c))}}),Di=Ni.parse,hr=_.prototype.attachShadow;hr&&(_.prototype.attachShadow=function(h){var c=hr.call(this,h);return Ne.set(this,c),c});var Pe=function(c){if(!te.has(c)){var p,w=new Promise(function(E){p=E});te.set(c,{$:w,_:p})}return te.get(c).$},Fe=e(Pe,$),ie=null;D(self).filter(function(h){return/^HTML.*Element$/.test(h)}).forEach(function(h){var c=self[h];function p(){var w=this.constructor;if(!Jt.has(w))throw new a("Illegal constructor");var E=Jt.get(w),N=E.is,x=E.tag;if(N){if(ie)return Fe(ie,N);var A=lr.call(F,x);return A.setAttribute("is",N),Fe(C(A,w.prototype),N)}else return Ai.call(this,c,[],w)}S(p.prototype=c.prototype,"constructor",{value:p}),S(self,h,{value:p})}),F.createElement=function(h,c){var p=c&&c.is;if(p){var w=ee.get(p);if(w&&Jt.get(w).tag===h)return new w}var E=lr.call(F,h);return p&&E.setAttribute("is",p),E},at.get=fr,at.whenDefined=Pe,at.upgrade=function(h){var c=h.getAttribute("is");if(c){var p=ee.get(c);if(p){Fe(C(h,p.prototype),c);return}}Oi.call(at,h)},at.define=function(h,c,p){if(fr(h))throw new l("'".concat(h,"' has already been defined as a custom element"));var w,E=p&&p.extends;Jt.set(c,E?{is:h,tag:E}:{is:"",tag:h}),E?(w="".concat(E,'[is="').concat(h,'"]'),cr.set(w,c.prototype),ee.set(h,c),re.push(w)):(xi.apply(at,arguments),ur.push(w=h)),Pe(h).then(function(){E?(dr(F.querySelectorAll(w)),De.forEach(ar,[w])):Di(F.querySelectorAll(w))}),te.get(h)._(c)}}})()});var ne=null,mr,vr,gr,yr=65,Re,Ct,pr=new Set,br=1111;Wi();function Wi(){if(!document.createElement("link").relList.supports("prefetch"))return;let t="instantVaryAccept"in document.body.dataset||"Shopify"in window,r=navigator.userAgent.indexOf("Chrome/");if(r>-1&&(ne=parseInt(navigator.userAgent.substring(r+7))),t&&ne&&ne<110)return;let i="instantMousedownShortcut"in document.body.dataset;mr="instantAllowQueryString"in document.body.dataset,vr="instantAllowExternalLinks"in document.body.dataset,gr="instantWhitelist"in document.body.dataset;let n={capture:!0,passive:!0},s=!1,o=!1,u=!1;if("instantIntensity"in document.body.dataset){let d=document.body.dataset.instantIntensity;if(d.startsWith("mousedown"))s=!0,d=="mousedown-only"&&(o=!0);else if(d.startsWith("viewport")){let f=navigator.connection&&navigator.connection.saveData,y=navigator.connection&&navigator.connection.effectiveType&&navigator.connection.effectiveType.includes("2g");!f&&!y&&(d=="viewport"?document.documentElement.clientWidth*document.documentElement.clientHeight<45e4&&(u=!0):d=="viewport-all"&&(u=!0))}else{let f=parseInt(d);isNaN(f)||(yr=f)}}if(o||document.addEventListener("touchstart",Vi,n),s?i||document.addEventListener("mousedown",Hi,n):document.addEventListener("mouseover",Bi,n),i&&document.addEventListener("mousedown",Ui,n),u){let d=window.requestIdleCallback;d||(d=f=>{f()}),d(function(){let y=new IntersectionObserver(g=>{g.forEach(m=>{if(m.isIntersecting){let L=m.target;y.unobserve(L),se(L.href)}})});document.querySelectorAll("a").forEach(g=>{oe(g)&&y.observe(g)})},{timeout:1500})}}function Vi(e){Re=performance.now();let t=e.target.closest("a");oe(t)&&se(t.href,"high")}function Bi(e){if(performance.now()-Re<br||!("closest"in e.target))return;let t=e.target.closest("a");oe(t)&&(t.addEventListener("mouseout",Ki,{passive:!0}),Ct=setTimeout(()=>{se(t.href,"high"),Ct=void 0},yr))}function Hi(e){let t=e.target.closest("a");oe(t)&&se(t.href,"high")}function Ki(e){e.relatedTarget&&e.target.closest("a")==e.relatedTarget.closest("a")||Ct&&(clearTimeout(Ct),Ct=void 0)}function Ui(e){if(performance.now()-Re<br)return;let t=e.target.closest("a");if(e.which>1||e.metaKey||e.ctrlKey||!t)return;t.addEventListener("click",function(i){i.detail!=1337&&i.preventDefault()},{capture:!0,passive:!1,once:!0});let r=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!1,detail:1337});t.dispatchEvent(r)}function oe(e){if(!(!e||!e.href)&&!(gr&&!("instant"in e.dataset))&&!(e.origin!=location.origin&&(!(vr||"instant"in e.dataset)||!ne))&&["http:","https:"].includes(e.protocol)&&!(e.protocol=="http:"&&location.protocol=="https:")&&!(!mr&&e.search&&!("instant"in e.dataset))&&!(e.hash&&e.pathname+e.search==location.pathname+location.search)&&!("noInstant"in e.dataset))return!0}function se(e,t="auto"){if(pr.has(e))return;let r=document.createElement("link");r.rel="prefetch",r.href=e,r.fetchPriority=t,r.as="document",document.head.appendChild(r),pr.add(e)}var Cr={};zi(Cr,{createFocusTrap:()=>gn});var Er=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],ae=Er.join(","),xr=typeof Element>"u",St=xr?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,le=!xr&&Element.prototype.getRootNode?function(e){var t;return e==null||(t=e.getRootNode)===null||t===void 0?void 0:t.call(e)}:function(e){return e?.ownerDocument},ce=function e(t,r){var i;r===void 0&&(r=!0);var n=t==null||(i=t.getAttribute)===null||i===void 0?void 0:i.call(t,"inert"),s=n===""||n==="true",o=s||r&&t&&e(t.parentNode);return o},qi=function(t){var r,i=t==null||(r=t.getAttribute)===null||r===void 0?void 0:r.call(t,"contenteditable");return i===""||i==="true"},Sr=function(t,r,i){if(ce(t))return[];var n=Array.prototype.slice.apply(t.querySelectorAll(ae));return r&&St.call(t,ae)&&n.unshift(t),n=n.filter(i),n},Or=function e(t,r,i){for(var n=[],s=Array.from(t);s.length;){var o=s.shift();if(!ce(o,!1))if(o.tagName==="SLOT"){var u=o.assignedElements(),d=u.length?u:o.children,f=e(d,!0,i);i.flatten?n.push.apply(n,f):n.push({scopeParent:o,candidates:f})}else{var y=St.call(o,ae);y&&i.filter(o)&&(r||!t.includes(o))&&n.push(o);var g=o.shadowRoot||typeof i.getShadowRoot=="function"&&i.getShadowRoot(o),m=!ce(g,!1)&&(!i.shadowRootFilter||i.shadowRootFilter(o));if(g&&m){var L=e(g===!0?o.children:g.children,!0,i);i.flatten?n.push.apply(n,L):n.push({scopeParent:o,candidates:L})}else s.unshift.apply(s,o.children)}}return n},Tr=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},vt=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||qi(t))&&!Tr(t)?0:t.tabIndex},Gi=function(t,r){var i=vt(t);return i<0&&r&&!Tr(t)?0:i},Zi=function(t,r){return t.tabIndex===r.tabIndex?t.documentOrder-r.documentOrder:t.tabIndex-r.tabIndex},Ar=function(t){return t.tagName==="INPUT"},Xi=function(t){return Ar(t)&&t.type==="hidden"},Yi=function(t){var r=t.tagName==="DETAILS"&&Array.prototype.slice.apply(t.children).some(function(i){return i.tagName==="SUMMARY"});return r},Qi=function(t,r){for(var i=0;i<t.length;i++)if(t[i].checked&&t[i].form===r)return t[i]},Ji=function(t){if(!t.name)return!0;var r=t.form||le(t),i=function(u){return r.querySelectorAll('input[type="radio"][name="'+u+'"]')},n;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")n=i(window.CSS.escape(t.name));else try{n=i(t.name)}catch(o){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",o.message),!1}var s=Qi(n,t.form);return!s||s===t},tn=function(t){return Ar(t)&&t.type==="radio"},en=function(t){return tn(t)&&!Ji(t)},rn=function(t){var r,i=t&&le(t),n=(r=i)===null||r===void 0?void 0:r.host,s=!1;if(i&&i!==t){var o,u,d;for(s=!!((o=n)!==null&&o!==void 0&&(u=o.ownerDocument)!==null&&u!==void 0&&u.contains(n)||t!=null&&(d=t.ownerDocument)!==null&&d!==void 0&&d.contains(t));!s&&n;){var f,y,g;i=le(n),n=(f=i)===null||f===void 0?void 0:f.host,s=!!((y=n)!==null&&y!==void 0&&(g=y.ownerDocument)!==null&&g!==void 0&&g.contains(n))}}return s},wr=function(t){var r=t.getBoundingClientRect(),i=r.width,n=r.height;return i===0&&n===0},nn=function(t,r){var i=r.displayCheck,n=r.getShadowRoot;if(getComputedStyle(t).visibility==="hidden")return!0;var s=St.call(t,"details>summary:first-of-type"),o=s?t.parentElement:t;if(St.call(o,"details:not([open]) *"))return!0;if(!i||i==="full"||i==="legacy-full"){if(typeof n=="function"){for(var u=t;t;){var d=t.parentElement,f=le(t);if(d&&!d.shadowRoot&&n(d)===!0)return wr(t);t.assignedSlot?t=t.assignedSlot:!d&&f!==t.ownerDocument?t=f.host:t=d}t=u}if(rn(t))return!t.getClientRects().length;if(i!=="legacy-full")return!0}else if(i==="non-zero-area")return wr(t);return!1},on=function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var r=t.parentElement;r;){if(r.tagName==="FIELDSET"&&r.disabled){for(var i=0;i<r.children.length;i++){var n=r.children.item(i);if(n.tagName==="LEGEND")return St.call(r,"fieldset[disabled] *")?!0:!n.contains(t)}return!0}r=r.parentElement}return!1},ue=function(t,r){return!(r.disabled||ce(r)||Xi(r)||nn(r,t)||Yi(r)||on(r))},Me=function(t,r){return!(en(r)||vt(r)<0||!ue(t,r))},sn=function(t){var r=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(r)||r>=0)},an=function e(t){var r=[],i=[];return t.forEach(function(n,s){var o=!!n.scopeParent,u=o?n.scopeParent:n,d=Gi(u,o),f=o?e(n.candidates):u;d===0?o?r.push.apply(r,f):r.push(u):i.push({documentOrder:s,tabIndex:d,item:n,isScope:o,content:f})}),i.sort(Zi).reduce(function(n,s){return s.isScope?n.push.apply(n,s.content):n.push(s.content),n},[]).concat(r)},Ir=function(t,r){r=r||{};var i;return r.getShadowRoot?i=Or([t],r.includeContainer,{filter:Me.bind(null,r),flatten:!1,getShadowRoot:r.getShadowRoot,shadowRootFilter:sn}):i=Sr(t,r.includeContainer,Me.bind(null,r)),an(i)},_r=function(t,r){r=r||{};var i;return r.getShadowRoot?i=Or([t],r.includeContainer,{filter:ue.bind(null,r),flatten:!0,getShadowRoot:r.getShadowRoot}):i=Sr(t,r.includeContainer,ue.bind(null,r)),i},Ot=function(t,r){if(r=r||{},!t)throw new Error("No node provided");return St.call(t,ae)===!1?!1:Me(r,t)},ln=Er.concat("iframe").join(","),fe=function(t,r){if(r=r||{},!t)throw new Error("No node provided");return St.call(t,ln)===!1?!1:ue(r,t)};function Nr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),r.push.apply(r,i)}return r}function Dr(e){for(var t=1;t<arguments.length;t++){var r=arguments[t]!=null?arguments[t]:{};t%2?Nr(Object(r),!0).forEach(function(i){cn(e,i,r[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nr(Object(r)).forEach(function(i){Object.defineProperty(e,i,Object.getOwnPropertyDescriptor(r,i))})}return e}function cn(e,t,r){return t=fn(t),t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function un(e,t){if(typeof e!="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==void 0){var i=r.call(e,t||"default");if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function fn(e){var t=un(e,"string");return typeof t=="symbol"?t:String(t)}var Pr={activateTrap:function(t,r){if(t.length>0){var i=t[t.length-1];i!==r&&i.pause()}var n=t.indexOf(r);n===-1||t.splice(n,1),t.push(r)},deactivateTrap:function(t,r){var i=t.indexOf(r);i!==-1&&t.splice(i,1),t.length>0&&t[t.length-1].unpause()}},dn=function(t){return t.tagName&&t.tagName.toLowerCase()==="input"&&typeof t.select=="function"},hn=function(t){return t?.key==="Escape"||t?.key==="Esc"||t?.keyCode===27},Mt=function(t){return t?.key==="Tab"||t?.keyCode===9},pn=function(t){return Mt(t)&&!t.shiftKey},mn=function(t){return Mt(t)&&t.shiftKey},Fr=function(t){return setTimeout(t,0)},Lr=function(t,r){var i=-1;return t.every(function(n,s){return r(n)?(i=s,!1):!0}),i},Rt=function(t){for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];return typeof t=="function"?t.apply(void 0,i):t},de=function(t){return t.target.shadowRoot&&typeof t.composedPath=="function"?t.composedPath()[0]:t.target},vn=[],gn=function(t,r){var i=r?.document||document,n=r?.trapStack||vn,s=Dr({returnFocusOnDeactivate:!0,escapeDeactivates:!0,delayInitialFocus:!0,isKeyForward:pn,isKeyBackward:mn},r),o={containers:[],containerGroups:[],tabbableGroups:[],nodeFocusedBeforeActivation:null,mostRecentlyFocusedNode:null,active:!1,paused:!1,delayInitialFocusTimer:void 0,recentNavEvent:void 0},u,d=function(l,a,v){return l&&l[a]!==void 0?l[a]:s[v||a]},f=function(l,a){var v=typeof a?.composedPath=="function"?a.composedPath():void 0;return o.containerGroups.findIndex(function(S){var T=S.container,D=S.tabbableNodes;return T.contains(l)||v?.includes(T)||D.find(function(C){return C===l})})},y=function(l){var a=s[l];if(typeof a=="function"){for(var v=arguments.length,S=new Array(v>1?v-1:0),T=1;T<v;T++)S[T-1]=arguments[T];a=a.apply(void 0,S)}if(a===!0&&(a=void 0),!a){if(a===void 0||a===!1)return a;throw new Error("`".concat(l,"` was specified but was not a node, or did not return a node"))}var D=a;if(typeof a=="string"&&(D=i.querySelector(a),!D))throw new Error("`".concat(l,"` as selector refers to no known node"));return D},g=function(){var l=y("initialFocus");if(l===!1)return!1;if(l===void 0||!fe(l,s.tabbableOptions))if(f(i.activeElement)>=0)l=i.activeElement;else{var a=o.tabbableGroups[0],v=a&&a.firstTabbableNode;l=v||y("fallbackFocus")}if(!l)throw new Error("Your focus-trap needs to have at least one focusable element");return l},m=function(){if(o.containerGroups=o.containers.map(function(l){var a=Ir(l,s.tabbableOptions),v=_r(l,s.tabbableOptions),S=a.length>0?a[0]:void 0,T=a.length>0?a[a.length-1]:void 0,D=v.find(function(H){return Ot(H)}),C=v.slice().reverse().find(function(H){return Ot(H)}),W=!!a.find(function(H){return vt(H)>0});return{container:l,tabbableNodes:a,focusableNodes:v,posTabIndexesFound:W,firstTabbableNode:S,lastTabbableNode:T,firstDomTabbableNode:D,lastDomTabbableNode:C,nextTabbableNode:function(nt){var ht=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,rt=a.indexOf(nt);return rt<0?ht?v.slice(v.indexOf(nt)+1).find(function(st){return Ot(st)}):v.slice(0,v.indexOf(nt)).reverse().find(function(st){return Ot(st)}):a[rt+(ht?1:-1)]}}}),o.tabbableGroups=o.containerGroups.filter(function(l){return l.tabbableNodes.length>0}),o.tabbableGroups.length<=0&&!y("fallbackFocus"))throw new Error("Your focus-trap must have at least one container with at least one tabbable node in it at all times");if(o.containerGroups.find(function(l){return l.posTabIndexesFound})&&o.containerGroups.length>1)throw new Error("At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.")},L=function b(l){var a=l.activeElement;if(a)return a.shadowRoot&&a.shadowRoot.activeElement!==null?b(a.shadowRoot):a},I=function b(l){if(l!==!1&&l!==L(document)){if(!l||!l.focus){b(g());return}l.focus({preventScroll:!!s.preventScroll}),o.mostRecentlyFocusedNode=l,dn(l)&&l.select()}},M=function(l){var a=y("setReturnFocus",l);return a||(a===!1?!1:l)},k=function(l){var a=l.target,v=l.event,S=l.isBackward,T=S===void 0?!1:S;a=a||de(v),m();var D=null;if(o.tabbableGroups.length>0){var C=f(a,v),W=C>=0?o.containerGroups[C]:void 0;if(C<0)T?D=o.tabbableGroups[o.tabbableGroups.length-1].lastTabbableNode:D=o.tabbableGroups[0].firstTabbableNode;else if(T){var H=Lr(o.tabbableGroups,function(ct){var Nt=ct.firstTabbableNode;return a===Nt});if(H<0&&(W.container===a||fe(a,s.tabbableOptions)&&!Ot(a,s.tabbableOptions)&&!W.nextTabbableNode(a,!1))&&(H=C),H>=0){var nt=H===0?o.tabbableGroups.length-1:H-1,ht=o.tabbableGroups[nt];D=vt(a)>=0?ht.lastTabbableNode:ht.lastDomTabbableNode}else Mt(v)||(D=W.nextTabbableNode(a,!1))}else{var rt=Lr(o.tabbableGroups,function(ct){var Nt=ct.lastTabbableNode;return a===Nt});if(rt<0&&(W.container===a||fe(a,s.tabbableOptions)&&!Ot(a,s.tabbableOptions)&&!W.nextTabbableNode(a))&&(rt=C),rt>=0){var st=rt===o.tabbableGroups.length-1?0:rt+1,Lt=o.tabbableGroups[st];D=vt(a)>=0?Lt.firstTabbableNode:Lt.firstDomTabbableNode}else Mt(v)||(D=W.nextTabbableNode(a))}}else D=y("fallbackFocus");return D},R=function(l){var a=de(l);if(!(f(a,l)>=0)){if(Rt(s.clickOutsideDeactivates,l)){u.deactivate({returnFocus:s.returnFocusOnDeactivate});return}Rt(s.allowOutsideClick,l)||l.preventDefault()}},P=function(l){var a=de(l),v=f(a,l)>=0;if(v||a instanceof Document)v&&(o.mostRecentlyFocusedNode=a);else{l.stopImmediatePropagation();var S,T=!0;if(o.mostRecentlyFocusedNode)if(vt(o.mostRecentlyFocusedNode)>0){var D=f(o.mostRecentlyFocusedNode),C=o.containerGroups[D].tabbableNodes;if(C.length>0){var W=C.findIndex(function(H){return H===o.mostRecentlyFocusedNode});W>=0&&(s.isKeyForward(o.recentNavEvent)?W+1<C.length&&(S=C[W+1],T=!1):W-1>=0&&(S=C[W-1],T=!1))}}else o.containerGroups.some(function(H){return H.tabbableNodes.some(function(nt){return vt(nt)>0})})||(T=!1);else T=!1;T&&(S=k({target:o.mostRecentlyFocusedNode,isBackward:s.isKeyBackward(o.recentNavEvent)})),I(S||o.mostRecentlyFocusedNode||g())}o.recentNavEvent=void 0},F=function(l){var a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;o.recentNavEvent=l;var v=k({event:l,isBackward:a});v&&(Mt(l)&&l.preventDefault(),I(v))},z=function(l){if(hn(l)&&Rt(s.escapeDeactivates,l)!==!1){l.preventDefault(),u.deactivate();return}(s.isKeyForward(l)||s.isKeyBackward(l))&&F(l,s.isKeyBackward(l))},$=function(l){var a=de(l);f(a,l)>=0||Rt(s.clickOutsideDeactivates,l)||Rt(s.allowOutsideClick,l)||(l.preventDefault(),l.stopImmediatePropagation())},B=function(){if(o.active)return Pr.activateTrap(n,u),o.delayInitialFocusTimer=s.delayInitialFocus?Fr(function(){I(g())}):I(g()),i.addEventListener("focusin",P,!0),i.addEventListener("mousedown",R,{capture:!0,passive:!1}),i.addEventListener("touchstart",R,{capture:!0,passive:!1}),i.addEventListener("click",$,{capture:!0,passive:!1}),i.addEventListener("keydown",z,{capture:!0,passive:!1}),u},X=function(){if(o.active)return i.removeEventListener("focusin",P,!0),i.removeEventListener("mousedown",R,!0),i.removeEventListener("touchstart",R,!0),i.removeEventListener("click",$,!0),i.removeEventListener("keydown",z,!0),u},q=function(l){var a=l.some(function(v){var S=Array.from(v.removedNodes);return S.some(function(T){return T===o.mostRecentlyFocusedNode})});a&&I(g())},_=typeof window<"u"&&"MutationObserver"in window?new MutationObserver(q):void 0,J=function(){_&&(_.disconnect(),o.active&&!o.paused&&o.containers.map(function(l){_.observe(l,{subtree:!0,childList:!0})}))};return u={get active(){return o.active},get paused(){return o.paused},activate:function(l){if(o.active)return this;var a=d(l,"onActivate"),v=d(l,"onPostActivate"),S=d(l,"checkCanFocusTrap");S||m(),o.active=!0,o.paused=!1,o.nodeFocusedBeforeActivation=i.activeElement,a?.();var T=function(){S&&m(),B(),J(),v?.()};return S?(S(o.containers.concat()).then(T,T),this):(T(),this)},deactivate:function(l){if(!o.active)return this;var a=Dr({onDeactivate:s.onDeactivate,onPostDeactivate:s.onPostDeactivate,checkCanReturnFocus:s.checkCanReturnFocus},l);clearTimeout(o.delayInitialFocusTimer),o.delayInitialFocusTimer=void 0,X(),o.active=!1,o.paused=!1,J(),Pr.deactivateTrap(n,u);var v=d(a,"onDeactivate"),S=d(a,"onPostDeactivate"),T=d(a,"checkCanReturnFocus"),D=d(a,"returnFocus","returnFocusOnDeactivate");v?.();var C=function(){Fr(function(){D&&I(M(o.nodeFocusedBeforeActivation)),S?.()})};return D&&T?(T(M(o.nodeFocusedBeforeActivation)).then(C,C),this):(C(),this)},pause:function(l){if(o.paused||!o.active)return this;var a=d(l,"onPause"),v=d(l,"onPostPause");return o.paused=!0,a?.(),X(),J(),v?.(),this},unpause:function(l){if(!o.paused||!o.active)return this;var a=d(l,"onUnpause"),v=d(l,"onPostUnpause");return o.paused=!1,a?.(),m(),B(),J(),v?.(),this},updateContainerElements:function(l){var a=[].concat(l).filter(Boolean);return o.containers=a.map(function(v){return typeof v=="string"?i.querySelector(v):v}),o.active&&m(),J(),this}},u.updateContainerElements(t),u};var ql=$i(Rr());function ke(e,t){e.indexOf(t)===-1&&e.push(t)}function ze(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}var kt=(e,t,r)=>Math.min(Math.max(r,e),t);var V={duration:.3,delay:0,endDelay:0,repeat:0,easing:"ease"};var Q=e=>typeof e=="number";var lt=e=>Array.isArray(e)&&!Q(e[0]);var Mr=(e,t,r)=>{let i=t-e;return((r-e)%i+i)%i+e};function zt(e,t){return lt(e)?e[Mr(0,e.length,t)]:e}var Tt=(e,t,r)=>-r*e+r*t+e;var jt=()=>{},Z=e=>e;var ot=(e,t,r)=>t-e===0?1:(r-e)/(t-e);function Pt(e,t){let r=e[e.length-1];for(let i=1;i<=t;i++){let n=ot(0,t,i);e.push(Tt(r,1,n))}}function At(e){let t=[0];return Pt(t,e-1),t}function $t(e,t=At(e.length),r=Z){let i=e.length,n=i-t.length;return n>0&&Pt(t,n),s=>{let o=0;for(;o<i-2&&!(s<t[o+1]);o++);let u=kt(0,1,ot(t[o],t[o+1],s));return u=zt(r,o)(u),Tt(e[o],e[o+1],u)}}var Wt=e=>Array.isArray(e)&&Q(e[0]);var gt=e=>typeof e=="object"&&!!e.createAnimation;var G=e=>typeof e=="function";var ut=e=>typeof e=="string";var yt={ms:e=>e*1e3,s:e=>e/1e3};function je(e,t){return t?e*(1e3/t):0}var kr=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e,yn=1e-7,bn=12;function wn(e,t,r,i,n){let s,o,u=0;do o=t+(r-t)/2,s=kr(o,i,n)-e,s>0?r=o:t=o;while(Math.abs(s)>yn&&++u<bn);return o}function It(e,t,r,i){if(e===t&&r===i)return Z;let n=s=>wn(s,0,1,e,r);return s=>s===0||s===1?s:kr(n(s),t,i)}var $e=(e,t="end")=>r=>{r=t==="end"?Math.min(r,.999):Math.max(r,.001);let i=r*e,n=t==="end"?Math.floor(i):Math.ceil(i);return kt(0,1,n/e)};var zr={ease:It(.25,.1,.25,1),"ease-in":It(.42,0,1,1),"ease-in-out":It(.42,0,.58,1),"ease-out":It(0,0,.58,1)},En=/\((.*?)\)/;function Ft(e){if(G(e))return e;if(Wt(e))return It(...e);if(zr[e])return zr[e];if(e.startsWith("steps")){let t=En.exec(e);if(t){let r=t[1].split(",");return $e(parseFloat(r[0]),r[1].trim())}}return Z}var ft=class{constructor(t,r=[0,1],{easing:i,duration:n=V.duration,delay:s=V.delay,endDelay:o=V.endDelay,repeat:u=V.repeat,offset:d,direction:f="normal",autoplay:y=!0}={}){if(this.startTime=null,this.rate=1,this.t=0,this.cancelTimestamp=null,this.easing=Z,this.duration=0,this.totalDuration=0,this.repeat=0,this.playState="idle",this.finished=new Promise((m,L)=>{this.resolve=m,this.reject=L}),i=i||V.easing,gt(i)){let m=i.createAnimation(r);i=m.easing,r=m.keyframes||r,n=m.duration||n}this.repeat=u,this.easing=lt(i)?Z:Ft(i),this.updateDuration(n);let g=$t(r,d,lt(i)?i.map(Ft):Z);this.tick=m=>{var L;s=s;let I=0;this.pauseTime!==void 0?I=this.pauseTime:I=(m-this.startTime)*this.rate,this.t=I,I/=1e3,I=Math.max(I-s,0),this.playState==="finished"&&this.pauseTime===void 0&&(I=this.totalDuration);let M=I/this.duration,k=Math.floor(M),R=M%1;!R&&M>=1&&(R=1),R===1&&k--;let P=k%2;(f==="reverse"||f==="alternate"&&P||f==="alternate-reverse"&&!P)&&(R=1-R);let F=I>=this.totalDuration?1:Math.min(R,1),z=g(this.easing(F));t(z),this.pauseTime===void 0&&(this.playState==="finished"||I>=this.totalDuration+o)?(this.playState="finished",(L=this.resolve)===null||L===void 0||L.call(this,z)):this.playState!=="idle"&&(this.frameRequestId=requestAnimationFrame(this.tick))},y&&this.play()}play(){let t=performance.now();this.playState="running",this.pauseTime!==void 0?this.startTime=t-this.pauseTime:this.startTime||(this.startTime=t),this.cancelTimestamp=this.startTime,this.pauseTime=void 0,this.frameRequestId=requestAnimationFrame(this.tick)}pause(){this.playState="paused",this.pauseTime=this.t}finish(){this.playState="finished",this.tick(0)}stop(){var t;this.playState="idle",this.frameRequestId!==void 0&&cancelAnimationFrame(this.frameRequestId),(t=this.reject)===null||t===void 0||t.call(this,!1)}cancel(){this.stop(),this.tick(this.cancelTimestamp)}reverse(){this.rate*=-1}commitStyles(){}updateDuration(t){this.duration=t,this.totalDuration=t*(this.repeat+1)}get currentTime(){return this.t}set currentTime(t){this.pauseTime!==void 0||this.rate===0?this.pauseTime=t:this.startTime=performance.now()-t/this.rate}get playbackRate(){return this.rate}set playbackRate(t){this.rate=t}};var Vt=function(){};var Bt=class{setAnimation(t){this.animation=t,t?.finished.then(()=>this.clearAnimation()).catch(()=>{})}clearAnimation(){this.animation=this.generator=void 0}};var We=new WeakMap;function he(e){return We.has(e)||We.set(e,{transforms:[],values:new Map}),We.get(e)}function jr(e,t){return e.has(t)||e.set(t,new Bt),e.get(t)}var xn=["","X","Y","Z"],Sn=["translate","scale","rotate","skew"],Ht={x:"translateX",y:"translateY",z:"translateZ"},$r={syntax:"<angle>",initialValue:"0deg",toDefaultUnit:e=>e+"deg"},On={translate:{syntax:"<length-percentage>",initialValue:"0px",toDefaultUnit:e=>e+"px"},rotate:$r,scale:{syntax:"<number>",initialValue:1,toDefaultUnit:Z},skew:$r},bt=new Map,me=e=>`--motion-${e}`,pe=["x","y","z"];Sn.forEach(e=>{xn.forEach(t=>{pe.push(e+t),bt.set(me(e+t),On[e])})});var Tn=(e,t)=>pe.indexOf(e)-pe.indexOf(t),An=new Set(pe),ve=e=>An.has(e),Wr=(e,t)=>{Ht[t]&&(t=Ht[t]);let{transforms:r}=he(e);ke(r,t),e.style.transform=In(r)},In=e=>e.sort(Tn).reduce(_n,"").trim(),_n=(e,t)=>`${e} ${t}(var(${me(t)}))`;var Kt=e=>e.startsWith("--"),Vr=new Set;function Br(e){if(!Vr.has(e)){Vr.add(e);try{let{syntax:t,initialValue:r}=bt.has(e)?bt.get(e):{};CSS.registerProperty({name:e,inherits:!1,syntax:t,initialValue:r})}catch{}}}var Ve=(e,t)=>document.createElement("div").animate(e,t),Hr={cssRegisterProperty:()=>typeof CSS<"u"&&Object.hasOwnProperty.call(CSS,"registerProperty"),waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate"),partialKeyframes:()=>{try{Ve({opacity:[1]})}catch{return!1}return!0},finished:()=>!!Ve({opacity:[0,1]},{duration:.001}).finished,linearEasing:()=>{try{Ve({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0}},Be={},wt={};for(let e in Hr)wt[e]=()=>(Be[e]===void 0&&(Be[e]=Hr[e]()),Be[e]);var Nn=.015,Dn=(e,t)=>{let r="",i=Math.round(t/Nn);for(let n=0;n<i;n++)r+=e(ot(0,i-1,n))+", ";return r.substring(0,r.length-2)},He=(e,t)=>G(e)?wt.linearEasing()?`linear(${Dn(e,t)})`:V.easing:Wt(e)?Pn(e):e,Pn=([e,t,r,i])=>`cubic-bezier(${e}, ${t}, ${r}, ${i})`;function Kr(e,t){for(let r=0;r<e.length;r++)e[r]===null&&(e[r]=r?e[r-1]:t());return e}var ge=e=>Array.isArray(e)?e:[e];function Ut(e){return Ht[e]&&(e=Ht[e]),ve(e)?me(e):e}var qt={get:(e,t)=>{t=Ut(t);let r=Kt(t)?e.style.getPropertyValue(t):getComputedStyle(e)[t];if(!r&&r!==0){let i=bt.get(t);i&&(r=i.initialValue)}return r},set:(e,t,r)=>{t=Ut(t),Kt(t)?e.style.setProperty(t,r):e.style[t]=r}};function ye(e,t=!0){if(!(!e||e.playState==="finished"))try{e.stop?e.stop():(t&&e.commitStyles(),e.cancel())}catch{}}function Ur(e,t){var r;let i=t?.toDefaultUnit||Z,n=e[e.length-1];if(ut(n)){let s=((r=n.match(/(-?[\d.]+)([a-z%]*)/))===null||r===void 0?void 0:r[2])||"";s&&(i=o=>o+s)}return i}function Fn(){return window.__MOTION_DEV_TOOLS_RECORD}function be(e,t,r,i={},n){let s=Fn(),o=i.record!==!1&&s,u,{duration:d=V.duration,delay:f=V.delay,endDelay:y=V.endDelay,repeat:g=V.repeat,easing:m=V.easing,persist:L=!1,direction:I,offset:M,allowWebkitAcceleration:k=!1,autoplay:R=!0}=i,P=he(e),F=ve(t),z=wt.waapi();F&&Wr(e,t);let $=Ut(t),B=jr(P.values,$),X=bt.get($);return ye(B.animation,!(gt(m)&&B.generator)&&i.record!==!1),()=>{let q=()=>{var b,l;return(l=(b=qt.get(e,$))!==null&&b!==void 0?b:X?.initialValue)!==null&&l!==void 0?l:0},_=Kr(ge(r),q),J=Ur(_,X);if(gt(m)){let b=m.createAnimation(_,t!=="opacity",q,$,B);m=b.easing,_=b.keyframes||_,d=b.duration||d}if(Kt($)&&(wt.cssRegisterProperty()?Br($):z=!1),F&&!wt.linearEasing()&&(G(m)||lt(m)&&m.some(G))&&(z=!1),z){X&&(_=_.map(a=>Q(a)?X.toDefaultUnit(a):a)),_.length===1&&(!wt.partialKeyframes()||o)&&_.unshift(q());let b={delay:yt.ms(f),duration:yt.ms(d),endDelay:yt.ms(y),easing:lt(m)?void 0:He(m,d),direction:I,iterations:g+1,fill:"both"};u=e.animate({[$]:_,offset:M,easing:lt(m)?m.map(a=>He(a,d)):void 0},b),u.finished||(u.finished=new Promise((a,v)=>{u.onfinish=a,u.oncancel=v}));let l=_[_.length-1];u.finished.then(()=>{L||(qt.set(e,$,l),u.cancel())}).catch(jt),k||(u.playbackRate=1.000001)}else if(n&&F)_=_.map(b=>typeof b=="string"?parseFloat(b):b),_.length===1&&_.unshift(parseFloat(q())),u=new n(b=>{qt.set(e,$,J?J(b):b)},_,Object.assign(Object.assign({},i),{duration:d,easing:m}));else{let b=_[_.length-1];qt.set(e,$,X&&Q(b)?X.toDefaultUnit(b):b)}return o&&s(e,t,_,{duration:d,delay:f,easing:m,repeat:g,offset:M},"motion-one"),B.setAnimation(u),u&&!R&&u.pause(),u}}var we=(e,t)=>e[t]?Object.assign(Object.assign({},e),e[t]):Object.assign({},e);function Et(e,t){var r;return typeof e=="string"?t?((r=t[e])!==null&&r!==void 0||(t[e]=document.querySelectorAll(e)),e=t[e]):e=document.querySelectorAll(e):e instanceof Element&&(e=[e]),Array.from(e||[])}var Ln=e=>e(),_t=(e,t,r=V.duration)=>new Proxy({animations:e.map(Ln).filter(Boolean),duration:r,options:t},Rn),Cn=e=>e.animations[0],Rn={get:(e,t)=>{let r=Cn(e);switch(t){case"duration":return e.duration;case"currentTime":return yt.s(r?.[t]||0);case"playbackRate":case"playState":return r?.[t];case"finished":return e.finished||(e.finished=Promise.all(e.animations.map(Mn)).catch(jt)),e.finished;case"stop":return()=>{e.animations.forEach(i=>ye(i))};case"forEachNative":return i=>{e.animations.forEach(n=>i(n,e))};default:return typeof r?.[t]>"u"?void 0:()=>e.animations.forEach(i=>i[t]())}},set:(e,t,r)=>{switch(t){case"currentTime":r=yt.ms(r);case"playbackRate":for(let i=0;i<e.animations.length;i++)e.animations[i][t]=r;return!0}return!1}},Mn=e=>e.finished;function qr(e=.1,{start:t=0,from:r=0,easing:i}={}){return(n,s)=>{let o=Q(r)?r:kn(r,s),u=Math.abs(o-n),d=e*u;if(i){let f=s*e;d=Ft(i)(d/f)*f}return t+d}}function kn(e,t){if(e==="first")return 0;{let r=t-1;return e==="last"?r:r/2}}function Ee(e,t,r){return G(e)?e(t,r):e}function Gr(e){return function(r,i,n={}){r=Et(r);let s=r.length;Vt(!!s,"No valid element provided."),Vt(!!i,"No keyframes defined.");let o=[];for(let u=0;u<s;u++){let d=r[u];for(let f in i){let y=we(n,f);y.delay=Ee(y.delay,u,s);let g=be(d,f,i[f],y,e);o.push(g)}}return _t(o,n,n.duration)}}var Ke=Gr(ft);function xe(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var n=0,i=Object.getOwnPropertySymbols(e);n<i.length;n++)t.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(e,i[n])&&(r[i[n]]=e[i[n]]);return r}function Ue(e,t,r,i){var n;return Q(t)?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):t==="<"?r:(n=i.get(t))!==null&&n!==void 0?n:e}function zn(e,t,r){for(let i=0;i<e.length;i++){let n=e[i];n.at>t&&n.at<r&&(ze(e,n),i--)}}function Zr(e,t,r,i,n,s){zn(e,n,s);for(let o=0;o<t.length;o++)e.push({value:t[o],at:Tt(n,s,i[o]),easing:zt(r,o)})}function Xr(e,t){return e.at===t.at?e.value===null?1:-1:e.at-t.at}function Yr(e,t={}){var r;let i=jn(e,t),n=i.map(s=>be(...s,ft)).filter(Boolean);return _t(n,t,(r=i[0])===null||r===void 0?void 0:r[3].duration)}function jn(e,t={}){var{defaultOptions:r={}}=t,i=xe(t,["defaultOptions"]);let n=[],s=new Map,o={},u=new Map,d=0,f=0,y=0;for(let g=0;g<e.length;g++){let m=e[g];if(ut(m)){u.set(m,f);continue}else if(!Array.isArray(m)){u.set(m.name,Ue(f,m.at,d,u));continue}let[L,I,M={}]=m;M.at!==void 0&&(f=Ue(f,M.at,d,u));let k=0,R=Et(L,o),P=R.length;for(let F=0;F<P;F++){let z=R[F],$=$n(z,s);for(let B in I){let X=Wn(B,$),q=ge(I[B]),_=we(M,B),{duration:J=r.duration||V.duration,easing:b=r.easing||V.easing}=_;if(gt(b)){Vt(B==="opacity"||q.length>1,"spring must be provided 2 keyframes within timeline()");let D=b.createAnimation(q,B!=="opacity",()=>0,B);b=D.easing,q=D.keyframes||q,J=D.duration||J}let l=Ee(M.delay,F,P)||0,a=f+l,v=a+J,{offset:S=At(q.length)}=_;S.length===1&&S[0]===0&&(S[1]=1);let T=S.length-q.length;T>0&&Pt(S,T),q.length===1&&q.unshift(null),Zr(X,q,b,S,a,v),k=Math.max(l+J,k),y=Math.max(v,y)}}d=f,f+=k}return s.forEach((g,m)=>{for(let L in g){let I=g[L];I.sort(Xr);let M=[],k=[],R=[];for(let P=0;P<I.length;P++){let{at:F,value:z,easing:$}=I[P];M.push(z),k.push(ot(0,y,F)),R.push($||V.easing)}k[0]!==0&&(k.unshift(0),M.unshift(M[0]),R.unshift("linear")),k[k.length-1]!==1&&(k.push(1),M.push(null)),n.push([m,L,M,Object.assign(Object.assign(Object.assign({},r),{duration:y,easing:R,offset:k}),i)])}}),n}function $n(e,t){return!t.has(e)&&t.set(e,{}),t.get(e)}function Wn(e,t){return t[e]||(t[e]=[]),t[e]}var Vn={any:0,all:1};function Qr(e,t,{root:r,margin:i,amount:n="any"}={}){if(typeof IntersectionObserver>"u")return()=>{};let s=Et(e),o=new WeakMap,u=f=>{f.forEach(y=>{let g=o.get(y.target);if(y.isIntersecting!==!!g)if(y.isIntersecting){let m=t(y);G(m)?o.set(y.target,m):d.unobserve(y.target)}else g&&(g(y),o.delete(y.target))})},d=new IntersectionObserver(u,{root:r,rootMargin:i,threshold:typeof n=="number"?n:Vn[n]});return s.forEach(f=>d.observe(f)),()=>d.disconnect()}var Se=new WeakMap,xt;function Bn(e,t){if(t){let{inlineSize:r,blockSize:i}=t[0];return{width:r,height:i}}else return e instanceof SVGElement&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight}}function Hn({target:e,contentRect:t,borderBoxSize:r}){var i;(i=Se.get(e))===null||i===void 0||i.forEach(n=>{n({target:e,contentSize:t,get size(){return Bn(e,r)}})})}function Kn(e){e.forEach(Hn)}function Un(){typeof ResizeObserver>"u"||(xt=new ResizeObserver(Kn))}function Jr(e,t){xt||Un();let r=Et(e);return r.forEach(i=>{let n=Se.get(i);n||(n=new Set,Se.set(i,n)),n.add(t),xt?.observe(i)}),()=>{r.forEach(i=>{let n=Se.get(i);n?.delete(t),n?.size||xt?.unobserve(i)})}}var Oe=new Set,Gt;function qn(){Gt=()=>{let e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};Oe.forEach(r=>r(t))},window.addEventListener("resize",Gt)}function ti(e){return Oe.add(e),Gt||qn(),()=>{Oe.delete(e),!Oe.size&&Gt&&(Gt=void 0)}}function ei(e,t){return G(e)?ti(e):Jr(e,t)}var Gn=50,ri=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),ni=()=>({time:0,x:ri(),y:ri()}),Zn={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function ii(e,t,r,i){let n=r[t],{length:s,position:o}=Zn[t],u=n.current,d=r.time;n.current=e["scroll"+o],n.scrollLength=e["scroll"+s]-e["client"+s],n.offset.length=0,n.offset[0]=0,n.offset[1]=n.scrollLength,n.progress=ot(0,n.scrollLength,n.current);let f=i-d;n.velocity=f>Gn?0:je(n.current-u,f)}function oi(e,t,r){ii(e,"x",t,r),ii(e,"y",t,r),t.time=r}function si(e,t){let r={x:0,y:0},i=e;for(;i&&i!==t;)if(i instanceof HTMLElement)r.x+=i.offsetLeft,r.y+=i.offsetTop,i=i.offsetParent;else if(i instanceof SVGGraphicsElement&&"getBBox"in i){let{top:n,left:s}=i.getBBox();for(r.x+=s,r.y+=n;i&&i.tagName!=="svg";)i=i.parentNode}return r}var Te={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]};var Ae={start:0,center:.5,end:1};function qe(e,t,r=0){let i=0;if(Ae[e]!==void 0&&(e=Ae[e]),ut(e)){let n=parseFloat(e);e.endsWith("px")?i=n:e.endsWith("%")?e=n/100:e.endsWith("vw")?i=n/100*document.documentElement.clientWidth:e.endsWith("vh")?i=n/100*document.documentElement.clientHeight:e=n}return Q(e)&&(i=t*e),r+i}var Xn=[0,0];function ai(e,t,r,i){let n=Array.isArray(e)?e:Xn,s=0,o=0;return Q(e)?n=[e,e]:ut(e)&&(e=e.trim(),e.includes(" ")?n=e.split(" "):n=[e,Ae[e]?e:"0"]),s=qe(n[0],r,i),o=qe(n[1],t),s-o}var Yn={x:0,y:0};function li(e,t,r){let{offset:i=Te.All}=r,{target:n=e,axis:s="y"}=r,o=s==="y"?"height":"width",u=n!==e?si(n,e):Yn,d=n===e?{width:e.scrollWidth,height:e.scrollHeight}:{width:n.clientWidth,height:n.clientHeight},f={width:e.clientWidth,height:e.clientHeight};t[s].offset.length=0;let y=!t[s].interpolate,g=i.length;for(let m=0;m<g;m++){let L=ai(i[m],f[o],d[o],u[s]);!y&&L!==t[s].interpolatorOffsets[m]&&(y=!0),t[s].offset[m]=L}y&&(t[s].interpolate=$t(At(g),t[s].offset),t[s].interpolatorOffsets=[...t[s].offset]),t[s].progress=t[s].interpolate(t[s].current)}function Qn(e,t=e,r){if(r.x.targetOffset=0,r.y.targetOffset=0,t!==e){let i=t;for(;i&&i!=e;)r.x.targetOffset+=i.offsetLeft,r.y.targetOffset+=i.offsetTop,i=i.offsetParent}r.x.targetLength=t===e?t.scrollWidth:t.clientWidth,r.y.targetLength=t===e?t.scrollHeight:t.clientHeight,r.x.containerLength=e.clientWidth,r.y.containerLength=e.clientHeight}function ci(e,t,r,i={}){let n=i.axis||"y";return{measure:()=>Qn(e,i.target,r),update:s=>{oi(e,r,s),(i.offset||i.target)&&li(e,r,i)},notify:G(t)?()=>t(r):Jn(t,r[n])}}function Jn(e,t){return e.pause(),e.forEachNative((r,{easing:i})=>{var n,s;if(r.updateDuration)i||(r.easing=Z),r.updateDuration(1);else{let o={duration:1e3};i||(o.easing="linear"),(s=(n=r.effect)===null||n===void 0?void 0:n.updateTiming)===null||s===void 0||s.call(n,o)}}),()=>{e.currentTime=t.progress}}var Zt=new WeakMap,ui=new WeakMap,Ge=new WeakMap,fi=e=>e===document.documentElement?window:e;function di(e,t={}){var{container:r=document.documentElement}=t,i=xe(t,["container"]);let n=Ge.get(r);n||(n=new Set,Ge.set(r,n));let s=ni(),o=ci(r,e,s,i);if(n.add(o),!Zt.has(r)){let f=()=>{let g=performance.now();for(let m of n)m.measure();for(let m of n)m.update(g);for(let m of n)m.notify()};Zt.set(r,f);let y=fi(r);window.addEventListener("resize",f,{passive:!0}),r!==document.documentElement&&ui.set(r,ei(r,f)),y.addEventListener("scroll",f,{passive:!0})}let u=Zt.get(r),d=requestAnimationFrame(u);return()=>{var f;typeof e!="function"&&e.stop(),cancelAnimationFrame(d);let y=Ge.get(r);if(!y||(y.delete(o),y.size))return;let g=Zt.get(r);Zt.delete(r),g&&(fi(r).removeEventListener("scroll",g),(f=ui.get(r))===null||f===void 0||f(),window.removeEventListener("resize",g))}}function to(e,t={}){return _t([()=>{let r=new ft(e,[0,1],t);return r.finished.catch(()=>{}),r}],t,t.duration)}function hi(e,t,r){return(G(e)?to:Ke)(e,t,r)}function dt(e){this.listenerMap=[{},{}],e&&this.root(e),this.handle=dt.prototype.handle.bind(this),this._removedListeners=[]}dt.prototype.root=function(e){let t=this.listenerMap,r;if(this.rootElement){for(r in t[1])t[1].hasOwnProperty(r)&&this.rootElement.removeEventListener(r,this.handle,!0);for(r in t[0])t[0].hasOwnProperty(r)&&this.rootElement.removeEventListener(r,this.handle,!1)}if(!e||!e.addEventListener)return this.rootElement&&delete this.rootElement,this;this.rootElement=e;for(r in t[1])t[1].hasOwnProperty(r)&&this.rootElement.addEventListener(r,this.handle,!0);for(r in t[0])t[0].hasOwnProperty(r)&&this.rootElement.addEventListener(r,this.handle,!1);return this};dt.prototype.captureForType=function(e){return["blur","error","focus","load","resize","scroll"].indexOf(e)!==-1};dt.prototype.on=function(e,t,r,i){let n,s,o,u;if(!e)throw new TypeError("Invalid event type: "+e);if(typeof t=="function"&&(i=r,r=t,t=null),i===void 0&&(i=this.captureForType(e)),typeof r!="function")throw new TypeError("Handler must be a type of Function");return n=this.rootElement,s=this.listenerMap[i?1:0],s[e]||(n&&n.addEventListener(e,this.handle,i),s[e]=[]),t?/^[a-z]+$/i.test(t)?(u=t,o=eo):/^#[a-z0-9\-_]+$/i.test(t)?(u=t.slice(1),o=io):(u=t,o=Element.prototype.matches):(u=null,o=ro.bind(this)),s[e].push({selector:t,handler:r,matcher:o,matcherParam:u}),this};dt.prototype.off=function(e,t,r,i){let n,s,o,u,d;if(typeof t=="function"&&(i=r,r=t,t=null),i===void 0)return this.off(e,t,r,!0),this.off(e,t,r,!1),this;if(o=this.listenerMap[i?1:0],!e){for(d in o)o.hasOwnProperty(d)&&this.off(d,t,r);return this}if(u=o[e],!u||!u.length)return this;for(n=u.length-1;n>=0;n--)s=u[n],(!t||t===s.selector)&&(!r||r===s.handler)&&(this._removedListeners.push(s),u.splice(n,1));return u.length||(delete o[e],this.rootElement&&this.rootElement.removeEventListener(e,this.handle,i)),this};dt.prototype.handle=function(e){let t,r,i=e.type,n,s,o,u,d=[],f,y="ftLabsDelegateIgnore";if(e[y]===!0)return;switch(f=e.target,f.nodeType===3&&(f=f.parentNode),f.correspondingUseElement&&(f=f.correspondingUseElement),n=this.rootElement,s=e.eventPhase||(e.target!==e.currentTarget?3:2),s){case 1:d=this.listenerMap[1][i];break;case 2:this.listenerMap[0]&&this.listenerMap[0][i]&&(d=d.concat(this.listenerMap[0][i])),this.listenerMap[1]&&this.listenerMap[1][i]&&(d=d.concat(this.listenerMap[1][i]));break;case 3:d=this.listenerMap[0][i];break}let g=[];for(r=d.length;f&&r;){for(t=0;t<r&&(o=d[t],!!o);t++)f.tagName&&["button","input","select","textarea"].indexOf(f.tagName.toLowerCase())>-1&&f.hasAttribute("disabled")?g=[]:o.matcher.call(f,o.matcherParam,f)&&g.push([e,f,o]);if(f===n||(r=d.length,f=f.parentElement||f.parentNode,f instanceof HTMLDocument))break}let m;for(t=0;t<g.length;t++)if(!(this._removedListeners.indexOf(g[t][2])>-1)&&(u=this.fire.apply(this,g[t]),u===!1)){g[t][0][y]=!0,g[t][0].preventDefault(),m=!1;break}return m};dt.prototype.fire=function(e,t,r){return r.handler.call(t,e,t)};function eo(e,t){return e.toLowerCase()===t.tagName.toLowerCase()}function ro(e,t){return this.rootElement===window?t===document||t===document.documentElement||t===window:this.rootElement===t}function io(e,t){return e===t.id}dt.prototype.destroy=function(){this.off(),this.root()};var no=dt;function Xt(e,t,r){let i=document.createElement(t);return e&&(i.className=e),r&&r.appendChild(i),i}function oo(e,t,r){let i=`translate3d(${e}px,${t||0}px,0)`;return r!==void 0&&(i+=` scale3d(${r},${r},1)`),i}function Ze(e,t,r){e.style.width=typeof t=="number"?`${t}px`:t,e.style.height=typeof r=="number"?`${r}px`:r}var it={IDLE:"idle",LOADING:"loading",LOADED:"loaded",ERROR:"error"};function so(e){return"button"in e&&e.button===1||e.ctrlKey||e.metaKey||e.altKey||e.shiftKey}function Yt(e,t,r=document){let i=[];if(e instanceof Element)i=[e];else if(e instanceof NodeList||Array.isArray(e))i=Array.from(e);else{let n=typeof e=="string"?e:t;n&&(i=Array.from(r.querySelectorAll(n)))}return i}function ao(e){return typeof e=="function"&&e.prototype&&e.prototype.goTo}function pi(){return!!(navigator.vendor&&navigator.vendor.match(/apple/i))}var Xe=class{constructor(t,r){this.type=t,this.defaultPrevented=!1,r&&Object.assign(this,r)}preventDefault(){this.defaultPrevented=!0}},Ye=class{constructor(){this._listeners={},this._filters={},this.pswp=void 0,this.options=void 0}addFilter(t,r,i=100){var n,s,o;this._filters[t]||(this._filters[t]=[]),(n=this._filters[t])===null||n===void 0||n.push({fn:r,priority:i}),(s=this._filters[t])===null||s===void 0||s.sort((u,d)=>u.priority-d.priority),(o=this.pswp)===null||o===void 0||o.addFilter(t,r,i)}removeFilter(t,r){this._filters[t]&&(this._filters[t]=this._filters[t].filter(i=>i.fn!==r)),this.pswp&&this.pswp.removeFilter(t,r)}applyFilters(t,...r){var i;return(i=this._filters[t])===null||i===void 0||i.forEach(n=>{r[0]=n.fn.apply(this,r)}),r[0]}on(t,r){var i,n;this._listeners[t]||(this._listeners[t]=[]),(i=this._listeners[t])===null||i===void 0||i.push(r),(n=this.pswp)===null||n===void 0||n.on(t,r)}off(t,r){var i;this._listeners[t]&&(this._listeners[t]=this._listeners[t].filter(n=>r!==n)),(i=this.pswp)===null||i===void 0||i.off(t,r)}dispatch(t,r){var i;if(this.pswp)return this.pswp.dispatch(t,r);let n=new Xe(t,r);return(i=this._listeners[t])===null||i===void 0||i.forEach(s=>{s.call(this,n)}),n}},Qe=class{constructor(t,r){if(this.element=Xt("pswp__img pswp__img--placeholder",t?"img":"div",r),t){let i=this.element;i.decoding="async",i.alt="",i.src=t,i.setAttribute("role","presentation")}this.element.setAttribute("aria-hidden","true")}setDisplayedSize(t,r){this.element&&(this.element.tagName==="IMG"?(Ze(this.element,250,"auto"),this.element.style.transformOrigin="0 0",this.element.style.transform=oo(0,0,t/250)):Ze(this.element,t,r))}destroy(){var t;(t=this.element)!==null&&t!==void 0&&t.parentNode&&this.element.remove(),this.element=null}},Je=class{constructor(t,r,i){this.instance=r,this.data=t,this.index=i,this.element=void 0,this.placeholder=void 0,this.slide=void 0,this.displayedImageWidth=0,this.displayedImageHeight=0,this.width=Number(this.data.w)||Number(this.data.width)||0,this.height=Number(this.data.h)||Number(this.data.height)||0,this.isAttached=!1,this.hasSlide=!1,this.isDecoding=!1,this.state=it.IDLE,this.data.type?this.type=this.data.type:this.data.src?this.type="image":this.type="html",this.instance.dispatch("contentInit",{content:this})}removePlaceholder(){this.placeholder&&!this.keepPlaceholder()&&setTimeout(()=>{this.placeholder&&(this.placeholder.destroy(),this.placeholder=void 0)},1e3)}load(t,r){if(this.slide&&this.usePlaceholder())if(this.placeholder){let i=this.placeholder.element;i&&!i.parentElement&&this.slide.container.prepend(i)}else{let i=this.instance.applyFilters("placeholderSrc",this.data.msrc&&this.slide.isFirstSlide?this.data.msrc:!1,this);this.placeholder=new Qe(i,this.slide.container)}this.element&&!r||this.instance.dispatch("contentLoad",{content:this,isLazy:t}).defaultPrevented||(this.isImageContent()?(this.element=Xt("pswp__img","img"),this.displayedImageWidth&&this.loadImage(t)):(this.element=Xt("pswp__content","div"),this.element.innerHTML=this.data.html||""),r&&this.slide&&this.slide.updateContentSize(!0))}loadImage(t){var r,i;if(!this.isImageContent()||!this.element||this.instance.dispatch("contentLoadImage",{content:this,isLazy:t}).defaultPrevented)return;let n=this.element;this.updateSrcsetSizes(),this.data.srcset&&(n.srcset=this.data.srcset),n.src=(r=this.data.src)!==null&&r!==void 0?r:"",n.alt=(i=this.data.alt)!==null&&i!==void 0?i:"",this.state=it.LOADING,n.complete?this.onLoaded():(n.onload=()=>{this.onLoaded()},n.onerror=()=>{this.onError()})}setSlide(t){this.slide=t,this.hasSlide=!0,this.instance=t.pswp}onLoaded(){this.state=it.LOADED,this.slide&&this.element&&(this.instance.dispatch("loadComplete",{slide:this.slide,content:this}),this.slide.isActive&&this.slide.heavyAppended&&!this.element.parentNode&&(this.append(),this.slide.updateContentSize(!0)),(this.state===it.LOADED||this.state===it.ERROR)&&this.removePlaceholder())}onError(){this.state=it.ERROR,this.slide&&(this.displayError(),this.instance.dispatch("loadComplete",{slide:this.slide,isError:!0,content:this}),this.instance.dispatch("loadError",{slide:this.slide,content:this}))}isLoading(){return this.instance.applyFilters("isContentLoading",this.state===it.LOADING,this)}isError(){return this.state===it.ERROR}isImageContent(){return this.type==="image"}setDisplayedSize(t,r){if(this.element&&(this.placeholder&&this.placeholder.setDisplayedSize(t,r),!this.instance.dispatch("contentResize",{content:this,width:t,height:r}).defaultPrevented&&(Ze(this.element,t,r),this.isImageContent()&&!this.isError()))){let i=!this.displayedImageWidth&&t;this.displayedImageWidth=t,this.displayedImageHeight=r,i?this.loadImage(!1):this.updateSrcsetSizes(),this.slide&&this.instance.dispatch("imageSizeChange",{slide:this.slide,width:t,height:r,content:this})}}isZoomable(){return this.instance.applyFilters("isContentZoomable",this.isImageContent()&&this.state!==it.ERROR,this)}updateSrcsetSizes(){if(!this.isImageContent()||!this.element||!this.data.srcset)return;let t=this.element,r=this.instance.applyFilters("srcsetSizesWidth",this.displayedImageWidth,this);(!t.dataset.largestUsedSize||r>parseInt(t.dataset.largestUsedSize,10))&&(t.sizes=r+"px",t.dataset.largestUsedSize=String(r))}usePlaceholder(){return this.instance.applyFilters("useContentPlaceholder",this.isImageContent(),this)}lazyLoad(){this.instance.dispatch("contentLazyLoad",{content:this}).defaultPrevented||this.load(!0)}keepPlaceholder(){return this.instance.applyFilters("isKeepingPlaceholder",this.isLoading(),this)}destroy(){this.hasSlide=!1,this.slide=void 0,!this.instance.dispatch("contentDestroy",{content:this}).defaultPrevented&&(this.remove(),this.placeholder&&(this.placeholder.destroy(),this.placeholder=void 0),this.isImageContent()&&this.element&&(this.element.onload=null,this.element.onerror=null,this.element=void 0))}displayError(){if(this.slide){var t,r;let i=Xt("pswp__error-msg","div");i.innerText=(t=(r=this.instance.options)===null||r===void 0?void 0:r.errorMsg)!==null&&t!==void 0?t:"",i=this.instance.applyFilters("contentErrorElement",i,this),this.element=Xt("pswp__content pswp__error-msg-container","div"),this.element.appendChild(i),this.slide.container.innerText="",this.slide.container.appendChild(this.element),this.slide.updateContentSize(!0),this.removePlaceholder()}}append(){if(this.isAttached||!this.element)return;if(this.isAttached=!0,this.state===it.ERROR){this.displayError();return}if(this.instance.dispatch("contentAppend",{content:this}).defaultPrevented)return;let t="decode"in this.element;this.isImageContent()?t&&this.slide&&(!this.slide.isActive||pi())?(this.isDecoding=!0,this.element.decode().catch(()=>{}).finally(()=>{this.isDecoding=!1,this.appendImage()})):this.appendImage():this.slide&&!this.element.parentNode&&this.slide.container.appendChild(this.element)}activate(){this.instance.dispatch("contentActivate",{content:this}).defaultPrevented||!this.slide||(this.isImageContent()&&this.isDecoding&&!pi()?this.appendImage():this.isError()&&this.load(!1,!0),this.slide.holderElement&&this.slide.holderElement.setAttribute("aria-hidden","false"))}deactivate(){this.instance.dispatch("contentDeactivate",{content:this}),this.slide&&this.slide.holderElement&&this.slide.holderElement.setAttribute("aria-hidden","true")}remove(){this.isAttached=!1,!this.instance.dispatch("contentRemove",{content:this}).defaultPrevented&&(this.element&&this.element.parentNode&&this.element.remove(),this.placeholder&&this.placeholder.element&&this.placeholder.element.remove())}appendImage(){this.isAttached&&(this.instance.dispatch("contentAppendImage",{content:this}).defaultPrevented||(this.slide&&this.element&&!this.element.parentNode&&this.slide.container.appendChild(this.element),(this.state===it.LOADED||this.state===it.ERROR)&&this.removePlaceholder()))}};function lo(e,t){if(e.getViewportSizeFn){let r=e.getViewportSizeFn(e,t);if(r)return r}return{x:document.documentElement.clientWidth,y:window.innerHeight}}function Ie(e,t,r,i,n){let s=0;if(t.paddingFn)s=t.paddingFn(r,i,n)[e];else if(t.padding)s=t.padding[e];else{let o="padding"+e[0].toUpperCase()+e.slice(1);t[o]&&(s=t[o])}return Number(s)||0}function co(e,t,r,i){return{x:t.x-Ie("left",e,t,r,i)-Ie("right",e,t,r,i),y:t.y-Ie("top",e,t,r,i)-Ie("bottom",e,t,r,i)}}var mi=4e3,tr=class{constructor(t,r,i,n){this.pswp=n,this.options=t,this.itemData=r,this.index=i,this.panAreaSize=null,this.elementSize=null,this.fit=1,this.fill=1,this.vFill=1,this.initial=1,this.secondary=1,this.max=1,this.min=1}update(t,r,i){let n={x:t,y:r};this.elementSize=n,this.panAreaSize=i;let s=i.x/n.x,o=i.y/n.y;this.fit=Math.min(1,s<o?s:o),this.fill=Math.min(1,s>o?s:o),this.vFill=Math.min(1,o),this.initial=this._getInitial(),this.secondary=this._getSecondary(),this.max=Math.max(this.initial,this.secondary,this._getMax()),this.min=Math.min(this.fit,this.initial,this.secondary),this.pswp&&this.pswp.dispatch("zoomLevelsUpdate",{zoomLevels:this,slideData:this.itemData})}_parseZoomLevelOption(t){let r=t+"ZoomLevel",i=this.options[r];if(i)return typeof i=="function"?i(this):i==="fill"?this.fill:i==="fit"?this.fit:Number(i)}_getSecondary(){let t=this._parseZoomLevelOption("secondary");return t||(t=Math.min(1,this.fit*3),this.elementSize&&t*this.elementSize.x>mi&&(t=mi/this.elementSize.x),t)}_getInitial(){return this._parseZoomLevelOption("initial")||this.fit}_getMax(){return this._parseZoomLevelOption("max")||Math.max(1,this.fit*4)}};function vi(e,t,r){let i=t.createContentFromData(e,r),n,{options:s}=t;if(s){n=new tr(s,e,-1);let o;t.pswp?o=t.pswp.viewportSize:o=lo(s,t);let u=co(s,o,e,r);n.update(i.width,i.height,u)}return i.lazyLoad(),n&&i.setDisplayedSize(Math.ceil(i.width*n.initial),Math.ceil(i.height*n.initial)),i}function uo(e,t){let r=t.getItemData(e);if(!t.dispatch("lazyLoadSlide",{index:e,itemData:r}).defaultPrevented)return vi(r,t,e)}var er=class extends Ye{getNumItems(){var t;let r=0,i=(t=this.options)===null||t===void 0?void 0:t.dataSource;i&&"length"in i?r=i.length:i&&"gallery"in i&&(i.items||(i.items=this._getGalleryDOMElements(i.gallery)),i.items&&(r=i.items.length));let n=this.dispatch("numItems",{dataSource:i,numItems:r});return this.applyFilters("numItems",n.numItems,i)}createContentFromData(t,r){return new Je(t,this,r)}getItemData(t){var r;let i=(r=this.options)===null||r===void 0?void 0:r.dataSource,n={};Array.isArray(i)?n=i[t]:i&&"gallery"in i&&(i.items||(i.items=this._getGalleryDOMElements(i.gallery)),n=i.items[t]);let s=n;s instanceof Element&&(s=this._domElementToItemData(s));let o=this.dispatch("itemData",{itemData:s||{},index:t});return this.applyFilters("itemData",o.itemData,t)}_getGalleryDOMElements(t){var r,i;return(r=this.options)!==null&&r!==void 0&&r.children||(i=this.options)!==null&&i!==void 0&&i.childSelector?Yt(this.options.children,this.options.childSelector,t)||[]:[t]}_domElementToItemData(t){let r={element:t},i=t.tagName==="A"?t:t.querySelector("a");if(i){r.src=i.dataset.pswpSrc||i.href,i.dataset.pswpSrcset&&(r.srcset=i.dataset.pswpSrcset),r.width=i.dataset.pswpWidth?parseInt(i.dataset.pswpWidth,10):0,r.height=i.dataset.pswpHeight?parseInt(i.dataset.pswpHeight,10):0,r.w=r.width,r.h=r.height,i.dataset.pswpType&&(r.type=i.dataset.pswpType);let s=t.querySelector("img");if(s){var n;r.msrc=s.currentSrc||s.src,r.alt=(n=s.getAttribute("alt"))!==null&&n!==void 0?n:""}(i.dataset.pswpCropped||i.dataset.cropped)&&(r.thumbCropped=!0)}return this.applyFilters("domItemData",r,t,i)}lazyLoadData(t,r){return vi(t,this,r)}},rr=class extends er{constructor(t){super(),this.options=t||{},this._uid=0,this.shouldOpen=!1,this._preloadedContent=void 0,this.onThumbnailsClick=this.onThumbnailsClick.bind(this)}init(){Yt(this.options.gallery,this.options.gallerySelector).forEach(t=>{t.addEventListener("click",this.onThumbnailsClick,!1)})}onThumbnailsClick(t){if(so(t)||window.pswp)return;let r={x:t.clientX,y:t.clientY};!r.x&&!r.y&&(r=null);let i=this.getClickedIndex(t);i=this.applyFilters("clickedIndex",i,t,this);let n={gallery:t.currentTarget};i>=0&&(t.preventDefault(),this.loadAndOpen(i,n,r))}getClickedIndex(t){if(this.options.getClickedIndexFn)return this.options.getClickedIndexFn.call(this,t);let r=t.target,n=Yt(this.options.children,this.options.childSelector,t.currentTarget).findIndex(s=>s===r||s.contains(r));return n!==-1?n:this.options.children||this.options.childSelector?-1:0}loadAndOpen(t,r,i){if(window.pswp||!this.options)return!1;if(!r&&this.options.gallery&&this.options.children){let n=Yt(this.options.gallery);n[0]&&(r={gallery:n[0]})}return this.options.index=t,this.options.initialPointerPos=i,this.shouldOpen=!0,this.preload(t,r),!0}preload(t,r){let{options:i}=this;r&&(i.dataSource=r);let n=[],s=typeof i.pswpModule;if(ao(i.pswpModule))n.push(Promise.resolve(i.pswpModule));else{if(s==="string")throw new Error("pswpModule as string is no longer supported");if(s==="function")n.push(i.pswpModule());else throw new Error("pswpModule is not valid")}typeof i.openPromise=="function"&&n.push(i.openPromise()),i.preloadFirstSlide!==!1&&t>=0&&(this._preloadedContent=uo(t,this));let o=++this._uid;Promise.all(n).then(u=>{if(this.shouldOpen){let d=u[0];this._openPhotoswipe(d,o)}})}_openPhotoswipe(t,r){if(r!==this._uid&&this.shouldOpen||(this.shouldOpen=!1,window.pswp))return;let i=typeof t=="object"?new t.default(this.options):new t(this.options);this.pswp=i,window.pswp=i,Object.keys(this._listeners).forEach(n=>{var s;(s=this._listeners[n])===null||s===void 0||s.forEach(o=>{i.on(n,o)})}),Object.keys(this._filters).forEach(n=>{var s;(s=this._filters[n])===null||s===void 0||s.forEach(o=>{i.addFilter(n,o.fn,o.priority)})}),this._preloadedContent&&(i.contentLoader.addToCache(this._preloadedContent),this._preloadedContent=void 0),i.on("destroy",()=>{this.pswp=void 0,delete window.pswp}),i.init()}destroy(){var t;(t=this.pswp)===null||t===void 0||t.destroy(),this.shouldOpen=!1,this._listeners={},Yt(this.options.gallery,this.options.gallerySelector).forEach(r=>{r.removeEventListener("click",this.onThumbnailsClick,!1)})}};(function(){Node.prototype.replaceChildren===void 0&&(Node.prototype.replaceChildren=e=>{for(;this.lastChild;)this.removeChild(this.lastChild);e!==void 0&&this.append(e)})})();(function(){let e=!1;if(document.createElement("i").addEventListener("click",()=>{},{get signal(){e=!0}}),e||!window.AbortController)return;let t=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(r,i,n){if(n&&n.signal){if(n.signal.aborted)return;n.signal.addEventListener("abort",()=>this.removeEventListener(r,i,{...n}))}return t.call(this,r,i,n)}})();export{ql as CustomElementsPolyfill,no as Delegate,Cr as FocusTrap,rr as PhotoSwipeLightbox,Te as ScrollOffset,hi as animate,Qr as inView,di as scroll,qr as stagger,Yr as timeline};
/*! Bundled license information:

@ungap/custom-elements/index.js:
  (*! (c) Andrea Giammarchi @webreflection ISC *)
  (*! (c) Andrea Giammarchi - ISC *)

instant.page/instantpage.js:
  (*! instant.page v5.2.0 - (C) 2019-2023 Alexandre Dieulot - https://instant.page/license *)

tabbable/dist/index.esm.js:
  (*!
  * tabbable 6.2.0
  * @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
  *)

focus-trap/dist/focus-trap.esm.js:
  (*!
  * focus-trap 7.5.4
  * @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE
  *)

photoswipe/dist/photoswipe-lightbox.esm.js:
  (*!
    * PhotoSwipe Lightbox 5.4.3 - https://photoswipe.com
    * (c) 2023 Dmytro Semenov
    *)
*/
