{%- comment -%}
Custom Aspects section (block-first)
- Every piece of content is a block so merchants can reorder freely
- Desktop image position toggle (left/right). On mobile/tablet, order follows block placement
- Icons grid provided as a single block (up to 10 icons, 5 per row on desktop)
- Feature cards provided as a single block with two sub-cards
- Nutrition button is its own block with a modal image
{%- endcomment -%}

<section class="custom-aspects{% if section.settings.image_position == 'right' %} is-image-right{% endif %}" aria-label="Health Aspects">
  <style>
    #shopify-section-{{ section.id }}{
      --ca-bg: {{ section.settings.background_color.rgb }};
      --ca-text: {{ section.settings.text_color.rgb }};
      --ca-accent: {{ section.settings.accent_color.rgb }};
      --ca-card-bg: {{ section.settings.card_bg.rgb }};
      --ca-eyebrow: {{ section.settings.eyebrow_color.rgb }};
      --ca-button-bg: {{ section.settings.button_bg.rgb }};
      --ca-button-text: {{ section.settings.button_text.rgb }};
      --ca-checkmark: {{ section.settings.checkmark_color.rgb }};
    }
  </style>
  {{ 'custom-typography.css' | asset_url | stylesheet_tag }}
  {{ 'custom-aspects.css' | asset_url | stylesheet_tag }}
  <script src="{{ 'custom-aspects.js' | asset_url }}" defer></script>

  <div class="ca-wrapper">
    <div class="ca-mobile">
      <div class="ca-grid">
        {% assign pending_nutrition = '' %}
        {%- for block in section.blocks -%}
          {%- if pending_nutrition != '' and block.type != 'ingredients' -%}
          {{ pending_nutrition }}
          {%- assign pending_nutrition = '' -%}
        {%- endif -%}
        {%- case block.type -%}
          {%- when 'image' -%}
            <div class="ca-block ca-side-image" {{ block.shopify_attributes }}>
              {%- if block.settings.image != blank -%}
                <img src="{{ block.settings.image | image_url: width: 1600 }}" width="{{ block.settings.image.width }}" height="{{ block.settings.image.height }}" alt="{{ block.settings.image.alt | escape }}" loading="lazy">
              {%- endif -%}
            </div>

          {%- when 'eyebrow' -%}
            <div class="ca-block ca-eyebrow ca-align-{{ block.settings.align }}" {{ block.shopify_attributes }}>{{ block.settings.text }}</div>

          {%- when 'heading' -%}
            <h2 class="ca-block ca-heading custom-heading-secondary custom-aspects ca-align-{{ block.settings.align }}{% unless block.settings.bold %} is-normal{% endunless %}" {{ block.shopify_attributes }}>
              <span>{{ block.settings.line1 }}</span>
              <span class="accent">{{ block.settings.line2 }}</span>
            </h2>

          {%- when 'text' -%}
            <div class="ca-block ca-desc rte custom-body-text ca-align-{{ block.settings.align }}" {{ block.shopify_attributes }}>{{ block.settings.body }}</div>

          {%- when 'feature-cards' -%}
            <div class="ca-block ca-cards ca-align-{{ block.settings.align }}" {{ block.shopify_attributes }}>
              <div class="ca-card">
                <h3>{{ block.settings.title1 }}</h3>
                {%- if block.settings.bullet1a != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1a }}</div></div>
                {%- endif -%}
                {%- if block.settings.bullet1b != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1b }}</div></div>
                {%- endif -%}
                {%- if block.settings.bullet1c != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1c }}</div></div>
                {%- endif -%}
                {%- if block.settings.bullet1d != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1d }}</div></div>
                {%- endif -%}
                {%- if block.settings.bullet1e != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1e }}</div></div>
                {%- endif -%}
              </div>
              <div class="ca-card">
                <h3>{{ block.settings.title2 }}</h3>
                {%- if block.settings.bullet2a != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2a }}</div></div>
                {%- endif -%}
                {%- if block.settings.bullet2b != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2b }}</div></div>
                {%- endif -%}
                {%- if block.settings.bullet2c != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2c }}</div></div>
                {%- endif -%}
                {%- if block.settings.bullet2d != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2d }}</div></div>
                {%- endif -%}
                {%- if block.settings.bullet2e != blank -%}
                  <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2e }}</div></div>
                {%- endif -%}
              </div>
            </div>

          {%- when 'icons-grid' -%}
            <div class="ca-block ca-icons" {{ block.shopify_attributes }}>
              {%- for i in (1..10) -%}
                {%- assign icon = 'icon' | append: i -%}
                {%- assign label = 'label' | append: i -%}
                {%- assign sub = 'sub' | append: i -%}
                {%- assign img = block.settings[icon] -%}
                {%- assign lbl = block.settings[label] -%}
                {%- assign sbl = block.settings[sub] -%}
                {%- if img != blank or lbl != blank or sbl != blank -%}
                  <div class="ca-icon">
                    {%- if img != blank -%}
                      <img src="{{ img | image_url: width: 140 }}" width="{{ img.width }}" height="{{ img.height }}" alt="">
                    {%- endif -%}
                    {%- if lbl != blank -%}<div class="ca-icon-label">{{ lbl }}</div>{%- endif -%}
                    {%- if sbl != blank -%}<div class="ca-icon-sub">{{ sbl }}</div>{%- endif -%}
                  </div>
                {%- endif -%}
              {%- endfor -%}
            </div>

          {%- when 'nutrition' -%}
            {% capture pending_nutrition %}
            <div class="ca-block ca-actions" {{ block.shopify_attributes }} data-nutrition>
              <button type="button" class="ca-button" data-nutrition-open>{{ block.settings.label }}</button>
              <div class="ca-modal-backdrop" aria-hidden="true" data-modal-backdrop>
                <div class="ca-modal" role="dialog" aria-modal="true" data-modal>
                  <button class="ca-modal-close" aria-label="Close" data-modal-close>
                    <svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true"><path d="M18.3 5.71 12 12l6.3 6.29-1.41 1.41L10.59 13.41 4.29 19.71 2.88 18.3 9.17 12 2.88 5.71 4.29 4.3 10.59 10.59 16.89 4.3z"/></svg>
                  </button>
                  {%- if block.settings.image != blank -%}
                    <img src="{{ block.settings.image | image_url: width: 1800 }}" width="{{ block.settings.image.width }}" height="{{ block.settings.image.height }}" alt="{{ block.settings.image.alt | escape }}">
                  {%- endif -%}
                </div>
              </div>
            </div>
            {% endcapture %}

          {%- when 'ingredients' -%}
            {%- if pending_nutrition != '' -%}
              <div class="ca-block ca-actions-group" {{ block.shopify_attributes }}>
                {{ pending_nutrition }}
                <div class="ca-ingredients rte ca-align-{{ block.settings.align }}">{{ block.settings.text }}</div>
              </div>
              {%- assign pending_nutrition = '' -%}
            {%- else -%}
              <div class="ca-block ca-ingredients rte ca-align-{{ block.settings.align }}" {{ block.shopify_attributes }}>{{ block.settings.text }}</div>
            {%- endif -%}
        {%- endcase -%}
      {%- endfor -%}
      {%- if pending_nutrition != '' -%}
        {{ pending_nutrition }}
      {%- endif -%}
        </div>
      </div>
      <div class="ca-desktop">
        <div class="ca-col-image">
          {%- for block in section.blocks -%}
            {%- if block.type == 'image' -%}
              <div class="ca-block ca-side-image" {{ block.shopify_attributes }}>
                {%- if block.settings.image != blank -%}
                  <img src="{{ block.settings.image | image_url: width: 1600 }}" width="{{ block.settings.image.width }}" height="{{ block.settings.image.height }}" alt="{{ block.settings.image.alt | escape }}" loading="lazy">
                {%- endif -%}
              </div>
            {%- endif -%}
          {%- endfor -%}
        </div>
        <div class="ca-col-content">
          {% assign pending_nutrition_desktop = '' %}
          {%- for block in section.blocks -%}
            {%- if pending_nutrition_desktop != '' and block.type != 'ingredients' -%}
              {{ pending_nutrition_desktop }}
              {%- assign pending_nutrition_desktop = '' -%}
            {%- endif -%}
            {%- case block.type -%}
              {%- when 'image' -%}{% comment %}skip image in content column{% endcomment %}

              {%- when 'eyebrow' -%}
                <div class="ca-block ca-eyebrow ca-align-{{ block.settings.align }}" {{ block.shopify_attributes }}>{{ block.settings.text }}</div>

              {%- when 'heading' -%}
                <h2 class="ca-block ca-heading custom-heading-secondary custom-aspects ca-align-{{ block.settings.align }}{% unless block.settings.bold %} is-normal{% endunless %}" {{ block.shopify_attributes }}>
                  <span>{{ block.settings.line1 }}</span>
                  <span class="accent">{{ block.settings.line2 }}</span>
                </h2>

              {%- when 'text' -%}
                <div class="ca-block ca-desc rte ca-align-{{ block.settings.align }}" {{ block.shopify_attributes }}>{{ block.settings.body }}</div>

              {%- when 'feature-cards' -%}
                <div class="ca-block ca-cards ca-align-{{ block.settings.align }}" {{ block.shopify_attributes }}>
                  <div class="ca-card">
                    <h3>{{ block.settings.title1 }}</h3>
                    {%- if block.settings.bullet1a != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1a }}</div></div>
                    {%- endif -%}
                    {%- if block.settings.bullet1b != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1b }}</div></div>
                    {%- endif -%}
                    {%- if block.settings.bullet1c != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1c }}</div></div>
                    {%- endif -%}
                    {%- if block.settings.bullet1d != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1d }}</div></div>
                    {%- endif -%}
                    {%- if block.settings.bullet1e != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet1e }}</div></div>
                    {%- endif -%}
                  </div>
                  <div class="ca-card">
                    <h3>{{ block.settings.title2 }}</h3>
                    {%- if block.settings.bullet2a != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2a }}</div></div>
                    {%- endif -%}
                    {%- if block.settings.bullet2b != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2b }}</div></div>
                    {%- endif -%}
                    {%- if block.settings.bullet2c != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2c }}</div></div>
                    {%- endif -%}
                    {%- if block.settings.bullet2d != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2d }}</div></div>
                    {%- endif -%}
                    {%- if block.settings.bullet2e != blank -%}
                      <div class="ca-check"><svg viewBox="0 0 24 24" width="18" height="18" aria-hidden="true"><path d="M9 16.17 4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg><div class="ca-check__text rte">{{ block.settings.bullet2e }}</div></div>
                    {%- endif -%}
                  </div>
                </div>

              {%- when 'icons-grid' -%}
                <div class="ca-block ca-icons" {{ block.shopify_attributes }}>
                  {%- for i in (1..10) -%}
                    {%- assign icon = 'icon' | append: i -%}
                    {%- assign label = 'label' | append: i -%}
                    {%- assign sub = 'sub' | append: i -%}
                    {%- assign img = block.settings[icon] -%}
                    {%- assign lbl = block.settings[label] -%}
                    {%- assign sbl = block.settings[sub] -%}
                    {%- if img != blank or lbl != blank or sbl != blank -%}
                      <div class="ca-icon">
                        {%- if img != blank -%}
                          <img src="{{ img | image_url: width: 140 }}" width="{{ img.width }}" height="{{ img.height }}" alt="">
                        {%- endif -%}
                        {%- if lbl != blank -%}<div class="ca-icon-label">{{ lbl }}</div>{%- endif -%}
                        {%- if sbl != blank -%}<div class="ca-icon-sub">{{ sbl }}</div>{%- endif -%}
                      </div>
                    {%- endif -%}
                  {%- endfor -%}
                </div>

              {%- when 'nutrition' -%}
                {% capture pending_nutrition_desktop %}
                <div class="ca-block ca-actions" {{ block.shopify_attributes }} data-nutrition>
                  <button type="button" class="ca-button" data-nutrition-open>{{ block.settings.label }}</button>
                  <div class="ca-modal-backdrop" aria-hidden="true" data-modal-backdrop>
                    <div class="ca-modal" role="dialog" aria-modal="true" data-modal>
                      <button class="ca-modal-close" aria-label="Close" data-modal-close>
                        <svg width="18" height="18" viewBox="0 0 24 24" aria-hidden="true"><path d="M18.3 5.71 12 12l6.3 6.29-1.41 1.41L10.59 13.41 4.29 19.71 2.88 18.3 9.17 12 2.88 5.71 4.29 4.3 10.59 10.59 16.89 4.3z"/></svg>
                      </button>
                      {%- if block.settings.image != blank -%}
                        <img src="{{ block.settings.image | image_url: width: 1800 }}" width="{{ block.settings.image.width }}" height="{{ block.settings.image.height }}" alt="{{ block.settings.image.alt | escape }}">
                      {%- endif -%}
                    </div>
                  </div>
                </div>
                {% endcapture %}

              {%- when 'ingredients' -%}
                {%- if pending_nutrition_desktop != '' -%}
                  <div class="ca-block ca-actions-group" {{ block.shopify_attributes }}>
                    {{ pending_nutrition_desktop }}
                    <div class="ca-ingredients rte ca-align-{{ block.settings.align }}">{{ block.settings.text }}</div>
                  </div>
                  {%- assign pending_nutrition_desktop = '' -%}
                {%- else -%}
                  <div class="ca-block ca-ingredients rte ca-align-{{ block.settings.align }}" {{ block.shopify_attributes }}>{{ block.settings.text }}</div>
                {%- endif -%}
            {%- endcase -%}
          {%- endfor -%}
          {%- if pending_nutrition_desktop != '' -%}
            {{ pending_nutrition_desktop }}
          {%- endif -%}
        </div>
      </div>
    </div>
</section>

{% schema %}
{
  "name": "Custom Aspects",
  "class": "shopify-section--custom-aspects",
  "tag": "section",
  "disabled_on": { "groups": ["header", "custom.overlay"] },
  "settings": [
    {"type":"select","id":"image_position","label":"Image position on desktop","default":"left","options":[{"value":"left","label":"Left"},{"value":"right","label":"Right"}]},
    {"type":"color","id":"background_color","label":"Background color","default":"#FFF3EC"},
    {"type":"color","id":"text_color","label":"Text color","default":"#2D2A26"},
    {"type":"color","id":"accent_color","label":"Accent color","default":"#D96443"},
    {"type":"color","id":"card_bg","label":"Feature card background","default":"#F1E1D7"},
    {"type":"color","id":"eyebrow_color","label":"Eyebrow text color","default":"#b4533b"},
    {"type":"color","id":"button_bg","label":"View Nutrition button background","default":"#c3583e"},
    {"type":"color","id":"button_text","label":"View Nutrition button text","default":"#ffffff"},
    {"type":"color","id":"checkmark_color","label":"Feature cards checkmark color","default":"#b4533b"}
  ],
  "blocks": [
    {"type":"image","name":"Main image","limit":1,"settings":[{"type":"image_picker","id":"image","label":"Image"}]},
    {"type":"eyebrow","name":"Eyebrow","limit":1,"settings":[{"type":"richtext","id":"text","label":"Text","default":"<p>ENERGY | FOCUS | GUT HEALTH | IMMUNITY<\/p>"},{"type":"select","id":"align","label":"Text alignment","default":"left","options":[{"value":"left","label":"Left"},{"value":"center","label":"Center"},{"value":"right","label":"Right"},{"value":"justify","label":"Justify"}]}]},
    {"type":"heading","name":"Heading","limit":1,"settings":[{"type":"text","id":"line1","label":"Line 1","default":"Covering All Aspects"},{"type":"text","id":"line2","label":"Line 2 (accent)","default":"of Health"},{"type":"checkbox","id":"bold","label":"Bold heading","default":true},{"type":"select","id":"align","label":"Text alignment","default":"left","options":[{"value":"left","label":"Left"},{"value":"center","label":"Center"},{"value":"right","label":"Right"},{"value":"justify","label":"Justify"}]}]},
    {"type":"text","name":"Body text","limit":1,"settings":[{"type":"richtext","id":"body","label":"Text","default":"<p>All-day energy & focus, no jitters or crash. Enjoy enhanced well-being every day.<\/p>"},{"type":"select","id":"align","label":"Text alignment","default":"left","options":[{"value":"left","label":"Left"},{"value":"center","label":"Center"},{"value":"right","label":"Right"},{"value":"justify","label":"Justify"}]}]},
    {"type":"feature-cards","name":"Feature cards","limit":1,"settings":[
      {"type":"text","id":"title1","label":"Left card title","default":"FLAVOR"},
      {"type":"richtext","id":"bullet1a","label":"Left bullet 1","default":"<p>Smooth &amp; creamy like your favorite latte<\/p>"},
      {"type":"richtext","id":"bullet1b","label":"Left bullet 2","default":"<p>Nutty aroma &amp; robust flavor<\/p>"},
      {"type":"richtext","id":"bullet1c","label":"Left bullet 3"},
      {"type":"richtext","id":"bullet1d","label":"Left bullet 4"},
      {"type":"richtext","id":"bullet1e","label":"Left bullet 5"},
      {"type":"text","id":"title2","label":"Right card title","default":"FEELING"},
      {"type":"richtext","id":"bullet2a","label":"Right bullet 1","default":"<p>You'll get jitter-free energy that boosts gut health, immunity, and your mood.<\/p>"},
      {"type":"richtext","id":"bullet2b","label":"Right bullet 2"},
      {"type":"richtext","id":"bullet2c","label":"Right bullet 3"},
      {"type":"richtext","id":"bullet2d","label":"Right bullet 4"},
      {"type":"richtext","id":"bullet2e","label":"Right bullet 5"},
      {"type":"select","id":"align","label":"Text alignment","default":"left","options":[{"value":"left","label":"Left"},{"value":"center","label":"Center"},{"value":"right","label":"Right"},{"value":"justify","label":"Justify"}]}
    ]},
    {"type":"icons-grid","name":"Icons grid","limit":1,"settings":[
      {"type":"image_picker","id":"icon1","label":"Icon 1"},{"type":"text","id":"label1","label":"Label 1","default":"48 MG"},{"type":"text","id":"sub1","label":"Sub 1","default":"CAFFEINE"},
      {"type":"image_picker","id":"icon2","label":"Icon 2"},{"type":"text","id":"label2","label":"Label 2","default":"GLUTEN"},{"type":"text","id":"sub2","label":"Sub 2","default":"FREE"},
      {"type":"image_picker","id":"icon3","label":"Icon 3"},{"type":"text","id":"label3","label":"Label 3","default":"100%"},{"type":"text","id":"sub3","label":"Sub 3","default":"VEGAN"},
      {"type":"image_picker","id":"icon4","label":"Icon 4"},{"type":"text","id":"label4","label":"Label 4","default":"NO"},{"type":"text","id":"sub4","label":"Sub 4","default":"SUGAR"},
      {"type":"image_picker","id":"icon5","label":"Icon 5"},{"type":"text","id":"label5","label":"Label 5","default":"NON"},{"type":"text","id":"sub5","label":"Sub 5","default":"GMO"},
      {"type":"image_picker","id":"icon6","label":"Icon 6"},{"type":"text","id":"label6","label":"Label 6"},{"type":"text","id":"sub6","label":"Sub 6"},
      {"type":"image_picker","id":"icon7","label":"Icon 7"},{"type":"text","id":"label7","label":"Label 7"},{"type":"text","id":"sub7","label":"Sub 7"},
      {"type":"image_picker","id":"icon8","label":"Icon 8"},{"type":"text","id":"label8","label":"Label 8"},{"type":"text","id":"sub8","label":"Sub 8"},
      {"type":"image_picker","id":"icon9","label":"Icon 9"},{"type":"text","id":"label9","label":"Label 9"},{"type":"text","id":"sub9","label":"Sub 9"},
      {"type":"image_picker","id":"icon10","label":"Icon 10"},{"type":"text","id":"label10","label":"Label 10"},{"type":"text","id":"sub10","label":"Sub 10"}
    ]},
    {"type":"nutrition","name":"Nutrition modal button","limit":1,"settings":[{"type":"text","id":"label","label":"Button label","default":"VIEW NUTRITION"},{"type":"image_picker","id":"image","label":"Modal image"}]},
    {"type":"ingredients","name":"Ingredients","limit":1,"settings":[{"type":"richtext","id":"text","label":"Text","default":"<p><strong>Ingredients:</strong> Organic Mushroom Blend (Cordyceps, Lion's Mane, Reishi, Shiitake, Turkey Tail, King Trumpet), Arabica Coffee, MCT Oil, Coconut Milk.<\/p>"},{"type":"select","id":"align","label":"Text alignment","default":"left","options":[{"value":"left","label":"Left"},{"value":"center","label":"Center"},{"value":"right","label":"Right"},{"value":"justify","label":"Justify"}]}]}
  ],
  "presets": [
    {"name": "Custom Aspects", "blocks": [
      {"type":"eyebrow"},
      {"type":"heading"},
      {"type":"text"},
      {"type":"image"},
      {"type":"feature-cards"},
      {"type":"icons-grid"},
      {"type":"nutrition"},
      {"type":"ingredients"}
    ]}
  ]
}
{% endschema %}

